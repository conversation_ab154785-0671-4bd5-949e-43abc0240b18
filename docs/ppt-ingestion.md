# PPT/PDF Ingestion Service

The PPT Ingestion Service provides powerful document processing capabilities for PowerPoint (PPTX) and PDF files. It extracts text via OCR using PyMuPDF and enhances the extraction using GPT-4 Vision to handle complex tables, charts, graphs, and other visual elements.

## Features

- **Multi-format Support**: Process both PPTX and PDF files
- **OCR Text Extraction**: Extract text from slides/pages using PyMuPDF
- **Vision-Enhanced Processing**: Use GPT-4 Vision to analyze slide images for complete text representation
- **High-Quality Images**: Export slides as high-resolution PNG images
- **Multiple Output Formats**: Export to plain text or JSON
- **RESTful API**: FastAPI endpoints for integration with other services

## Installation

Install the required dependencies:

```bash
pip install -r requirements.txt
```

**Note**: For OCR support, you may also need to install Tesseract OCR on your system:

- **macOS**: `brew install tesseract`
- **Ubuntu**: `sudo apt-get install tesseract-ocr`
- **Windows**: Download from [GitHub Tesseract releases](https://github.com/UB-Mannheim/tesseract/wiki)

## Configuration

Set your OpenAI API key in your environment:

```bash
export OPENAI_API_KEY="your-api-key-here"
```

Or create a `.env` file in the project root:

```
OPENAI_API_KEY=your-api-key-here
```

## Usage

### Command Line Interface

The easiest way to process presentations is using the CLI script:

```bash
# Process a PowerPoint presentation
python scripts/process_presentation.py presentation.pptx

# Process a PDF
python scripts/process_presentation.py document.pdf

# Specify output file
python scripts/process_presentation.py presentation.pptx -o output.txt

# Export as JSON
python scripts/process_presentation.py presentation.pptx -o output.json -f json

# Disable GPT-4 Vision enhancement (faster, OCR only)
python scripts/process_presentation.py presentation.pptx --no-vision

# Use a custom vision prompt
python scripts/process_presentation.py presentation.pptx --vision-prompt "Focus on extracting numerical data from tables"
```

### Python API

Use the service directly in your Python code:

```python
from src.backend.services.ppt_ingestion import get_ppt_ingestion_service

# Initialize the service
service = get_ppt_ingestion_service()

# Process a file
slides = service.process_file(
    file_path="presentation.pptx",
    use_vision=True
)

# Access slide data
for slide in slides:
    print(f"Slide {slide.page_number}")
    print(f"OCR Text: {slide.ocr_text}")
    print(f"Enhanced Text: {slide.enhanced_text}")
    print()

# Export to text file
service.export_to_text(slides, "output.txt")

# Export to JSON
service.export_to_json(slides, "output.json")
```

### REST API

The service is available via FastAPI endpoints when the backend is running:

#### Start the Backend

```bash
./start.sh
```

Or manually:

```bash
cd src/backend
python main.py
```

#### API Endpoints

**Process a presentation and get structured JSON response:**

```bash
curl -X POST "http://localhost:8000/ppt-ingestion/process" \
  -F "file=@presentation.pptx" \
  -F "use_vision=true"
```

**Process a presentation and get plain text:**

```bash
curl -X POST "http://localhost:8000/ppt-ingestion/process-to-text" \
  -F "file=@presentation.pptx" \
  -F "use_vision=true"
```

**Health check:**

```bash
curl http://localhost:8000/ppt-ingestion/health
```

#### Response Format (JSON endpoint)

```json
{
  "total_slides": 10,
  "slides": [
    {
      "page_number": 1,
      "ocr_text": "Welcome to Our Company\nQ3 2024 Results",
      "enhanced_text": "Welcome to Our Company\n\nQ3 2024 Results\n\nThis slide shows a title page with the company logo...",
      "has_image": true,
      "image_format": "png"
    }
  ],
  "filename": "presentation.pptx",
  "file_type": "pptx"
}
```

## How It Works

### 1. File Processing

The service accepts PPTX or PDF files and processes them differently based on the format:

- **PDF Files**: Directly processed using PyMuPDF
- **PPTX Files**: Converted to PDF internally, then processed using PyMuPDF

### 2. Text Extraction

For each slide/page:
1. Extract text using PyMuPDF's OCR capabilities
2. Convert the page to a high-resolution PNG image (300 DPI equivalent)

### 3. Vision Enhancement (Optional)

If vision enhancement is enabled:
1. Send both the OCR text and the slide image to GPT-4 Vision
2. GPT-4 Vision analyzes the image to:
   - Correct OCR errors
   - Describe complex tables, charts, and graphs
   - Extract information from visual elements
   - Understand the structure and layout

### 4. Output

The processed slides can be exported as:
- **Plain Text**: Each slide's text with clear separators
- **JSON**: Structured data with metadata

## Advanced Features

### Custom Vision Prompts

Customize how GPT-4 Vision analyzes your slides:

```python
custom_prompt = """
You are analyzing a financial presentation slide.
Focus on:
1. Extracting all numerical data accurately
2. Describing charts and graphs in detail
3. Identifying trends and patterns

OCR Text:
{ocr_text}

Provide a detailed text representation.
"""

slides = service.process_file(
    file_path="financial_report.pptx",
    use_vision=True,
    vision_prompt=custom_prompt
)
```

### Processing Large Files

For large presentations, you may want to process slides individually to manage memory:

```python
service = get_ppt_ingestion_service()

# Process without vision first (faster)
slides = service.process_file("large_presentation.pptx", use_vision=False)

# Then enhance specific slides with vision
for slide in slides[:5]:  # Only first 5 slides
    service._enhance_with_vision([slide])
```

### Batch Processing

Process multiple files:

```python
import glob
from pathlib import Path

service = get_ppt_ingestion_service()

# Process all presentations in a directory
for file_path in glob.glob("presentations/*.pptx"):
    print(f"Processing {file_path}...")
    slides = service.process_file(file_path)
    
    output_name = Path(file_path).stem + "_processed.txt"
    service.export_to_text(slides, output_name)
```

## Performance Considerations

- **OCR Only**: Fast, suitable for text-heavy slides without complex visuals
- **With Vision**: Slower but more accurate, especially for:
  - Complex tables
  - Charts and graphs
  - Diagrams
  - Mixed text and visual content

Typical processing times (per slide):
- OCR only: 0.5-1 second
- With GPT-4 Vision: 3-5 seconds

## Troubleshooting

### "OpenAI API key is required"

Ensure your API key is set:
```bash
export OPENAI_API_KEY="your-key"
```

### "Unsupported file format"

The service only supports `.pptx` and `.pdf` files. Convert other formats first.

### Poor OCR Quality

If OCR text quality is poor:
1. Ensure the source document has good resolution
2. Enable vision enhancement for better results
3. Consider using higher-quality source files

### Memory Issues

For very large files:
- Process slides in batches
- Disable vision enhancement for initial processing
- Use the `--no-vision` flag in CLI

## Integration Examples

### As a Workflow Delegate

The service can be integrated as a delegate in agentic-core workflows:

```python
# In a workflow step
{
  "type": "delegate",
  "delegate_name": "ppt_processor",
  "input": {
    "file_path": "${previous_step.output.file_path}",
    "use_vision": true
  }
}
```

### With Other Services

Combine with other services for powerful workflows:

```python
# Process presentation, then analyze with LLM
service = get_ppt_ingestion_service()
slides = service.process_file("strategy.pptx")

# Extract all text
full_text = "\n\n".join(
    slide.enhanced_text or slide.ocr_text 
    for slide in slides
)

# Send to LLM for analysis
from openai import OpenAI
client = OpenAI()
response = client.chat.completions.create(
    model="gpt-4",
    messages=[{
        "role": "user",
        "content": f"Summarize this presentation:\n\n{full_text}"
    }]
)
```

## API Reference

### `PPTIngestionService`

Main service class for processing presentations.

**Methods:**

- `process_file(file_path, use_vision=True, vision_prompt=None)`: Process a file
- `export_to_text(slides, output_path, use_enhanced=True)`: Export to text file
- `export_to_json(slides, output_path)`: Export to JSON file

### `SlideData`

Data class representing a single slide/page.

**Attributes:**

- `page_number` (int): The slide/page number (1-indexed)
- `ocr_text` (str): Text extracted via OCR
- `enhanced_text` (str | None): Enhanced text from GPT-4 Vision
- `image_data` (bytes): PNG image data
- `image_format` (str): Image format (always "png")

## Future Enhancements

Planned features:
- Support for more file formats (ODP, Google Slides)
- Image extraction and saving
- Table extraction to CSV/Excel
- Multi-language OCR support
- Streaming API for real-time processing
- Caching for faster re-processing

