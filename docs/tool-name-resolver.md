# Tool Name Resolver

## Overview

The Tool Name Resolver is an Axon-PFC service that automatically resolves human-readable tool names to their corresponding UUIDs before sending workflows to agentic-core. This allows you to write workflows using intuitive tool names instead of hard-to-remember UUIDs.

## Why This Exists

**Problem:** Agentic-core's database requires tool UUIDs for lookups, making workflows hard to read and maintain:

```json
{
  "tools": [
    "a5616fdc-f539-47ce-a415-fcb10ca355ef",
    "69a2e114-5e07-4d71-981b-bef5b6e26e4c"
  ]
}
```

**Solution:** Write workflows with tool names, and Axon-PFC automatically converts them:

```json
{
  "tools": ["save_output", "save_variable"]
}
```

## How It Works

### 1. Automatic Resolution

When you create a workflow via the Axon-PFC API, the tool resolver:

1. **Detects** all tool names in the workflow JSON
2. **Queries** the `agentic_objects.tool` table to find matching UUIDs
3. **Replaces** tool names with UUIDs
4. **Sends** the transformed JSON to agentic-core

### 2. Database Query

The resolver uses this SQL query to find tool UUIDs:

```sql
SELECT 
    t.name,
    tv.id as version_id
FROM agentic_objects.tool t
JOIN agentic_objects.tool_version tv ON t.id = tv.tool_id
WHERE t.name = ANY(:tool_names)
AND tv.is_draft = false
ORDER BY tv.modified_date DESC
```

**Note:** If multiple versions of a tool exist, the resolver uses the **most recent non-draft version**.

### 3. Caching

The resolver caches tool name → UUID mappings in memory for performance. The cache is cleared when:
- The resolver instance is recreated
- You explicitly call `resolver.clear_cache()`

## Usage Examples

### Example 1: Team with Tools

**Before (with UUIDs):**
```json
{
  "agents": [
    {
      "name": "researcher",
      "tools": [
        "32d5a1fc-df40-425e-8356-cc87092dbdb4",
        "a5616fdc-f539-47ce-a415-fcb10ca355ef"
      ]
    }
  ]
}
```

**After (with names):**
```json
{
  "agents": [
    {
      "name": "researcher",
      "tools": ["OpenAI_Websearch", "save_output"]
    }
  ]
}
```

### Example 2: Multiple Agents

```json
{
  "steps": [
    {
      "type": "team",
      "team": {
        "agents": [
          {
            "name": "coordinator",
            "tools": ["save_output", "save_variable", "read_input"]
          },
          {
            "name": "researcher",
            "tools": ["OpenAI_Websearch", "save_output"]
          },
          {
            "name": "doc_creator",
            "tools": ["save_output", "docx_conversion"]
          }
        ]
      }
    }
  ]
}
```

All tool names will be automatically resolved to UUIDs when the workflow is created.

## Common Tool Names

Here are the standard tools available in agentic-core:

| Tool Name | Description | UUID |
|-----------|-------------|------|
| `read_input` | Read input variables | `6c360598-ca60-4211-aa16-8d45a0e5c8ce` |
| `save_output` | Save output to file | `a5616fdc-f539-47ce-a415-fcb10ca355ef` |
| `save_variable` | Save workflow variable | `69a2e114-5e07-4d71-981b-bef5b6e26e4c` |
| `OpenAI_Websearch` | Web search via OpenAI | `32d5a1fc-df40-425e-8356-cc87092dbdb4` |
| `docx_conversion` | Convert Markdown to DOCX | `c4cee33d-8904-42b9-9641-391e61d8a84d` |

**Note:** This list is for reference. The resolver will automatically find any tools you create in agentic-core.

## API Integration

The tool resolver is automatically invoked when you create a workflow via the API:

```bash
curl -X POST http://localhost:8000/api/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Workflow",
    "workflow": {
      "steps": [
        {
          "type": "team",
          "team": {
            "agents": [
              {
                "name": "agent1",
                "tools": ["save_output", "save_variable"]
              }
            ]
          }
        }
      ]
    }
  }'
```

The response will include the workflow with resolved UUIDs:

```json
{
  "id": "...",
  "workflow": {
    "steps": [
      {
        "type": "team",
        "team": {
          "agents": [
            {
              "name": "agent1",
              "tools": [
                "a5616fdc-f539-47ce-a415-fcb10ca355ef",
                "69a2e114-5e07-4d71-981b-bef5b6e26e4c"
              ]
            }
          ]
        }
      }
    ]
  }
}
```

## Error Handling

### Missing Tool

If a tool name cannot be resolved, you'll get an error:

```json
{
  "detail": "Tool resolution failed: Could not resolve tool names: unknown_tool"
}
```

**Solution:** Check the tool name spelling or verify the tool exists in agentic-core.

### Database Connection Error

If the resolver can't connect to the database:

```json
{
  "detail": "Tool resolution failed: connection to server ... failed"
}
```

**Solution:** Check that the `DATABASE_URL` environment variable is set correctly.

## Adding New Tools

When you create new tools in agentic-core, they're **automatically available** to the resolver:

1. Create a tool in agentic-core (via API or UI)
2. Use the tool name in your workflow JSON
3. Axon-PFC will automatically resolve it ✅

**No manual configuration needed!**

## Technical Details

### Architecture

```
┌─────────────┐
│  Frontend   │
│  (Submit    │
│  Workflow)  │
└──────┬──────┘
       │
       ▼
┌─────────────────────────────────────────────┐
│  Axon-PFC Backend                           │
│  ┌──────────────────────────────────────┐   │
│  │  POST /api/workflows                 │   │
│  │  ├─► ToolResolver.resolve_workflow() │   │
│  │  │   └─► Query DB for tool UUIDs    │   │
│  │  └─► json_to_flow()                  │   │
│  │      └─► Create Flow in DB           │   │
│  └──────────────────────────────────────┘   │
└─────────────────────────────────────────────┘
       │
       ▼
┌─────────────┐
│  Agentic-   │
│  Core       │
│  (Executes  │
│  Workflow)  │
└─────────────┘
```

### Files

- **Service:** `src/backend/services/tool_resolver.py`
- **Integration:** `src/backend/workflow_json_serializer.py` (in `json_to_flow()`)
- **Documentation:** `docs/tool-name-resolver.md` (this file)

### Performance

- **Caching:** Tool mappings are cached in memory after first lookup
- **Batch Query:** All tool names are resolved in a single database query
- **Negligible Overhead:** Resolution adds < 10ms to workflow creation

## Future Enhancements

Possible improvements:

1. **Agent Name Resolution:** Similar to tools, resolve agent names/roles to UUIDs
2. **Validation:** Warn if tool names are ambiguous or deprecated
3. **Suggestions:** Provide "did you mean?" suggestions for misspelled tool names
4. **Version Pinning:** Allow specifying tool versions (e.g., `save_output@v2`)

## Troubleshooting

### Issue: Tool names not resolving

**Symptoms:** Getting UUID format errors after implementing tool name resolver

**Causes:**
1. Tool doesn't exist in database
2. Tool is marked as draft
3. Database connection issue

**Solutions:**
1. Verify tool exists: `SELECT * FROM agentic_objects.tool WHERE name = 'your_tool_name'`
2. Check draft status: `SELECT is_draft FROM agentic_objects.tool_version WHERE ...`
3. Test database connection: `psql $DATABASE_URL`

### Issue: Cache not updating

**Symptoms:** New tools not being found, even though they exist

**Solution:** The resolver cache persists for the lifetime of the Python process. Restart the Axon-PFC backend to clear the cache.

## Summary

✅ **Write workflows with tool names, not UUIDs**
✅ **Automatic resolution happens transparently**
✅ **Works with any tools you create in agentic-core**
✅ **No manual configuration needed**

The Tool Name Resolver makes your workflows more readable, maintainable, and intuitive!

