# Auto-Resume Service

## ⚠️ DEPRECATION NOTICE

**This service is NO LONGER NEEDED.** As of the GraphQL Log Writer update, logs are captured for ALL executions regardless of the `debugging_enabled` flag. Eval runs can now use `debugging_enabled=False` and will not pause, eliminating the need for this service.

## Overview

The Auto-Resume Service was a background service that automatically resumed paused eval test executions. It solved the conflict between needing detailed execution logs (`debugging_enabled=True`) and requiring automated test execution.

## The Problem

When `debugging_enabled=True` in agentic-core:
- ✅ **Benefit:** Detailed logs are captured via GraphQL event subscriptions
- ❌ **Issue:** Execution automatically **pauses** for manual/interactive debugging
- ❌ **Result:** Automated eval tests get stuck and don't complete

When `debugging_enabled=False`:
- ✅ **Benefit:** Execution runs automatically without pausing
- ❌ **Issue:** No detailed logs are captured
- ❌ **Result:** Can't debug failures or review execution details

## The Solution

The Auto-Resume Service bridges this gap by:

1. **Polling** the database every 2 seconds for PAU SED flow executions
2. **Filtering** to only resume executions associated with eval runs
3. **Auto-resuming** by updating:
   - `flow_execution.result`: `PAUSED` → `RUNNING`
   - `flow_execution_step.result`: `paused` → `pending`
4. **Tracking** resumed executions to avoid duplicate processing

## Result

With the Auto-Resume Service running, eval tests get **both**:
- ✅ **Detailed execution logs** (from `debugging_enabled=True`)
- ✅ **Automatic execution** (via auto-resume)
- ✅ **Pass/Fail results** (execution completes normally)
- ✅ **Dashboard visibility** (shows in "Active Runs")

## Architecture

```
User clicks "Run Test" 
   ↓
Backend: EvalRunner creates flow_execution with debugging_enabled=True
   ↓
Worker: Starts execution, detects debugging=True, sets status to PAUSED
   ↓
Auto-Resume Service: (2s poll interval)
   - Detects PAUSED execution
   - Verifies it's associated with an eval_run
   - Updates status to RUNNING
   - Updates steps from paused to pending
   ↓
Worker: Resumes normal execution
   ↓
Execution completes → Pass/Fail result
```

## Implementation

**File:** `src/backend/services/auto_resume.py`

**Key Functions:**
- `find_paused_eval_executions()` - Queries for PAUSED executions with eval runs
- `resume_execution()` - Updates database to resume execution
- `run()` - Main polling loop (every 2 seconds)

**Database Query:**
```sql
SELECT 
    fe.id as execution_id,
    er.id as eval_run_id,
    ec.name as case_name
FROM flow_execution.flow_execution fe
JOIN evaluation.eval_run er ON er.flow_execution_id = fe.id
JOIN evaluation.eval_case ec ON er.eval_case_id = ec.id
WHERE fe.result = 'PAUSED'
AND fe.end_time IS NULL
AND fe.debugging_enabled = true
```

## Configuration

**Environment Variables:**
- `DATABASE_URL` - PostgreSQL connection string (required)
- Poll interval: 2 seconds (hardcoded, can be modified in code)

**Startup:** Automatically started by `./start.sh`
**Shutdown:** Automatically stopped by `./stop.sh`

## Monitoring

**View logs:**
```bash
tail -f auto_resume.log
```

**Example log output:**
```
2025-10-21 12:35:02 - INFO - 🚀 Auto-Resume Service Started
2025-10-21 12:35:02 - INFO - ⏰ Polling every 2 seconds
2025-10-21 12:35:05 - INFO - Found 1 paused eval execution(s)
2025-10-21 12:35:05 - INFO - ✅ Auto-resumed execution 5c61e5f5-358b-4e53-a381-109a721777d4
2025-10-21 12:35:05 - INFO -    Test case: test
2025-10-21 12:35:05 - INFO -    Eval run: 73caf7cc-61af-4326-8732-a8d1d26a9309
2025-10-21 12:35:05 - INFO -    Updated 1 step(s) from paused → pending
```

## Status

**Check if running:**
```bash
ps aux | grep auto_resume | grep -v grep
```

**PID file location:** `.auto_resume.pid`

## Limitations

1. **Resume lag:** 0-2 second delay between pause and resume (poll interval)
2. **Database-only:** Doesn't publish RabbitMQ messages (relies on worker polling)
3. **Eval-only:** Only resumes executions associated with eval_run records
4. **No retroactive:** Doesn't resume executions that were paused before service started

## Trade-offs

**Why not modify agentic-core directly?**
- Agentic-core is a separate repository/codebase
- This solution keeps changes contained to Axon-PFC
- Easier to maintain and doesn't require upstream changes

**Why polling vs event-driven?**
- Simpler implementation (no WebSocket/GraphQL subscriptions needed)
- 2-second latency is acceptable for automated tests
- More reliable than event subscriptions for this use case

## Future Improvements

1. **Configurable poll interval** via environment variable
2. **Metrics/statistics** on resume counts and timing
3. **Web UI indicator** showing auto-resume is active
4. **Health check endpoint** for monitoring
5. **Selective resume** based on workflow/tenant/user filters

