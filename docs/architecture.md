# Axon-PFC Architecture

Complete system architecture and design documentation.

---

## 🏗️ System Overview

Axon-PFC is a workflow execution and evaluation platform built on top of **agentic-core**. It provides:

1. **Workflow Management** - Create, edit, and manage multi-agent workflows
2. **Test Case System** - Define expected inputs/outputs for workflows
3. **Execution Engine** - Run workflows via agentic-core
4. **Validation System** - Automatically validate workflow outputs
5. **Monitoring Dashboard** - Real-time system status and execution tracking

---

## 📊 Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                         Frontend                             │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐      │
│  │  Workflows   │  │ Test Cases   │  │  Dashboard   │      │
│  │     Tab      │  │     Tab      │  │     Page     │      │
│  └──────┬───────┘  └──────┬───────┘  └──────┬───────┘      │
│         │                  │                  │              │
│         └──────────────────┼──────────────────┘              │
│                            │ HTTP/REST API                   │
└────────────────────────────┼─────────────────────────────────┘
                             │
┌────────────────────────────┼─────────────────────────────────┐
│                      Backend (FastAPI)                       │
│                            │                                 │
│  ┌─────────────────────────┼──────────────────────────┐     │
│  │            API Routes                               │     │
│  │  ┌────────────┐  ┌────────────┐  ┌────────────┐   │     │
│  │  │ Workflows  │  │ Evaluation │  │ Dashboard  │   │     │
│  │  │   Routes   │  │   Routes   │  │   Routes   │   │     │
│  │  └─────┬──────┘  └─────┬──────┘  └─────┬──────┘   │     │
│  └────────┼───────────────┼───────────────┼──────────┘     │
│           │               │               │                 │
│  ┌────────┼───────────────┼───────────────┼──────────┐     │
│  │                Services                            │     │
│  │  ┌─────┴─────────┐  ┌─┴──────────┐  ┌─┴─────────┐ │     │
│  │  │  EvalRunner   │  │ EvalMonitor│  │  RabbitMQ │ │     │
│  │  │   (on-demand) │  │ (daemon)   │  │  Monitor  │ │     │
│  │  └───────┬───────┘  └─────┬──────┘  └───────────┘ │     │
│  └──────────┼──────────────────┼───────────────────────┘     │
│             │                  │                             │
└─────────────┼──────────────────┼─────────────────────────────┘
              │                  │
              │    ┌─────────────┘
              │    │
┌─────────────┼────┼─────────────────────────────────────────┐
│                PostgreSQL (Agentic-Core)                    │
│                                                              │
│  ┌──────────────────┐  ┌──────────────────┐                │
│  │  flow_execution  │  │   evaluation     │                │
│  │     schema       │  │     schema       │                │
│  │                  │  │                  │                │
│  │ • flow          │  │ • eval_case      │                │
│  │ • flow_version  │  │ • eval_run       │                │
│  │ • flow_execution│←─┼─• eval_suite     │                │
│  │                  │  │ • eval_batch_run │                │
│  └──────────────────┘  └──────────────────┘                │
└─────────────────────────────────────────────────────────────┘
              │
              │ Publishes
              ↓
┌──────────────────────────────────────────────────────────────┐
│                    RabbitMQ (Agentic-Core)                   │
│                                                               │
│  Queue: agentic.async        Queue: agentic.flyweight        │
│  ┌──────────────────┐        ┌──────────────────┐           │
│  │  Flow Execution  │        │  Flow Execution  │           │
│  │    Messages      │        │    Messages      │           │
│  └──────────────────┘        └──────────────────┘           │
└───────────┬──────────────────────────┬───────────────────────┘
            │                          │
            │ Consumes                 │ Consumes
            ↓                          ↓
┌─────────────────────┐    ┌─────────────────────┐
│  Async Worker       │    │  Flyweight Worker   │
│  (Docker Container) │    │  (Docker Container) │
│                     │    │                     │
│  • LLM Calls        │    │  • Fast Execution   │
│  • Agent Teams      │    │  • Simple Tasks     │
│  • OpenAI API       │    │                     │
└─────────────────────┘    └─────────────────────┘
```

---

## 🔧 Component Details

### Frontend (React)

**Location:** `src/frontend/`

**Technology:** Vanilla React (via CDN), no build process

**Components:**
- `WorkflowList` - Display all workflows
- `WorkflowDetail` - View/edit workflow definition
- `EvalCasesTab` - Manage test cases for a workflow
- `DashboardPage` - Real-time monitoring and system health
- `CreateEvalCaseModal` - Form for creating test cases

**State Management:**
- React hooks (`useState`, `useEffect`)
- No Redux/context (kept simple)

**API Communication:**
- Fetch API for HTTP requests
- 2-second polling interval for dashboard

---

### Backend (FastAPI)

**Location:** `src/backend/`

**Technology:** Python 3.13+, FastAPI, SQLAlchemy

#### Routes

##### 1. Workflow Routes (`main.py`)
```python
GET  /api/workflows              # List all workflows
POST /api/workflows              # Create workflow
GET  /api/workflows/{id}         # Get workflow detail
PUT  /api/workflows/{id}         # Update workflow
GET  /api/workflows/{id}/versions # Get workflow versions
```

##### 2. Evaluation Routes (`routes/evaluation.py`)
```python
GET  /api/eval-cases                     # List test cases
POST /api/eval-cases                     # Create test case
GET  /api/eval-cases/{id}                # Get test case
PUT  /api/eval-cases/{id}                # Update test case
POST /api/eval-cases/{id}/run            # Execute test case
POST /api/eval-runs/{id}/cancel          # Cancel running execution
```

##### 3. Dashboard Routes (`routes/dashboard.py`)
```python
GET  /api/dashboard/status               # System status + metrics
GET  /api/recent-completions             # Recent executions
```

#### Services

##### 1. EvalRunner (`services/eval_runner.py`)

**Purpose:** Execute test cases

**Flow:**
1. Receive test case ID
2. Load test case and workflow definition
3. Create `flow_execution` record in database
4. Create `eval_run` record (links to flow_execution)
5. Publish message to RabbitMQ
6. Return execution ID

**Key Methods:**
- `run_eval_case(eval_case_id, user_id, tenant_id)` → Creates and starts execution

##### 2. EvalMonitor (`services/eval_monitor.py`)

**Purpose:** Background daemon that validates completed executions

**Flow:**
1. Every 5 seconds, query for completed executions without validation
2. Load expected outputs from eval_case
3. Compare actual outputs with expected
4. Mark eval_run as passed/failed
5. Store validation results

**Validation Logic:**
```python
def validate_output(expected, actual):
    for key, expected_value in expected.items():
        if key not in actual:
            return False, f"Missing key: {key}"
        
        if expected_value == "*":
            # Wildcard - just check key exists
            continue
            
        if actual[key] != expected_value:
            return False, f"Mismatch: {key}"
    
    return True, "Validation passed"
```

**Runs as:** Background daemon (started by `start.sh`)

##### 3. RabbitMQMonitor (`services/rabbitmq_monitor.py`)

**Purpose:** Query RabbitMQ for queue statistics

**Flow:**
1. Connect to RabbitMQ
2. Declare queues in passive mode (doesn't create)
3. Read message counts and consumer counts
4. Return statistics

---

### Database Schema

#### Existing Schema (Agentic-Core)

**Schema:** `flow_execution`

```sql
-- Workflow definitions
flow (id, name, definition_json, ...)

-- Workflow versions
flow_version (id, flow_id, version_number, agents_json, ...)

-- Execution records
flow_execution (
    id,
    flow_id,
    flow_version_id,
    input_variables JSONB,
    output_variables JSONB,
    result VARCHAR,  -- PENDING, RUNNING, COMPLETE, FAILED, CANCELLED
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    ...
)
```

#### New Schema (Axon-PFC)

**Schema:** `evaluation`

```sql
-- Test case definitions
eval_case (
    id UUID PRIMARY KEY,
    workflow_id UUID,  -- References flow.id
    name VARCHAR,
    input_variables JSONB,
    expected_output JSONB,
    validation_rules JSONB,
    ...
)

-- Execution + validation results
eval_run (
    id UUID PRIMARY KEY,
    eval_case_id UUID,
    flow_execution_id UUID UNIQUE,  -- Links to flow_execution
    passed BOOLEAN,
    validation_output JSONB,
    evaluated_at TIMESTAMP
)

-- Test suites
eval_suite (
    id UUID PRIMARY KEY,
    workflow_id UUID,
    name VARCHAR,
    ...
)

eval_suite_cases (
    suite_id UUID,
    eval_case_id UUID,
    order_index INTEGER,
    PRIMARY KEY (suite_id, eval_case_id)
)

-- Batch runs
eval_batch_run (
    id UUID PRIMARY KEY,
    workflow_id UUID,
    suite_id UUID,
    started_at TIMESTAMP,
    status VARCHAR
)
```

**Key Views:**
- `eval_run_detail` - Joined view of eval_run + flow_execution
- `eval_case_stats` - Pass/fail statistics per test case
- `active_eval_runs` - Currently running evaluations
- `batch_run_progress` - Progress of batch executions

---

## 🔄 Execution Flow

### Creating and Running a Test Case

```
1. USER: Creates test case via UI
   ↓
2. FRONTEND: POST /api/eval-cases
   {
     workflow_id: "...",
     name: "Space Test",
     input_variables: {"topic": "space"},
     expected_output: {"greeting_message": "*"}
   }
   ↓
3. BACKEND: Saves to evaluation.eval_case
   ↓
4. USER: Clicks "Run Test"
   ↓
5. FRONTEND: POST /api/eval-cases/{id}/run
   ↓
6. EVALRUNNER:
   a. Create flow_execution record (PENDING)
   b. Create eval_run record (links to flow_execution)
   c. Publish message to RabbitMQ queue
   d. Return execution IDs
   ↓
7. RABBITMQ: Message queued
   ↓
8. WORKER: Picks up message
   a. Updates flow_execution to RUNNING
   b. Executes workflow (agents, LLM calls, etc.)
   c. Stores output_variables
   d. Updates flow_execution to COMPLETE/FAILED
   ↓
9. EVALMONITOR: (polling every 5s)
   a. Detects completed flow_execution
   b. Loads expected_output from eval_case
   c. Validates output_variables vs expected_output
   d. Updates eval_run: passed=true/false, validation_output
   ↓
10. DASHBOARD: (polling every 2s)
    - Moves execution from "Active Runs" to "Recent Completions"
    - Shows pass/fail badge
```

### Cancelling an Execution

```
1. USER: Clicks "Cancel" button on active run
   ↓
2. FRONTEND: Confirmation dialog
   ↓
3. FRONTEND: POST /api/eval-runs/{id}/cancel
   {
     flow_execution_id: "..."
   }
   ↓
4. BACKEND:
   a. Verify eval_run exists
   b. Check flow_execution is RUNNING or PENDING
   c. UPDATE flow_execution SET result='CANCELLED', end_time=NOW()
   d. Return success
   ↓
5. WORKER: Next iteration detects CANCELLED, stops processing
   ↓
6. EVALMONITOR: Detects CANCELLED execution, skips validation
   ↓
7. DASHBOARD: Removes from "Active Runs", shows in "Recent Completions" with CANCELLED badge
```

---

## 🗄️ Data Flow

### Workflow Definition Storage

```
User creates workflow
  ↓
Frontend transforms to JSON
  ↓
Backend: workflow_json_serializer.py
  ↓
  • Flattens nested structures
  • Converts variables to array format
  • Generates UUIDs for agents
  ↓
Database: flow + flow_version tables
```

### Workflow Execution Data

```
Test case run initiated
  ↓
Create flow_execution (input_variables from test case)
  ↓
Worker executes
  ↓
Agent uses save_variable tool
  ↓
output_variables stored in flow_execution
  ↓
EvalMonitor reads output_variables
  ↓
Validation results in eval_run
```

---

## 🔐 Security Considerations

### Database Access
- Connection string in `.env` (not committed)
- PostgreSQL authentication via username/password
- Network: localhost only (not exposed publicly)

### API Keys
- OpenAI API key in agentic-core `.env`
- Loaded by Docker containers at startup
- Not logged or exposed in API responses

### CORS
- Currently: No CORS restrictions (frontend/backend same domain)
- For production: Configure CORS middleware in FastAPI

### Input Validation
- Pydantic models validate all API inputs
- SQL injection: Prevented by SQLAlchemy parameterized queries
- UUID validation for all ID parameters

---

## 📈 Scalability Considerations

### Current Limitations

1. **Single Backend Instance**
   - No load balancing
   - Limited to ~100 concurrent requests

2. **Polling-Based Dashboard**
   - 2-second intervals
   - Not real-time
   - Could overwhelm with 100+ users

3. **Single EvalMonitor Daemon**
   - Validates sequentially
   - 5-second polling interval

### Scaling Strategies

#### Horizontal Scaling (Backend)
```
                  ┌→ Backend Instance 1
Load Balancer ────┼→ Backend Instance 2
                  └→ Backend Instance 3
                           ↓
                    PostgreSQL (shared)
```

#### WebSocket Dashboard
Replace polling with WebSocket:
```python
@app.websocket("/ws/dashboard")
async def dashboard_websocket(websocket: WebSocket):
    await websocket.accept()
    while True:
        status = get_dashboard_status()
        await websocket.send_json(status)
        await asyncio.sleep(1)
```

#### Multiple EvalMonitors
Use advisory locks to prevent conflicts:
```sql
SELECT pg_try_advisory_lock(eval_run.id)
-- Only one monitor processes each eval_run
```

#### Celery Tasks for Validation
Instead of polling daemon, use Celery task triggered on completion:
```python
@celery.task
def validate_eval_run(eval_run_id):
    # Validation logic
    pass

# Trigger from worker after execution completes
validate_eval_run.delay(eval_run_id)
```

---

## 🧪 Testing Strategy

### Unit Tests
- `tests/test_json_serializer.py` - Workflow transformation logic
- `tests/test_api.py` - API endpoint behavior (mocked DB)

### Integration Tests
- Full workflow creation → execution → validation flow
- Database migrations
- RabbitMQ message publishing

### End-to-End Tests
- Selenium/Playwright for UI testing
- Test complete user journeys

---

## 📦 Deployment Architecture

### Development (Current)

```
Developer Machine
├── Agentic-Core (Docker Compose)
│   ├── PostgreSQL (port 54321)
│   ├── RabbitMQ (port 56721)
│   ├── Async Worker
│   └── Flyweight Worker
└── Axon-PFC (Local Processes)
    ├── Backend (port 8000)
    ├── Frontend (port 3000)
    └── EvalMonitor (background)
```

### Production (Recommended)

```
AWS/GCP/Azure
├── RDS PostgreSQL (managed)
├── Amazon MQ / CloudAMQP (managed RabbitMQ)
├── ECS/EKS/GKE
│   ├── Backend (replicas: 3+)
│   ├── Frontend (nginx static serving)
│   ├── EvalMonitor (single instance)
│   └── Async Workers (replicas: 5+)
└── Application Load Balancer
```

---

## 🔧 Configuration Management

### Environment Variables

**Axon-PFC (.env):**
- `DATABASE_URL` - PostgreSQL connection string
- `RABBITMQ_URL` - RabbitMQ connection string
- `BACKEND_HOST`, `BACKEND_PORT` - Backend server config
- `FRONTEND_HOST`, `FRONTEND_PORT` - Frontend server config

**Agentic-Core (.env):**
- `OPENAI_API_KEY` - LLM API key
- Database/RabbitMQ configs (internal Docker networking)

### Secrets Management

**Development:** `.env` files (gitignored)
**Production:** Use AWS Secrets Manager, GCP Secret Manager, or HashiCorp Vault

---

## 📊 Monitoring and Observability

### Current Monitoring

1. **Dashboard** - Real-time system health
2. **Logs** - `backend.log`, `eval_monitor.log`, `frontend.log`
3. **RabbitMQ Management UI** - Queue stats (port 15672)

### Production Monitoring (Recommended)

1. **Application Metrics**
   - Prometheus + Grafana
   - Metrics: request rate, latency, error rate
   
2. **Log Aggregation**
   - ELK Stack (Elasticsearch, Logstash, Kibana)
   - CloudWatch Logs
   
3. **Distributed Tracing**
   - OpenTelemetry
   - Jaeger/Zipkin
   
4. **Alerts**
   - PagerDuty/Opsgenie
   - Slack notifications

---

## 🔄 CI/CD Pipeline (Recommended)

```
GitHub/GitLab
    ↓
  Push to main
    ↓
  GitHub Actions / GitLab CI
    ↓
  ┌───────────────────────────┐
  │  1. Run Tests             │
  │  2. Build Docker Images   │
  │  3. Run Migrations        │
  │  4. Deploy to Staging     │
  │  5. Smoke Tests           │
  │  6. Deploy to Production  │
  └───────────────────────────┘
```

---

**Last Updated:** 2025-10-15  
**Version:** 1.0

