# Workflow Parallel Execution Scripts

This directory contains scripts for creating and processing workflow runs in parallel using agentic-core.

## Quick Start

### 1. Prerequisites

Ensure all services are running:

```bash
# From the axon-pfc directory
./start.sh

# Also ensure agentic-core workers are running:
cd ../agentic-core
docker compose up -d async-engine-flyweight async-engine-async async-engine-cpu
```

### 2. Create the Test Workflow

```bash
python setup_test_workflow.py
```

This creates a "test" workflow from the example in `src/backend/example_team.json`.

### 3. Run Workflows in Parallel

```bash
python run_workflow_parallel.py
```

This will:
- Create 5 flow executions with different input topics
- Publish them to RabbitMQ
- Monitor their execution progress
- Show completion status

## Scripts Overview

### Main Scripts

1. **`run_workflow_parallel.py`** - Complete solution (recommended)
   - Creates flow executions in the database
   - Publishes run messages to RabbitMQ  
   - Monitors execution progress
   - Shows results

2. **`process_runs.py`** - Publisher only
   - Creates and publishes runs
   - No monitoring
   - Use when you just want to queue work

3. **`run_processor_worker.py`** - Simplified worker
   - Consumes messages from RabbitMQ
   - For testing/debugging only
   - Production should use actual async-engine-worker

4. **`setup_test_workflow.py`** - Setup helper
   - Creates the "test" workflow
   - One-time setup

### Documentation

- **`RUN_WORKFLOWS.md`** - Comprehensive documentation
  - Detailed architecture
  - Troubleshooting guide
  - Customization examples
  - Production considerations

- **`README_WORKFLOWS.md`** - This file
  - Quick start guide
  - Basic usage

## How It Works

```
┌──────────────────────┐
│ run_workflow_        │  1. Create FlowExecution records
│ parallel.py          │  2. Publish messages to RabbitMQ
└──────────┬───────────┘
           │
           ▼
    ┌──────────────┐
    │  RabbitMQ    │  3. Messages queued
    │  agentic.*   │
    └──────┬───────┘
           │
           ▼
    ┌──────────────┐
    │ async-engine │  4. Workers process messages
    │ workers      │     - Start flow
    └──────┬───────┘     - Execute teams
           │             - Update database
           ▼
    ┌──────────────┐
    │  PostgreSQL  │  5. Results stored
    └──────────────┘
```

## Concurrency Control

The scripts use Python's `asyncio.Semaphore` to limit concurrent operations:

```python
semaphore = asyncio.Semaphore(3)  # Max 3 at a time

async with semaphore:
    # Only 3 tasks can be here simultaneously
    await create_and_publish_run(...)
```

This prevents overwhelming RabbitMQ, the database, or the workers.

## Example Output

```
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Workflow Parallel Runner                                  ║
╚══════════════════════════════════════════════════════════════════════════════╝

Setting up Workflow Parallel Runner...
✓ Found workflow 'test'
  Version ID: 12345678-1234-1234-1234-123456789abc
✓ RabbitMQ connection established
✓ Messaging backend initialized
✓ Setup complete

Creating and Publishing Runs
Input sets: 5
Max concurrency: 3

[Run 1] Creating execution...
📤 Published run 11111111-1111-1111-1111-111111111111 with variables: {'topic': 'space exploration'}
[Run 2] Creating execution...
📤 Published run 22222222-2222-2222-2222-222222222222 with variables: {'topic': 'ocean adventure'}
...

✓ Published 5 runs successfully

Monitoring Run Execution
Monitoring 5 runs (timeout: 600s)

Status: 0 completed, 0 failed, 5 pending (5s elapsed)
✅ Run 11111111-1111-1111-1111-111111111111 completed
...
```

## Customization

### Change Workflow

Edit `run_workflow_parallel.py`:

```python
runner = WorkflowParallelRunner(
    workflow_name="your_workflow_name",  # Change this
    max_concurrent_runs=3
)
```

### Change Input Variables

Edit the `input_sets` list:

```python
input_sets = [
    {"your_variable": "value1"},
    {"your_variable": "value2"},
]
```

### Change Concurrency

```python
runner = WorkflowParallelRunner(
    workflow_name="test",
    max_concurrent_runs=5  # Process 5 at once
)
```

## Troubleshooting

### "Workflow 'test' not found"

Run: `python setup_test_workflow.py`

### "No tenant found in database"

Make sure agentic-core is initialized:

```bash
cd ../agentic-core
docker compose up -d graphql  # This runs migrations
```

### Runs stay pending

Check if workers are running:

```bash
cd ../agentic-core
docker compose ps | grep async-engine
docker compose logs async-engine-flyweight
```

### RabbitMQ connection failed

Check if RabbitMQ is running:

```bash
docker compose ps rabbitmq
```

Visit http://localhost:15672/ (guest/guest) to verify.

## Next Steps

1. ✅ Run `setup_test_workflow.py` to create the workflow
2. ✅ Run `run_workflow_parallel.py` to execute it
3. 📖 Read `RUN_WORKFLOWS.md` for detailed documentation
4. 🔧 Customize for your own workflows
5. 🚀 Integrate into your application

## Production Usage

⚠️ **These scripts are for demonstration and development.**

For production:
- Use the actual async-engine-worker from agentic-core
- Add proper error handling and retries
- Monitor worker health and queue depths
- Use connection pooling
- Add distributed tracing
- Set up alerting

See `RUN_WORKFLOWS.md` for details.

## Support

- Issues with scripts: Check this README and RUN_WORKFLOWS.md
- Issues with agentic-core: See agentic-core documentation
- Database issues: Check PostgreSQL logs
- RabbitMQ issues: Check management interface at http://localhost:15672/

Happy workflow orchestration! 🎉

