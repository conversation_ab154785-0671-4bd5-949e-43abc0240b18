# System User Permissions in Axon-PFC

## Overview

The GraphQL Log Writer service in Axon-PFC requires proper authentication and authorization to subscribe to flow execution event streams. This document explains how the permission system works and how to configure it.

## Architecture

### Authentication Flow

```
GraphQL Log Writer
    ↓ (Basic Auth: username=system, password=foo)
WebSocket Connection
    ↓ (Authorization header + connection_init)
GraphQL Server (agentic-core)
    ↓ (Validates credentials)
Database Lookup
    ↓ (system@internal → User ID)
Context Injection
    ↓ (User object in GraphQL context)
```

### Authorization Flow

```
GraphQL Subscription Request (flowExecutionStream)
    ↓
Extract User from Context
    ↓ (extract_user_from_auth_context)
Get Flow Execution
    ↓ (Determine tenant_id)
Check Permissions
    ↓ (validate_flow_execution_access_with_token)
Query Database
    ↓ (users.tenant_entitlements)
    │
    ├─ Has 'admin' entitlement? → ALLOW
    ├─ Has 'audit' entitlement? → ALLOW
    └─ No matching entitlement? → DENY (403 Forbidden)
```

## Database Schema

### System User

```sql
-- Located in users.user table
SELECT id, email, name, system_admin
FROM users.user
WHERE email = 'system@internal';

-- Result:
--   id: dcc189d6-9f2e-49f8-b789-da444df4f665
--   email: system@internal
--   name: System User
--   system_admin: false (doesn't bypass tenant restrictions)
```

### Tenant Entitlements

```sql
-- Located in users.tenant_entitlements table
CREATE TABLE users.tenant_entitlements (
    id UUID PRIMARY KEY,
    tenant_id UUID NOT NULL,
    user_id UUID NOT NULL,
    claims users.tenant_entitlement[],
    UNIQUE(tenant_id, user_id)
);

-- Entitlement enum values:
--   - admin: Full access (execute, audit, manage)
--   - audit: Read-only access (view executions, logs)
--   - execute: Can run workflows
--   - read_data: File download access
--   - write_data: File upload access
```

### Required Configuration

For the system user to subscribe to logs, it needs **per-tenant** entitlements:

```sql
INSERT INTO users.tenant_entitlements (id, user_id, tenant_id, claims)
VALUES (
  gen_random_uuid(),
  'dcc189d6-9f2e-49f8-b789-da444df4f665',  -- system user ID
  '40eaf721-236a-407e-b0f5-6e09b7cd39d8',  -- tenant ID
  ARRAY['admin', 'audit']::users.tenant_entitlement[]
);
```

## Setup Script

The `scripts/setup_system_user_entitlements.sh` script automates this configuration:

### What It Does

1. **Validates Prerequisites**
   - Checks that PostgreSQL container is running
   - Verifies system user exists in database

2. **Discovers Tenants**
   - Queries all tenants from `users.tenant` table
   - Reports count for transparency

3. **Grants Entitlements**
   - Inserts entitlements for each tenant
   - Uses `ON CONFLICT` to safely update existing records
   - Grants both `admin` and `audit` permissions

4. **Verifies Configuration**
   - Displays final entitlement table
   - Shows which tenants have access

### Usage

```bash
cd /Users/<USER>/projects/axon-pfc
./scripts/setup_system_user_entitlements.sh
```

### When to Run

- **Required:** After initial database migration
- **Optional:** After creating new tenants
- **Troubleshooting:** When GraphQL log writer shows "Forbidden" errors

## Security Considerations

### Tenant Isolation

The permission system enforces **strict tenant isolation**:
- System user doesn't have global access
- Must be explicitly granted access to each tenant
- Can't subscribe to executions in unauthorized tenants

### Entitlement Types

For log writer functionality:
- **audit**: Minimum required (read-only access to execution logs)
- **admin**: Optional (provides additional management capabilities)

### Best Practice

Grant **both** `admin` and `audit` to the system user:
- Allows future expansion of system service capabilities
- Doesn't increase attack surface (system user is internal)
- Simplifies troubleshooting

## Troubleshooting

### Error: "Forbidden" or 4403 WebSocket Close

**Symptom:**
```
⚠️  Connection closed for: <execution-id>
```

**Cause:** System user lacks entitlements for the execution's tenant

**Solution:**
```bash
./scripts/setup_system_user_entitlements.sh
```

### Error: "System user not found"

**Symptom:**
```
❌ Error: System user not found in database
   Expected email: system@internal
```

**Cause:** Agentic-core not properly initialized

**Solution:**
```bash
cd ../agentic-core
docker compose down
docker compose up -d
# Wait ~30 seconds for initialization
```

### Error: "No tenants found"

**Symptom:**
```
⚠️  Warning: No tenants found in database
```

**Cause:** Fresh database with no tenants created

**Solution:** This is normal for new installations. The script will succeed with 0 entitlements. Run it again after creating tenants.

## Environment Variables

### Required in .env

```bash
# System user credentials for GraphQL authentication
SYSTEM_USER=system
SYSTEM_PASSWORD=foo

# Note: These MUST match agentic-core's configuration
# Located in: agentic-core/backend/services/graphql/main.py
```

### Default Values

If not specified, the log writer uses hardcoded defaults:
- `SYSTEM_USER`: `"system"`
- `SYSTEM_PASSWORD`: `"foo"`

**Security Note:** In production, use a strong password and store in environment variables, not in code.

## Code References

### Axon-PFC

- **Log Writer**: `src/backend/services/graphql_log_writer.py:75-79`
  - Retrieves credentials from environment
  - Encodes as Base64 for Basic auth

### Agentic-Core

- **WebSocket Auth**: `backend/services/graphql/main.py:334-398`
  - `on_connect()` validates credentials
  - Decodes Basic auth header
  - Looks up user in `users_db` dict

- **Permission Check**: `backend/lib/graphql_bindings/flow_execution/flow_execution.py:1400-1456`
  - `resolve_flow_execution_stream()` validates subscription request
  - Calls `validate_flow_execution_access_with_token()`

- **Authorization Logic**: `backend/lib/authorization/authorization.py`
  - `extract_user_from_context()`: Gets user from context (lines 30-86)
  - `validate_flow_execution_access_with_token()`: Checks permissions (lines 599-641)
  - `get_user_entitlements()`: Queries database (lines 114-158)

## Summary

The system user permission model ensures:
1. ✅ **Proper authentication** via Basic auth credentials
2. ✅ **Tenant isolation** via per-tenant entitlements
3. ✅ **Fail-closed security** (deny by default)
4. ✅ **Audit trail** (all access tied to user_id)
5. ✅ **Easy maintenance** via automated setup script

By running the setup script after database migrations, you ensure the GraphQL log writer has the necessary permissions to subscribe to execution logs across all tenants.

