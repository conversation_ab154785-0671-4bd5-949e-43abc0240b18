# Axon-PFC Documentation

Complete documentation for the Axon-PFC workflow execution and evaluation platform.

---

## 📚 Documentation Index

### Getting Started
- **[../SETUP.md](../SETUP.md)** - Complete installation and setup guide
- **[quick-start.md](./quick-start.md)** - Quick reference for common tasks

### Core Guides
- **[workflow-guide.md](./workflow-guide.md)** - Creating and testing workflows
- **[evaluation-system.md](./evaluation-system.md)** - Test case system and validation
- **[architecture.md](./architecture.md)** - System design and components

### API & Reference
- **[api-reference.md](./api-reference.md)** - Complete REST API documentation

### Implementation Details
- **[implementation-plan.md](./implementation-plan.md)** - Original implementation plan
- **[agentic-core-changes.md](./agentic-core-changes.md)** - Required agentic-core modifications

### Legacy Documentation
- **[eval-system-original.md](./eval-system-original.md)** - Original eval system README
- **[workflows-readme.md](./workflows-readme.md)** - Workflow README
- **[run-workflows.md](./run-workflows.md)** - Workflow execution guide

---

## 📖 Documentation by Use Case

### "I want to install and run Axon-PFC"
→ Start with **[../SETUP.md](../SETUP.md)**

### "I want to create a workflow"
→ Read **[workflow-guide.md](./workflow-guide.md)**

### "I want to test my workflow"
→ Read **[evaluation-system.md](./evaluation-system.md)**

### "I want to understand how it works"
→ Read **[architecture.md](./architecture.md)**

### "I want to use the API"
→ Read **[api-reference.md](./api-reference.md)**

### "I'm having issues"
→ See **Troubleshooting** section in **[../SETUP.md](../SETUP.md)**

---

## 🎯 Quick Links

### Common Tasks

**Create a workflow:**
```bash
# See workflow-guide.md, section "Creating Workflows"
curl -X POST http://localhost:8000/api/workflows -d @workflow.json
```

**Create a test case:**
```bash
# See evaluation-system.md, section "Test Case Creation"
curl -X POST http://localhost:8000/api/eval-cases -d @test-case.json
```

**Run a test:**
```bash
# See evaluation-system.md, section "Running Tests"
curl -X POST http://localhost:8000/api/eval-cases/{id}/run
```

**Monitor executions:**
```
# Open dashboard in browser
http://localhost:3000 → Dashboard button
```

---

## 📋 Document Overview

### SETUP.md (Root)
**Complete installation guide**
- Prerequisites
- Agentic-core setup
- Axon-PFC setup
- Database migration
- Configuration
- Starting/stopping services
- Troubleshooting

### workflow-guide.md
**How to create and configure workflows**
- Workflow structure (JSON format)
- Agent configuration
- Variables and I/O
- Termination conditions
- Testing workflows
- Best practices
- Example workflows

### evaluation-system.md
**Testing and validation system**
- Test case creation
- Validation rules (wildcard, exact match)
- Running tests
- Monitoring executions
- Batch testing
- Advanced features (trends, flaky tests)

### architecture.md
**System design and architecture**
- System overview (diagram)
- Component details (frontend, backend, services)
- Database schema
- Execution flow
- Data flow
- Scalability considerations
- Deployment architecture

### api-reference.md
**Complete REST API documentation**
- All endpoints with request/response examples
- Error responses
- Client libraries (Python, JavaScript)
- Webhooks (future)

---

## 🔍 Search Tips

### Finding Information

**"How do I..."**
- Install? → SETUP.md
- Create workflows? → workflow-guide.md
- Test workflows? → evaluation-system.md
- Use the API? → api-reference.md

**"What is..."**
- The architecture? → architecture.md
- A test case? → evaluation-system.md
- An eval_run? → architecture.md (Database Schema)

**"Why is..."**
- My workflow stuck? → SETUP.md (Troubleshooting)
- My test failing? → evaluation-system.md (Troubleshooting)
- The dashboard showing errors? → architecture.md (Monitoring)

---

## 📝 Contributing to Docs

### Documentation Standards

1. **Use Markdown** - All docs in `.md` format
2. **Include examples** - Code examples for every concept
3. **Link between docs** - Cross-reference related sections
4. **Keep updated** - Update date at bottom of each doc

### Adding New Documentation

1. Create `.md` file in `docs/` directory
2. Add entry to this README.md index
3. Link from relevant existing docs
4. Follow existing formatting style

---

## 🗂️ File Organization

```
docs/
├── README.md                    # This file
├── architecture.md              # System architecture
├── workflow-guide.md            # Workflow creation
├── evaluation-system.md         # Testing system
├── api-reference.md             # API docs
├── quick-start.md               # Quick reference
├── implementation-plan.md       # Original plan
├── agentic-core-changes.md      # Agentic-core mods
├── eval-system-original.md      # Legacy
├── workflows-readme.md          # Legacy
└── run-workflows.md             # Legacy
```

---

## 📊 Documentation Metrics

- **Total Pages**: 10
- **Primary Guides**: 4 (workflow, evaluation, architecture, API)
- **Setup Docs**: 1 (SETUP.md)
- **Legacy Docs**: 3
- **Last Updated**: 2025-10-15

---

## 💡 Documentation Philosophy

Our documentation follows these principles:

1. **Task-Oriented** - Organized by what users want to accomplish
2. **Progressive Disclosure** - Start simple, add complexity gradually
3. **Example-Rich** - Every concept has code examples
4. **Troubleshooting-First** - Common issues prominently featured
5. **Cross-Referenced** - Easy to navigate between related topics

---

## 🔄 Documentation Updates

### Versioning

Documentation version matches software version:
- Current: **v1.0.0**
- Last Updated: **2025-10-15**

### Changelog

**v1.0.0 (2025-10-15)**
- Initial comprehensive documentation
- Setup guide with agentic-core integration
- Complete workflow and evaluation guides
- Full API reference
- Architecture documentation

---

**Need help?** Start with **[../SETUP.md](../SETUP.md)** or ask in GitHub Issues.

