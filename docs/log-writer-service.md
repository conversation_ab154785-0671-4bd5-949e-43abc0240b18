# GraphQL Event Log Writer Service

## Overview

The GraphQL Event Log Writer is a background service that subscribes to flow execution events via GraphQL WebSocket subscriptions and writes them to traditional log files. This bridges the gap between the async-engine architecture (which uses message-based processing) and the need for readable execution logs.

## Architecture

The async-engine (agentic-core) uses:
- **Message-based processing**: RabbitMQ + Celery workers
- **Real-time events**: GraphQL subscriptions for live monitoring
- **Database records**: Execution state and variables stored in PostgreSQL

However, it does NOT create traditional log files. This service fills that gap by:
1. Monitoring the database for ALL active flow executions (regardless of `debugging_enabled` flag)
2. Subscribing to their GraphQL event streams via WebSocket
3. Writing formatted logs to `artifacts/{execution_id}/flow-execution.log`
4. Making logs available through the `/api/eval-runs/{run_id}/logs` endpoint

**Key Feature:** This service captures logs for ALL executions, which means:
- ✅ Eval runs can use `debugging_enabled=False` (no pausing)
- ✅ Logs are still captured to disk automatically
- ✅ No need for Auto-Resume Service

## Event Types Handled

The service subscribes to and formats four types of events:

### 1. FlowExecutionEvent
Standard execution events (steps starting, completing, failing, etc.)
```
[{timestamp}] [DELEGATE] [agent-123] Message about execution progress
```

### 2. FlowExecutionArtifactEvent
File operations (reading, writing, creating files)
```
[{timestamp}] [ARTIFACT] File write: output.txt (/path/to/file)
```

### 3. FlowExecutionDebugEvent
Debug events (breakpoints, resume, variable inspection)
```
[{timestamp}] [DEBUG:BREAKPOINT_HIT] Context: Step paused at breakpoint
```

### 4. FlowExecutionTerminatedEvent
Execution completion or failure
```
[{timestamp}] [TERMINATED:COMPLETE] Flow execution completed successfully
  Detail: All steps finished
```

## Configuration

The service uses these environment variables:

- `GRAPHQL_WS_URL`: WebSocket endpoint (default: `ws://localhost:5002/graphql/`)
- `SYSTEM_USER`: Authentication username (default: `system`)
- `SYSTEM_PASSWORD`: Authentication password (default: `system`)
- `DATABASE_URL`: PostgreSQL connection string
- `AGENTIC_CORE_PATH`: Path to agentic-core directory (for artifacts)

## Log Format

Logs are written with the following format:
```
[YYYY-MM-DD HH:MM:SS.mmm] [EVENT_TYPE] [Component] Message
```

Example log output:
```
[2025-10-15 12:07:30.123] === Flow Execution Log Started ===
[2025-10-15 12:07:30.456] [SYSTEM] [system] Starting flow execution
[2025-10-15 12:07:31.789] [DELEGATE] [researcher] Analyzing requirements
[2025-10-15 12:07:35.012] [DELEGATE] [writer] Generating documentation
[2025-10-15 12:07:38.345] [ARTIFACT] File write: report.md (/artifacts/...)
[2025-10-15 12:07:40.678] [TERMINATED:COMPLETE] Flow execution completed
[2025-10-15 12:07:40.679] === Flow Execution Log Ended ===
```

## Starting the Service

The service is automatically started by `start.sh`:

```bash
./start.sh
```

It can also be run standalone:

```bash
cd /path/to/axon-pfc
python -m src.backend.services.graphql_log_writer
```

## Monitoring

View logs in real-time:
```bash
tail -f graphql_log_writer.log
```

The service logs its own activity:
- When it discovers new executions
- When subscriptions are established
- When subscriptions complete
- Any errors encountered

## Troubleshooting

### No logs being generated

1. **Check if service is running**:
   ```bash
   ps aux | grep graphql_log_writer
   ```

2. **Check service logs**:
   ```bash
   tail -f graphql_log_writer.log
   ```

3. **Verify WebSocket connection**:
   - Ensure GraphQL server is running on port 5002
   - Check `GRAPHQL_WS_URL` environment variable

### Subscription failures

If you see "Connection failed" or "Subscription error":
1. Verify GraphQL server is accessible
2. Check authentication credentials
3. Ensure execution ID is valid
4. Check network connectivity

### Log files not appearing

If subscriptions work but no log files:
1. Verify artifacts directory exists and is writable
2. Check `AGENTIC_CORE_PATH` environment variable
3. Ensure sufficient disk space

## Integration with Dashboard

The dashboard's run detail page automatically fetches logs via:
```
GET /api/eval-runs/{run_id}/logs
```

The backend endpoint:
1. Queries the database for the execution's log file path
2. Reads from `artifacts/{execution_id}/flow-execution.log`
3. Returns the content to the frontend
4. Auto-refreshes every second for active runs

## Performance Considerations

- The service maintains one WebSocket connection per active execution
- Log files are written with line buffering for immediate availability
- Completed executions are automatically cleaned up
- Database polling occurs every 2 seconds (configurable)

## Future Enhancements

Possible improvements:
1. **Filtering**: Support for log level filtering (INFO, DEBUG, ERROR)
2. **Rotation**: Automatic log rotation for large files
3. **Compression**: Compress logs for completed executions
4. **Structured logging**: JSON format option for machine parsing
5. **Search**: Full-text search across logs
6. **Replay**: Re-subscribe to historical events from database

