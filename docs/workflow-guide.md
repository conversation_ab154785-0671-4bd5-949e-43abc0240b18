# Workflow Creation Guide

Complete guide to creating and testing workflows in Axon-PFC.

---

## 📋 Table of Contents

1. [Workflow Basics](#workflow-basics)
2. [Workflow Structure](#workflow-structure)
3. [Creating Workflows](#creating-workflows)
4. [Agent Configuration](#agent-configuration)
5. [Variables and I/O](#variables-and-io)
6. [Termination Conditions](#termination-conditions)
7. [Testing Workflows](#testing-workflows)
8. [Best Practices](#best-practices)
9. [Example Workflows](#example-workflows)

---

## Workflow Basics

### What is a Workflow?

A workflow in Axon-PFC is a **team of AI agents** that collaborate to accomplish a task:

```
User Input → Coordinator Agent → Worker Agents → Final Output
```

### Workflow Types

1. **Team Workflows** - Multiple agents collaborating
2. **Single Agent** - One agent handling the task
3. **Sequential** - Agents execute in order
4. **Parallel** - Agents execute simultaneously (future)

---

## Workflow Structure

### JSON Format

```json
{
  "name": "Workflow Name",
  "description": "What this workflow does",
  "type": "team",
  "variables": {
    "input_var": {
      "type": "Input",
      "data_type": "str",
      "direction": "Input"
    },
    "output_var": {
      "type": "Output",
      "data_type": "str",
      "direction": "Output"
    }
  },
  "agents": [
    {
      "name": "agent_name",
      "model": "gpt-4o",
      "skills": "Agent instructions...",
      "persona": "Agent personality..."
    }
  ],
  "termination_condition": {
    "type": "regex",
    "pattern": "TERMINATE"
  }
}
```

---

## Creating Workflows

### Via UI

1. **Navigate to Frontend**
   - Open http://localhost:3000
   - Click "New Workflow" button

2. **Enter Workflow Details**
   - **Name**: Descriptive name (e.g., "Greeting Team Workflow")
   - **Description**: What the workflow does

3. **Define Variables**
   - **Input Variables**: What the workflow receives
   - **Output Variables**: What the workflow produces

4. **Add Agents**
   - Click "Add Agent"
   - Configure each agent:
     - Name
     - Model (gpt-4o, gpt-4, gpt-3.5-turbo)
     - Skills (instructions)
     - Persona (personality/role)

5. **Set Termination Condition**
   - Usually: `"TERMINATE"` regex pattern

6. **Save Workflow**
   - Click "Create Workflow"
   - System creates `flow` and `flow_version` records

### Via API

```bash
curl -X POST http://localhost:8000/api/workflows \
  -H "Content-Type: application/json" \
  -d @workflow.json
```

**File: workflow.json**
```json
{
  "name": "My Workflow",
  "description": "Description",
  "definition": {
    "type": "team",
    "variables": { /* ... */ },
    "agents": [ /* ... */ ]
  },
  "user_id": "ebd4bbe3-3065-4222-a240-e480aaef0672",
  "tenant_id": "40eaf721-236a-407e-b0f5-6e09b7cd39d8"
}
```

### From Example

Use the provided example as a template:

```bash
cp src/backend/example_team.json my_workflow.json
# Edit my_workflow.json
# Import via UI or API
```

---

## Agent Configuration

### Agent Roles

#### Coordinator Agent

**Purpose:** Orchestrates other agents

**Skills Example:**
```
You are the coordinator. Your job is to:

1. Read the input variables
2. Ask @agent1 to do task A
3. Ask @agent2 to do task B
4. Ask @agent3 to combine results
5. DO NOT create content yourself - only coordinate

Selection guide:
{roles}

Conversation so far:
{history}

Available agents: {participants}
```

**Key Points:**
- Uses `@mentions` to talk to other agents
- Doesn't create content directly
- Manages conversation flow

#### Worker Agents

**Purpose:** Perform specific tasks

**Skills Example:**
```
You create opening greetings for any topic.

When the coordinator asks you:
1. Read the 'topic' variable
2. Create a warm, engaging opening line
3. Keep it under 50 words
4. Return your greeting directly
```

**Key Points:**
- Focused on one task
- Responds to coordinator
- Produces specific output

#### Closing Agent

**Purpose:** Finalizes output and terminates

**Skills Example:**
```
You finalize greetings and save them.

When the coordinator asks you:
1. Read ALL messages from other agents
2. Combine everything into final output
3. **REQUIRED**: Use save_variable tool:
   save_variable("greeting_message", "<final content>")
4. **REQUIRED**: Say exactly: "TERMINATE"
5. Example: "Done! TERMINATE"
```

**Key Points:**
- Uses `save_variable` tool
- Says "TERMINATE" to end workflow
- Combines work from other agents

---

### Model Selection

| Model | Speed | Cost | Quality | Best For |
|-------|-------|------|---------|----------|
| gpt-4o | Fast | Medium | Excellent | Most workflows |
| gpt-4 | Slow | High | Best | Complex reasoning |
| gpt-3.5-turbo | Very Fast | Low | Good | Simple tasks |

**Recommendation:** Start with `gpt-4o` for all agents.

---

### Skills vs Persona

**Skills:** *What the agent does* (instructions)
```
You create greetings.
1. Read the topic
2. Write 2-3 sentences
3. Be warm and welcoming
```

**Persona:** *How the agent behaves* (personality)
```
You are a friendly, enthusiastic writer who loves
making people feel welcome.
```

**Best Practice:** 
- Skills = task-focused
- Persona = tone/style-focused

---

## Variables and I/O

### Input Variables

Data provided TO the workflow:

```json
{
  "variables": {
    "topic": {
      "type": "Input",
      "data_type": "str",
      "direction": "Input",
      "description": "The topic to create a greeting about"
    },
    "user_name": {
      "type": "Input",
      "data_type": "str",
      "direction": "Input"
    }
  }
}
```

**Data Types:**
- `str` - String/text
- `int` - Integer number
- `float` - Decimal number
- `bool` - True/false
- `list` - Array
- `dict` - Object/dictionary

### Output Variables

Data produced BY the workflow:

```json
{
  "variables": {
    "greeting_message": {
      "type": "Output",
      "data_type": "str",
      "direction": "Output",
      "description": "The generated greeting"
    },
    "conversation_log": {
      "type": "Output",
      "data_type": "str",
      "direction": "Output"
    }
  }
}
```

### Accessing Variables in Agents

Agents automatically have access to input variables:

```
"skills": "Read the 'topic' variable and create a greeting about it."
```

The variable is available in the agent's context.

### Saving Output Variables

Agents MUST use the `save_variable` tool:

```python
# In agent conversation
save_variable("greeting_message", "Welcome to space exploration!")
save_variable("conversation_log", "Full conversation text here")
```

**Important:** Output variables must be saved explicitly!

---

## Termination Conditions

### Why Termination Matters

Without termination:
- Workflow runs indefinitely
- Costs accumulate
- No output produced

### Regex Pattern (Recommended)

```json
{
  "termination_condition": {
    "type": "regex",
    "pattern": "TERMINATE"
  }
}
```

Agent must say exactly: `"TERMINATE"` or `"Done! TERMINATE"`

### Message Count Limit

```json
{
  "termination_condition": {
    "type": "max_messages",
    "limit": 20
  }
}
```

Workflow stops after 20 agent messages.

### Combined Conditions

```json
{
  "termination_condition": {
    "type": "or",
    "conditions": [
      {"type": "regex", "pattern": "TERMINATE"},
      {"type": "max_messages", "limit": 50}
    ]
  }
}
```

Stops when either condition is met.

---

## Testing Workflows

### 1. Create Test Case

**Via UI:**
1. Go to workflow detail
2. Click "Test Cases" tab
3. Click "Create Test Case"
4. Fill in:
   - Name: "Basic Space Test"
   - Input Variables: `{"topic": "space exploration"}`
   - Expected Output: `{"greeting_message": "*"}`
5. Save

### 2. Run Test

1. Click "Run Test" button
2. Navigate to Dashboard
3. Watch execution in "Active Runs"

### 3. Verify Results

**Success:**
- Execution shows "COMPLETE"
- Validation shows "✓ Yes" (passed)
- Duration < 30 seconds

**Failure:**
- Execution shows "FAILED" or stuck "RUNNING"
- Check validation output for details

### 4. Iterate

If test fails:
1. Check execution error message
2. Update agent instructions
3. Re-run test
4. Repeat until passing

---

## Best Practices

### 1. Start Simple

✅ **Do:**
```json
{
  "agents": [
    {
      "name": "simple_agent",
      "skills": "Create a greeting for the given topic."
    }
  ]
}
```

❌ **Don't:**
```json
{
  "agents": [
    {"name": "coordinator"},
    {"name": "analyzer"},
    {"name": "researcher"},
    {"name": "writer"},
    {"name": "editor"},
    {"name": "validator"}
  ]
}
```

Start with 1-2 agents, add more as needed.

### 2. Clear Instructions

✅ **Do:**
```
When the coordinator asks you:
1. Read the 'topic' variable
2. Create a greeting (2-3 sentences)
3. Return your greeting
```

❌ **Don't:**
```
Create greetings.
```

Be specific about steps.

### 3. Explicit Termination

✅ **Do:**
```
After saving variables, you MUST say: "TERMINATE"

Example: "Done! TERMINATE"
```

❌ **Don't:**
```
When done, finish the task.
```

Be explicit about the exact termination phrase.

### 4. Use Wildcards in Tests

✅ **Start with:**
```json
{
  "expected_output": {
    "greeting_message": "*",
    "conversation_log": "*"
  }
}
```

✅ **Then refine:**
```json
{
  "expected_output": {
    "greeting_message": "*",
    "conversation_log": "*",
    "word_count": ">= 20"
  }
}
```

Start permissive, add constraints later.

### 5. Version Control

- Each workflow update creates a new version
- Old versions are preserved
- You can roll back if needed

### 6. Naming Conventions

**Workflows:**
- "Greeting Generator Team"
- "Customer Support Workflow"
- "Document Summarizer"

**Agents:**
- `coordinator` (orchestrator)
- `greeting_creator` (specific task)
- `closing_specialist` (finalizer)

**Test Cases:**
- "Basic Space Test"
- "Long Input Handling"
- "Edge Case - Empty Topic"

---

## Example Workflows

### Example 1: Simple Greeting

**Purpose:** Generate a greeting for any topic

```json
{
  "name": "Simple Greeting Workflow",
  "type": "team",
  "variables": {
    "topic": {
      "type": "Input",
      "data_type": "str",
      "direction": "Input"
    },
    "greeting": {
      "type": "Output",
      "data_type": "str",
      "direction": "Output"
    }
  },
  "agents": [
    {
      "id": "agent-uuid",
      "name": "greeter",
      "model": "gpt-4o",
      "skills": "Create a warm greeting about the given topic. After writing, use save_variable('greeting', '<your greeting>') and say 'TERMINATE'.",
      "persona": "You are a friendly, enthusiastic greeter."
    }
  ],
  "termination_condition": {
    "type": "regex",
    "pattern": "TERMINATE"
  }
}
```

**Test Case:**
```json
{
  "input_variables": {"topic": "coffee"},
  "expected_output": {"greeting": "*"}
}
```

---

### Example 2: Multi-Agent Team

**Purpose:** Collaborative greeting creation

```json
{
  "name": "Greeting Team Workflow",
  "type": "team",
  "variables": {
    "topic": {
      "type": "Input",
      "data_type": "str",
      "direction": "Input"
    },
    "greeting_message": {
      "type": "Output",
      "data_type": "str",
      "direction": "Output"
    }
  },
  "agents": [
    {
      "id": "coordinator-uuid",
      "name": "coordinator",
      "model": "gpt-4o",
      "skills": "Orchestrate greeting creation:\n1. Ask @greeting_creator to create opening\n2. Ask @message_enhancer to add middle\n3. Ask @closing_specialist to finalize\n\n{roles}\n{history}\n{participants}",
      "persona": "Efficient project manager"
    },
    {
      "id": "creator-uuid",
      "name": "greeting_creator",
      "model": "gpt-4o",
      "skills": "Create opening greetings. Read 'topic' variable and write 1-2 warm sentences.",
      "persona": "Creative and welcoming"
    },
    {
      "id": "enhancer-uuid",
      "name": "message_enhancer",
      "model": "gpt-4o",
      "skills": "Add inspiring middle section. Read previous messages and add 2-3 engaging sentences.",
      "persona": "Inspirational speaker"
    },
    {
      "id": "closer-uuid",
      "name": "closing_specialist",
      "model": "gpt-4o",
      "skills": "Read ALL messages, combine into final greeting, use save_variable('greeting_message', '<final>'), say 'TERMINATE'.",
      "persona": "Detail-oriented finalizer"
    }
  ],
  "termination_condition": {
    "type": "regex",
    "pattern": "TERMINATE"
  }
}
```

**Test Case:**
```json
{
  "input_variables": {"topic": "space exploration"},
  "expected_output": {"greeting_message": "*"}
}
```

---

### Example 3: Data Processing

**Purpose:** Analyze and summarize text

```json
{
  "name": "Text Analyzer",
  "type": "team",
  "variables": {
    "input_text": {
      "type": "Input",
      "data_type": "str",
      "direction": "Input"
    },
    "summary": {
      "type": "Output",
      "data_type": "str",
      "direction": "Output"
    },
    "key_points": {
      "type": "Output",
      "data_type": "list",
      "direction": "Output"
    },
    "sentiment": {
      "type": "Output",
      "data_type": "str",
      "direction": "Output"
    }
  },
  "agents": [
    {
      "id": "analyzer-uuid",
      "name": "text_analyzer",
      "model": "gpt-4o",
      "skills": "Analyze input_text:\n1. Create summary (3-5 sentences)\n2. Extract key points (list)\n3. Determine sentiment (positive/negative/neutral)\n4. save_variable for each output\n5. Say 'TERMINATE'",
      "persona": "Analytical and objective"
    }
  ],
  "termination_condition": {
    "type": "regex",
    "pattern": "TERMINATE"
  }
}
```

**Test Case:**
```json
{
  "input_variables": {
    "input_text": "The new product launch was a huge success..."
  },
  "expected_output": {
    "summary": "*",
    "key_points": "*",
    "sentiment": "*"
  }
}
```

---

## Troubleshooting

### Workflow Never Completes

**Symptoms:** Stuck in RUNNING state for > 5 minutes

**Causes:**
- Agent doesn't say "TERMINATE"
- Agent conversation loops
- No termination condition

**Solutions:**
1. Add explicit TERMINATE instruction to closing agent
2. Add max_messages termination condition
3. Cancel and fix workflow

### Output Variables Missing

**Symptoms:** Validation fails with "Missing key: variable_name"

**Causes:**
- Agent didn't call `save_variable`
- Wrong variable name
- Agent forgot to save

**Solutions:**
1. Check agent instructions include `save_variable` call
2. Verify variable names match exactly
3. Add explicit examples in agent skills

### Agent Ignores Instructions

**Symptoms:** Agent does something different than instructed

**Causes:**
- Instructions too vague
- Conflicting instructions
- Model limitations

**Solutions:**
1. Be more explicit (numbered steps)
2. Remove ambiguity
3. Add examples
4. Try gpt-4 instead of gpt-3.5-turbo

---

## Advanced Topics

### Custom Tools

Future feature: Define custom Python functions for agents to call

```json
{
  "tools": [
    {
      "name": "search_database",
      "description": "Search the database for records",
      "parameters": {
        "query": "str",
        "limit": "int"
      }
    }
  ]
}
```

### Conditional Logic

Future feature: Branching workflows

```json
{
  "steps": [
    {"agent": "analyzer", "next": "classifier"},
    {
      "agent": "classifier",
      "branches": {
        "positive": "positive_handler",
        "negative": "negative_handler"
      }
    }
  ]
}
```

### Human-in-the-Loop

Future feature: Pause for human input

```json
{
  "steps": [
    {"agent": "draft_writer"},
    {"type": "human_review", "timeout": 3600},
    {"agent": "publisher"}
  ]
}
```

---

**Last Updated:** 2025-10-15  
**Version:** 1.0

