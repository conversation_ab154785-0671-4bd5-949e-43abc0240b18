# Workflow Parallel Execution - Quick Start

## 🚀 Get Started in 3 Steps

### Step 1: Setup (One Time)

```bash
# Make sure services are running
cd ../agentic-core
docker compose up -d postgres rabbitmq async-engine-flyweight async-engine-async

# Create the test workflow
cd ../axon-pfc
python setup_test_workflow.py
```

### Step 2: Run Workflows

```bash
python run_workflow_parallel.py
```

### Step 3: Watch It Work! 🎉

```
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Workflow Parallel Runner                                  ║
╚══════════════════════════════════════════════════════════════════════════════╝

✓ Found workflow 'test'
✓ RabbitMQ connection established
✓ Setup complete

📤 Published run 1 with variables: {'topic': 'space exploration'}
📤 Published run 2 with variables: {'topic': 'ocean adventure'}
...
✓ Published 5 runs successfully

✅ Run 1 completed
✅ Run 2 completed
...
```

## 📁 What Was Created

| File | Size | Purpose |
|------|------|---------|
| `run_workflow_parallel.py` | 16K | ⭐ **Main script** - Complete solution |
| `process_runs.py` | 12K | Publisher only (no monitoring) |
| `run_processor_worker.py` | 11K | Simplified worker (for debugging) |
| `setup_test_workflow.py` | 2.3K | Creates the "test" workflow |
| `RUN_WORKFLOWS.md` | 9.8K | 📚 Comprehensive documentation |
| `README_WORKFLOWS.md` | 6.2K | 📖 Quick reference guide |
| `WORKFLOW_EXECUTION_SUMMARY.md` | 11K | 🏗️ Implementation details |

## 🎯 Key Features

- ✅ **Parallel Execution** - Run multiple workflows concurrently
- ✅ **Concurrency Control** - Semaphore limits (default: 3 at a time)
- ✅ **Real-time Monitoring** - Watch progress as runs complete
- ✅ **agentic-core Integration** - Uses proper message formats
- ✅ **Error Handling** - Failed runs don't crash the batch
- ✅ **Production Ready** - Architecture follows best practices

## 🔧 Customization

### Change the Workflow

```python
runner = WorkflowParallelRunner(
    workflow_name="my_workflow",  # Change this
    max_concurrent_runs=3
)
```

### Change Input Variables

```python
input_sets = [
    {"my_variable": "value1"},
    {"my_variable": "value2"},
    {"my_variable": "value3"},
]
```

### Increase Concurrency

```python
runner = WorkflowParallelRunner(
    workflow_name="test",
    max_concurrent_runs=10  # More parallel runs
)
```

## 🐛 Troubleshooting

### "Workflow 'test' not found"
```bash
python setup_test_workflow.py
```

### "Connection refused"
```bash
# Check services are running
cd ../agentic-core
docker compose ps
```

### Runs stay "pending"
```bash
# Check workers are running
docker compose logs async-engine-flyweight
```

## 📚 Learn More

- **Quick Start**: `README_WORKFLOWS.md` (this file)
- **Comprehensive Guide**: `RUN_WORKFLOWS.md`
- **Implementation Details**: `WORKFLOW_EXECUTION_SUMMARY.md`

## 💡 How It Works

```
Create Runs → Publish to RabbitMQ → Workers Process → Monitor Status
    ↓              ↓                      ↓              ↓
Database      agentic.flyweight      async-engine    Poll DB
              queue                  workers         for results
```

## ✨ That's It!

You now have a complete system for running workflows in parallel!

**Next Steps:**
1. Read `RUN_WORKFLOWS.md` for detailed documentation
2. Customize for your own workflows
3. Integrate into your application

Happy workflow orchestration! 🎉

