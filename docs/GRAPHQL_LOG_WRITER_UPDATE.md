# GraphQL Log Writer Update - No More Auto-Resume Needed

## Summary

The GraphQL Log Writer has been updated to monitor **ALL** flow executions, not just those with `debugging_enabled=true`. This change eliminates the need for the Auto-Resume Service.

## What Changed

### 1. GraphQL Log Writer (`src/backend/services/graphql_log_writer.py`)

**Before:**
```python
query = text("""
    SELECT id, result, debugging_enabled
    FROM flow_execution.flow_execution
    WHERE result IN ('PENDING', 'RUNNING')
      AND debugging_enabled = true  # <-- Only monitored debugging=true
    ORDER BY start_time DESC
""")
```

**After:**
```python
query = text("""
    SELECT id, result, debugging_enabled
    FROM flow_execution.flow_execution
    WHERE result IN ('PENDING', 'RUNNING')
      # Removed debugging_enabled filter - monitors ALL executions
    ORDER BY start_time DESC
""")
```

### 2. Eval Runner Default (`src/backend/services/eval_runner.py`)

- Updated documentation to clarify that logs are captured regardless of `debugging_enabled`
- Default remains `enable_debugging=False` (no pausing)
- Logs are still captured via GraphQL Log Writer

## Benefits

### ✅ Simplified Architecture
- **No more Auto-Resume Service needed**
- One less background service to maintain
- Cleaner, more straightforward execution flow

### ✅ Automatic Execution
- Eval runs with `debugging_enabled=False` run automatically without pausing
- No need to detect and resume paused executions
- Tests complete faster and more reliably

### ✅ Full Logging
- All executions get logs written to disk automatically
- Dashboard can still display full execution logs
- No loss of debugging capability

## Migration

### Stop Auto-Resume Service

The Auto-Resume Service is now deprecated and can be stopped:

```bash
# If running via start.sh, comment out the auto-resume section
# Or manually stop the service
pkill -f "auto_resume.py"
```

### Verify GraphQL Log Writer is Running

Ensure the GraphQL Log Writer service is running:

```bash
# Check if running
ps aux | grep graphql_log_writer

# If not running, start it
python -m src.backend.services.graphql_log_writer &

# Or use start.sh which includes it
./start.sh
```

### Test Eval Runs

1. Run a test eval case
2. Verify it completes without pausing
3. Check that logs are available in the dashboard
4. Confirm logs exist at `artifacts/{execution_id}/flow-execution.log`

## How It Works Now

```
User clicks "Run Test"
   ↓
Backend: EvalRunner creates flow_execution with debugging_enabled=False
   ↓
Worker: Starts execution normally (no pausing)
   ↓
GraphQL Log Writer: 
   - Detects execution in database
   - Subscribes to GraphQL event stream
   - Writes logs to disk in real-time
   ↓
Execution completes → Pass/Fail result
   ↓
Dashboard: Displays logs from disk
```

## Rollback (If Needed)

If GraphQL events are not emitted when `debugging_enabled=False`:

1. **Revert GraphQL Log Writer:**
   ```python
   # Add back the filter in _get_active_flow_executions()
   WHERE result IN ('PENDING', 'RUNNING')
     AND debugging_enabled = true
   ```

2. **Enable debugging in eval runs:**
   ```python
   # In routes/evaluation.py or wherever eval runner is initialized
   eval_runner = EvalRunner(db=db, enable_debugging=True)
   ```

3. **Restart Auto-Resume Service:**
   ```bash
   python -m src.backend.services.auto_resume &
   ```

## Testing

A test script has been created to verify GraphQL events are emitted:

```bash
cd /Users/<USER>/projects/axon-pfc
python scripts/test_graphql_events.py
```

This will:
- Create a test execution with `debugging_enabled=False`
- Subscribe to its GraphQL event stream
- Report whether events are received
- Clean up after itself

## Files Modified

- ✅ `src/backend/services/graphql_log_writer.py` - Removed debugging_enabled filter
- ✅ `src/backend/services/eval_runner.py` - Updated documentation
- ✅ `src/backend/services/auto_resume.py` - Added deprecation notice
- ✅ `docs/log-writer-service.md` - Updated to reflect changes
- ✅ `docs/auto-resume-service.md` - Added deprecation notice
- ✅ `scripts/test_graphql_events.py` - Created for testing

## Next Steps

1. ✅ Changes applied to codebase
2. ⏳ Restart GraphQL Log Writer service with new code
3. ⏳ Stop Auto-Resume Service (no longer needed)
4. ⏳ Test eval runs to verify logs are captured
5. ⏳ Monitor for any issues

## Questions?

If logs are not appearing:
1. Check GraphQL Log Writer is running: `ps aux | grep graphql_log_writer`
2. Check logs: `tail -f graphql_log_writer.log`
3. Verify GraphQL server is accessible on port 5002
4. Run test script: `python scripts/test_graphql_events.py`

