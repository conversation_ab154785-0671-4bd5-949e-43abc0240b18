# OpenAI Agents SDK Implementation

## Overview

The workflow specification generator has been reimplemented using the [OpenAI Agents SDK](https://openai.github.io/openai-agents-python/quickstart/), enabling multi-agent orchestration with specialized agents working together through handoffs.

## Architecture

### Multi-Agent System

The system uses 7 specialized agents that work together to generate comprehensive workflow specifications:

1. **Research Agent** - Researches best practices, frameworks, and industry standards
2. **Requirements Analyst Agent** - Analyzes requirements and creates overview
3. **Risk Assessment Agent** - Identifies risks with severity levels and mitigation strategies
4. **Tools & Resources Agent** - Identifies required tools, technologies, and resources
5. **Timeline Planner Agent** - Creates implementation timeline with phases and milestones
6. **Success Metrics Agent** - Defines KPIs and measurable success criteria
7. **Documentation Agent** - Compiles all findings into cohesive specification

### Agent Orchestration Flow

```
User Request → Orchestrator Agent
                    ↓
            [Research Agent] ← Web search enabled
                    ↓ (handoff)
         [Requirements Analyst] ← Uses research findings
                    ↓ (handoff)
          [Risk Assessment Agent] ← Uses requirements & scope
                    ↓ (handoff)
        [Tools & Resources Agent] ← Uses research & requirements
                    ↓ (handoff)
          [Timeline Planner Agent] ← Uses requirements & objectives
                    ↓ (handoff)
         [Success Metrics Agent] ← Uses objectives & requirements
                    ↓ (handoff)
          [Documentation Agent] ← Compiles all agent outputs
                    ↓
            Final Specification → Word Document
```

## Key Features

### Handoffs Between Agents

Each agent handoff generates frontend messages, providing real-time visibility into the process:

```python
self.add_message(task_id, "thinking", f"Handoff to {agent_name}...")
self.update_progress(task_id, progress_value)
```

Messages types:
- **info**: General information about the process
- **thinking**: Current agent activity or handoff
- **success**: Agent completion or milestone
- **error**: Error conditions

### Structured Outputs with Pydantic

Each agent has a strongly-typed output model using Pydantic:

```python
class RiskAssessmentOutput(BaseModel):
    risks: list[Dict[str, str]]  # description, severity, mitigation

risk_output = result.final_output_as(RiskAssessmentOutput)
```

Benefits:
- Type safety
- Automatic validation
- Clear interfaces between agents
- Easy JSON serialization

### Context Passing

Agents build on each other's work by receiving context from previous agents:

```python
risk_context = f"""
Workflow: {workflow_request}
Overview: {requirements_output.overview}
Scope: {requirements_output.scope}

Please identify all risks, their severity, and mitigation strategies.
"""
```

This ensures coherent, comprehensive specifications.

### Progress Tracking

Progress is tracked and communicated to the frontend at each stage:

- 5%: Initial setup
- 15%: Research phase
- 30%: Requirements analysis
- 45%: Risk assessment
- 60%: Tools & resources
- 75%: Timeline planning
- 85%: Success metrics
- 95%: Documentation compilation
- 98%: Word document generation
- 100%: Complete

## Implementation Details

### Agent Configuration

Agents are configured with:

```python
agent = Agent(
    name="Agent Name",
    handoff_description="When to use this agent",
    instructions="Detailed instructions...",
    output_type=OutputModel,  # Pydantic model
    model_settings={"model": "gpt-4o"}
)
```

### Running Agents

Agents are executed asynchronously using the Runner:

```python
result = await Runner.run(agent, input_text)
output = result.final_output_as(OutputModel)
```

### Async/Sync Bridge

The service provides both async and sync interfaces:

```python
# Async implementation
async def _generate_specification_async(self, task_id, workflow_request):
    # ... multi-agent orchestration

# Sync wrapper for backward compatibility
def generate_specification(self, task_id, workflow_request):
    asyncio.run(self._generate_specification_async(task_id, workflow_request))
```

## Benefits Over Previous Implementation

### 1. Specialization
Each agent focuses on one aspect, leading to higher quality outputs in each domain.

### 2. Transparency
Every agent handoff generates frontend messages, providing clear visibility into the AI's reasoning process.

### 3. Maintainability
Agent instructions and outputs are clearly defined and separated, making the system easier to understand and modify.

### 4. Extensibility
New agents can be added easily by:
- Defining a new output model
- Creating the agent with instructions
- Adding it to the orchestrator's handoff list

### 5. Structured Data
Pydantic models ensure consistent, validated output structures that are easy to work with.

### 6. Better Results
Multiple specialized agents with domain expertise produce more comprehensive and detailed specifications than a single monolithic prompt.

## API Surface

The implementation maintains backward compatibility with the existing API:

```python
generator = get_generator()

# Create task
task_id = generator.create_task(workflow_request)

# Generate specification (async with agents)
generator.generate_specification(task_id, workflow_request)

# Get task status (includes messages from agent handoffs)
status = generator.get_task_status(task_id)
```

## Message Flow Example

Frontend receives messages like:

```json
[
  {"type": "info", "content": "Starting analysis of workflow request: ..."},
  {"type": "thinking", "content": "Handoff to Research Agent..."},
  {"type": "success", "content": "Research Agent completed analysis"},
  {"type": "thinking", "content": "Handoff to Requirements Analyst..."},
  {"type": "success", "content": "Requirements Analyst completed analysis"},
  ...
]
```

## Future Enhancements

### Potential Improvements

1. **Parallel Execution**: Run independent agents (e.g., risk assessment and tools) in parallel
2. **Agent Reflection**: Add agents that review and critique other agents' outputs
3. **User Interaction**: Add guardrails that request user input for clarification
4. **Streaming**: Stream agent responses in real-time for immediate feedback
5. **Memory**: Add persistent memory across specification generations
6. **Custom Tools**: Give agents access to custom tools (databases, APIs, etc.)

### Adding New Agents

To add a new agent:

```python
# 1. Define output model
class NewAgentOutput(BaseModel):
    field1: str
    field2: list[str]

# 2. Create agent
new_agent = Agent(
    name="New Agent",
    handoff_description="Purpose of this agent",
    instructions="Detailed instructions...",
    output_type=NewAgentOutput,
    model_settings={"model": self.model}
)

# 3. Add to orchestrator
self.orchestrator_agent = Agent(
    handoffs=[..., new_agent]
)

# 4. Call in workflow
result = await self._run_agent_with_tracking(
    task_id, new_agent, context, "New Agent", 50
)
```

## References

- [OpenAI Agents SDK Documentation](https://openai.github.io/openai-agents-python/)
- [OpenAI Agents Quickstart](https://openai.github.io/openai-agents-python/quickstart/)
- [Pydantic Documentation](https://docs.pydantic.dev/)

## Dependencies

```
openai>=1.52.0
openai-agents>=0.1.0
pydantic>=2.0.0
```

## Testing

The agent system can be tested using the existing workflow spec API:

```bash
# Start the backend
./start.sh

# Use the frontend UI at http://localhost:3456
# Or test via API:
curl -X POST http://localhost:8080/api/workflow-spec/generate \
  -H "Content-Type: application/json" \
  -d '{"workflow_request": "Create a customer onboarding workflow"}'
```

Monitor the frontend for real-time agent handoff messages.



