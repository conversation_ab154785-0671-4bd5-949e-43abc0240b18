# Workflow JSON Generator - Troubleshooting Guide

## "Failed to get status" Error

If you're seeing "failed to get status" in the frontend, here's how to diagnose and fix it:

### Step 1: Verify Backend is Running

Check that the backend server is running:

```bash
# Check if backend is running on port 8000
curl http://localhost:8000/health

# Should return: {"status":"healthy","database":"connected"}
```

If the backend isn't running:

```bash
cd /Users/<USER>/projects/axon-pfc
source venv/bin/activate
cd src/backend
python main.py
```

### Step 2: Test the Endpoint Directly

```bash
# Test the health check for the workflow generation service
curl http://localhost:8000/workflow-generation/health

# Should return: {"status":"healthy","service":"workflow-json-generation"}
```

If you get a 404, the route isn't loaded. **Restart the backend server**.

### Step 3: Check CORS Configuration

The error might be a CORS issue. Check the browser console for CORS errors:

1. Open browser DevTools (F12)
2. Go to Console tab
3. Look for red CORS errors

The backend already has CORS enabled for all origins in `main.py`:

```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### Step 4: Test Upload and Status Flow

```bash
# Create a simple test document
cat > /tmp/test_spec.docx << 'EOF'
This is a test workflow specification.

Objective: Process data and generate reports.

Inputs:
- data_file: Path to data file

Steps:
1. Load data
2. Process data
3. Generate report

Outputs:
- report: Generated report text
EOF

# Note: The above won't create a valid .docx, use this Python script instead:
python3 << 'EOF'
from docx import Document
doc = Document()
doc.add_heading('Workflow Specification', 0)
doc.add_paragraph('Objective: Process data and generate reports.')
doc.add_heading('Inputs', level=1)
doc.add_paragraph('- data_file: Path to data file')
doc.add_heading('Steps', level=1)
doc.add_paragraph('1. Load data')
doc.add_paragraph('2. Process data')
doc.add_paragraph('3. Generate report')
doc.add_heading('Outputs', level=1)
doc.add_paragraph('- report: Generated report text')
doc.save('/tmp/test_spec.docx')
print('✅ Test document created at /tmp/test_spec.docx')
EOF

# Upload and start generation
TASK_ID=$(curl -X POST http://localhost:8000/workflow-generation/generate \
  -F "file=@/tmp/test_spec.docx" \
  -F "workflow_name=Test Workflow" \
  | jq -r '.task_id')

echo "Task ID: $TASK_ID"

# Check status
curl "http://localhost:8000/workflow-generation/status/$TASK_ID" | jq

# Poll status every 2 seconds
watch -n 2 "curl -s http://localhost:8000/workflow-generation/status/$TASK_ID | jq"
```

### Step 5: Check Backend Logs

Look at the backend logs for errors:

```bash
# If running in terminal, check the output
# Look for:
# - Import errors
# - OpenAI API key issues
# - Exception tracebacks
```

### Common Issues and Solutions

#### Issue: "Workflow JSON Generator service not available"

**Cause**: Import failed for `workflow_json_generator.py`

**Solution**: Check that all dependencies are installed:
```bash
cd /Users/<USER>/projects/axon-pfc
source venv/bin/activate
pip install -r requirements.txt
```

#### Issue: "OPENAI_API_KEY not set"

**Cause**: Missing OpenAI API key

**Solution**: Set the API key in `.env`:
```bash
echo "OPENAI_API_KEY=your-key-here" >> .env
```

Then restart the backend.

#### Issue: Task stuck in "processing" status

**Cause**: Agent execution timed out or failed

**Solution**: Check backend logs for the error. Common causes:
- OpenAI API rate limits
- Network issues
- Invalid API key
- Model not available

#### Issue: 404 on /workflow-generation/status/{id}

**Cause**: Route not loaded properly

**Solution**: Restart backend server. The route should auto-load from `main.py`.

#### Issue: CORS error in browser

**Cause**: Frontend accessing from different origin

**Solution**: 
1. Access frontend from `http://localhost:8000/src/frontend/index.html`
2. Or update CORS settings in `main.py` to include your origin

### Debug Mode

Add debug logging to see what's happening:

**In `workflow_json_generator.py`**, the service already has print statements:
```python
print(f"[WorkflowJSONGenerator] Starting workflow design for task {task_id}")
print(f"[WorkflowJSONGenerator] Successfully generated workflow for task {task_id}")
print(f"[WorkflowJSONGenerator] Error in task {task_id}: {str(e)}")
```

**In frontend**, check browser console:
```javascript
console.error('Status poll error:', err);
```

### Verification Checklist

- [ ] Backend server is running on port 8000
- [ ] `/workflow-generation/health` returns 200
- [ ] OpenAI API key is set in `.env`
- [ ] All dependencies installed (`pip install -r requirements.txt`)
- [ ] No CORS errors in browser console
- [ ] Backend logs show no import errors
- [ ] Frontend is accessing `http://localhost:8000`

### Testing Without Frontend

If frontend isn't working, test the API directly:

```python
import requests
import time

# Upload document
with open('/tmp/test_spec.docx', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/workflow-generation/generate',
        files={'file': f},
        data={'workflow_name': 'Test Workflow'}
    )
    result = response.json()
    print(f"Upload response: {result}")
    task_id = result['task_id']

# Poll status
for i in range(30):  # Poll for up to 60 seconds
    response = requests.get(f'http://localhost:8000/workflow-generation/status/{task_id}')
    status = response.json()
    print(f"Status ({i*2}s): {status['status']} - {status['message']}")
    
    if status['status'] in ['completed', 'failed']:
        print(f"\nFinal result: {status}")
        if status['status'] == 'completed':
            print(f"\nGenerated workflow JSON:")
            import json
            print(json.dumps(status['workflow_json'], indent=2))
        break
    
    time.sleep(2)
```

### Still Having Issues?

1. **Check Python version**: Requires Python 3.10+
   ```bash
   python --version
   ```

2. **Verify virtual environment**: Make sure you're in the venv
   ```bash
   which python
   # Should show: /Users/<USER>/projects/axon-pfc/venv/bin/python
   ```

3. **Check all imports manually**:
   ```bash
   cd /Users/<USER>/projects/axon-pfc
   source venv/bin/activate
   python -c "from agents import Agent, Runner, ModelSettings; print('✅ OpenAI Agents SDK OK')"
   python -c "from docx import Document; print('✅ python-docx OK')"
   python -c "import sys; sys.path.insert(0, 'src/backend'); from services.workflow_json_generator import get_workflow_json_generator; print('✅ Service OK')"
   ```

4. **Restart everything**:
   ```bash
   # Kill backend if running
   pkill -f "python.*main.py"
   
   # Restart fresh
   cd /Users/<USER>/projects/axon-pfc
   source venv/bin/activate
   cd src/backend
   python main.py
   ```

5. **Check the actual error**: Look at the specific error message in:
   - Browser DevTools Console
   - Backend terminal output
   - Network tab in DevTools (shows actual HTTP error)

