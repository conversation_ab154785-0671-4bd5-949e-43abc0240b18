# AI Workflow Specification Generator

## Overview

The AI Workflow Specification Generator is a new feature that uses OpenAI's GPT models to automatically generate comprehensive workflow specification documents based on natural language descriptions.

## Features

- **Natural Language Input**: Simply describe what you want the workflow to do
- **Real-time Streaming**: Watch the AI's thought process as it analyzes and generates specifications
- **Built-in Web Search**: GPT-5 automatically searches the web for best practices and current standards
- **Professional Word Documents**: Download polished specification documents in .docx format
- **Polling Architecture**: Simple polling every second for status updates (no WebSocket complexity)
- **GPT-5 Powered**: Uses OpenAI's latest model with enhanced reasoning capabilities

## Architecture

### Backend Components

#### 1. Service: `workflow_spec_generator.py`

Located at: `src/backend/services/workflow_spec_generator.py`

**Key Features:**
- Task-based async processing
- In-memory task storage (can be moved to Redis for production)
- OpenAI integration with custom tools
- Word document generation using python-docx

**Main Classes:**
- `WorkflowSpecGenerator`: Main service class that handles all generation logic
  - `create_task()`: Initialize a new generation task
  - `generate_specification()`: Main generation method (runs in background thread)
  - `_web_search_tool_definition()`: Defines the web search tool for OpenAI
  - `_generate_word_document()`: Creates formatted Word documents

**Task States:**
- `processing`: Task is currently running
- `completed`: Task finished successfully
- `error`: Task encountered an error

**Progress Tracking:**
- Progress is tracked from 0-100%
- Messages are logged with timestamps and types (thinking, tool_call, success, error, info)

#### 2. Routes: `workflow_spec.py`

Located at: `src/backend/routes/workflow_spec.py`

**API Endpoints:**

1. **POST `/api/workflow-spec/generate`**
   - Start a new specification generation
   - Request body: `{ "workflow_request": "string" }`
   - Response: `{ "task_id": "uuid", "status": "processing", "message": "..." }`

2. **GET `/api/workflow-spec/status/{task_id}`**
   - Poll for task status updates
   - Returns full task state including messages, progress, and download URL when complete
   - Response includes:
     - `id`: Task ID
     - `status`: Current status
     - `messages`: Array of agent messages
     - `progress`: Progress percentage (0-100)
     - `download_url`: Available when status is "completed"

3. **GET `/api/workflow-spec/download/{task_id}`**
   - Download the generated Word document
   - Returns the .docx file with appropriate headers

### Frontend Component

#### `WorkflowSpecGenerator` Component

Located in: `src/frontend/app.js`

**Features:**
- Clean, intuitive input form
- Real-time progress bar with color coding
- Message stream showing AI's thought process
- Automatic polling every second when active
- Beautiful download section when complete
- Easy reset to generate another specification

**User Flow:**
1. User enters workflow description (up to 2000 characters)
2. Clicks "Generate Specification"
3. Polling begins automatically
4. Progress bar and messages update in real-time
5. When complete, download button appears
6. User can download Word document or generate another

**Polling Implementation:**
```javascript
React.useEffect(() => {
    if (!taskId) return;
    
    const pollStatus = async () => {
        const statusData = await api.get(`/api/workflow-spec/status/${taskId}`);
        setStatus(statusData);
        
        if (statusData.status === 'completed' || statusData.status === 'error') {
            setGenerating(false);
        }
    };
    
    pollStatus();
    const interval = setInterval(pollStatus, 1000);
    
    return () => clearInterval(interval);
}, [taskId]);
```

## Generated Document Structure

The Word document includes:

1. **Title Page**: "Workflow Specification"
2. **Metadata**: Generation timestamp and original request
3. **Overview**: High-level description of the workflow
4. **Workflow Steps**: Numbered list of detailed steps
5. **Risk Assessment**: Identified risks with severity levels and mitigation strategies
6. **Required Tools & Resources**: List of necessary tools
7. **Success Metrics**: Measurable success criteria
8. **Implementation Timeline**: Estimated timeline
9. **Recommendations**: Additional suggestions and best practices

## Configuration

### Environment Variables

- `OPENAI_API_KEY`: Your OpenAI API key (required)
- `OPENAI_MODEL`: Model to use (default: "gpt-5")
  - Options: `gpt-5` (recommended), `gpt-5-mini`, `gpt-5-nano`
  - Fallback: `gpt-4o` if GPT-5 not available

### GPT-5 Features

The system now uses **GPT-5** with enhanced capabilities:

- **Advanced Reasoning**: Deeper analysis with configurable reasoning effort
- **Better Accuracy**: More reliable and factually accurate specifications
- **Improved Tool Usage**: Smarter web search integration
- **Structured Output**: Native JSON object formatting
- **Adaptive Design**: Automatically adjusts to request complexity

For detailed GPT-5 configuration and features, see [GPT5_UPGRADE.md](./GPT5_UPGRADE.md)

### File Storage

Generated documents are temporarily stored in: `temp/workflow_specs/`

**Note**: This directory is added to `.gitignore` and should be cleaned periodically in production.

## Example Usage

### Example Request:
```
Take an IC memo and produce a structured assessment of risks
```

### Generated Output:
The AI will:
1. Analyze the requirement
2. Search for relevant risk assessment frameworks
3. Break down the workflow into steps
4. Identify potential risks
5. Suggest tools and metrics
6. Generate a comprehensive Word document

### Sample Message Stream:
```
INFO: Starting analysis of workflow request...
THINKING: Analyzing workflow requirements and researching best practices...
TOOL CALL: Searching web: IC memo risk assessment frameworks
THINKING: Synthesizing research findings and drafting specification...
THINKING: Parsing specification structure...
THINKING: Generating Word document...
SUCCESS: Specification generated successfully!
```

## Integration

The feature is fully integrated into the main navigation:

```
Workflows | Dashboard | 🤖 AI Spec Generator
```

Click the "🤖 AI Spec Generator" button to access the tool.

## Performance

- **Generation Time**: Typically 15-45 seconds depending on complexity
- **Polling Frequency**: Every 1 second
- **Document Size**: Usually 2-5 pages for standard workflows
- **Concurrent Tasks**: Supports multiple simultaneous generations

## Future Enhancements

1. **Real Web Search**: Integrate with Serper API or Brave Search for actual web research
2. **Redis Task Storage**: Move from in-memory to Redis for scalability
3. **Template Customization**: Allow users to choose document templates
4. **History**: Save and view previously generated specifications
5. **Export Formats**: Support PDF, Markdown, and HTML exports
6. **Collaboration**: Share specifications with team members
7. **AI Model Selection**: Let users choose between GPT-4, Claude, etc.
8. **Cost Tracking**: Monitor and display API costs per generation

## Troubleshooting

### Common Issues

**Issue**: "Cannot find implementation or library stub for module named 'docx'"
**Solution**: Run `pip install python-docx` in your virtual environment

**Issue**: Generation fails with OpenAI error
**Solution**: Check that `OPENAI_API_KEY` is set in your `.env` file

**Issue**: Documents not downloading
**Solution**: Ensure the `temp/workflow_specs/` directory exists and has write permissions

**Issue**: Polling stops prematurely
**Solution**: Check browser console for network errors; verify backend is running

## Security Considerations

1. **Input Validation**: Requests are limited to 2000 characters
2. **Rate Limiting**: Should be implemented in production
3. **File Cleanup**: Old documents should be deleted after 24 hours
4. **API Key Protection**: Never expose OpenAI API key to frontend
5. **UUID Filenames**: Prevents path traversal attacks

## Testing

To test the complete flow:

1. Start the backend: `cd src/backend && python main.py`
2. Open frontend: `http://localhost:8000/`
3. Navigate to "AI Spec Generator"
4. Enter a test request: "Create a workflow for customer onboarding"
5. Watch the progress and download the result

## Dependencies

### Python Packages
- `python-docx>=1.1.0`: Word document generation
- `openai>=1.0.0`: OpenAI API integration
- `fastapi>=0.104.0`: API framework
- `pydantic>=2.0.0`: Data validation

### Frontend
- React 18 (from CDN)
- Tailwind CSS (from CDN)
- No build process required

## API Reference

### Request Model
```python
class GenerateRequest(BaseModel):
    workflow_request: str  # min: 10 chars, max: 2000 chars
```

### Response Models
```python
class GenerateResponse(BaseModel):
    task_id: str
    status: str
    message: str

class TaskStatus(BaseModel):
    id: str
    status: str
    workflow_request: str
    messages: list
    progress: int
    created_at: str
    completed_at: str | None
    error: str | None
    download_url: str | None
```

## Maintenance

### Regular Tasks
- Clean up old documents in `temp/workflow_specs/`
- Monitor OpenAI API usage and costs
- Review and improve system prompts based on user feedback
- Update web search integration when ready

### Monitoring
- Track generation success/failure rates
- Monitor average generation times
- Log API errors for troubleshooting
- Collect user feedback on specification quality

## Credits

Built with:
- **OpenAI GPT-5** for AI generation (with GPT-4o fallback)
- python-docx for document creation
- FastAPI for backend API
- React for frontend UI

## See Also

- [GPT-5 Upgrade Documentation](./GPT5_UPGRADE.md) - Detailed GPT-5 features and configuration
- [Workflow Guide](./workflow-guide.md) - General workflow documentation
- [API Reference](./api-reference.md) - Complete API documentation

