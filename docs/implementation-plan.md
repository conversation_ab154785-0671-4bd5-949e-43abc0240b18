# Evaluation System Implementation Plan

**Version:** 2.0 (Revised)  
**Last Updated:** 2025-10-15  
**Status:** Ready for Implementation

---

## 🎯 Overview

Build a comprehensive evaluation and testing system for workflows that:
- Stores test case definitions with expected inputs/outputs
- Executes test cases via the existing workflow execution system
- Validates results and tracks pass/fail rates
- Provides real-time monitoring dashboard
- Manages everything via start.sh/stop.sh scripts

**Key Design Principle:** Extend existing `flow_execution` schema rather than duplicate it.

---

## 📊 Database Schema (Minimal Design)

### New Schema: `evaluation`

#### 1. eval_case (Test Definitions)
```sql
CREATE TABLE evaluation.eval_case (
    id UUID PRIMARY KEY,
    workflow_id UUID REFERENCES agentic_objects.flow(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    input_variables JSONB NOT NULL,     -- Test inputs
    expected_output JSONB,               -- Expected outputs
    validation_rules JSONB,              -- Custom validation rules
    tags TEXT[],                         -- For categorization
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES users.user(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(workflow_id, name)
);
```

#### 2. eval_run (Links to Executions + Validation)
```sql
CREATE TABLE evaluation.eval_run (
    id UUID PRIMARY KEY,
    eval_case_id UUID REFERENCES evaluation.eval_case(id),
    flow_execution_id UUID UNIQUE REFERENCES flow_execution.flow_execution(id),
    batch_run_id UUID REFERENCES evaluation.eval_batch_run(id),
    
    -- ONLY eval-specific fields (execution state comes from flow_execution)
    passed BOOLEAN,
    validation_output JSONB,
    validation_notes TEXT,
    evaluated_at TIMESTAMP
);
```

**Note:** Status, timing, I/O all come from `flow_execution` via foreign key!

#### 3. eval_suite (Test Grouping)
```sql
CREATE TABLE evaluation.eval_suite (
    id UUID PRIMARY KEY,
    workflow_id UUID REFERENCES agentic_objects.flow(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_by UUID REFERENCES users.user(id),
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(workflow_id, name)
);

CREATE TABLE evaluation.eval_suite_cases (
    suite_id UUID REFERENCES evaluation.eval_suite(id),
    eval_case_id UUID REFERENCES evaluation.eval_case(id),
    order_index INTEGER,
    PRIMARY KEY (suite_id, eval_case_id)
);
```

#### 4. eval_batch_run (Batch Tracking)
```sql
CREATE TABLE evaluation.eval_batch_run (
    id UUID PRIMARY KEY,
    workflow_id UUID REFERENCES agentic_objects.flow(id),
    suite_id UUID REFERENCES evaluation.eval_suite(id),
    name VARCHAR(255),
    started_by UUID REFERENCES users.user(id),
    started_at TIMESTAMP DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'RUNNING'
);
```

### Key Views

```sql
-- Complete eval run info (joins with flow_execution)
CREATE VIEW evaluation.eval_run_detail AS
SELECT 
    er.id, er.passed, er.validation_notes,
    fe.result as status, fe.created_at, fe.updated_at,
    fe.variables as outputs, ec.name as test_name
FROM evaluation.eval_run er
JOIN flow_execution.flow_execution fe ON er.flow_execution_id = fe.id
JOIN evaluation.eval_case ec ON er.eval_case_id = ec.id;

-- Test case statistics
CREATE VIEW evaluation.eval_case_stats AS
SELECT 
    ec.id, ec.name,
    COUNT(er.id) as total_runs,
    COUNT(*) FILTER (WHERE er.passed = true) as passed_runs,
    ROUND(AVG(...) * 100, 2) as success_rate
FROM evaluation.eval_case ec
LEFT JOIN evaluation.eval_run er ON ec.id = er.eval_case_id
GROUP BY ec.id;
```

**See:** `migrations/add_evaluation_schema_revised.sql` for complete schema

---

## 🏗️ Architecture

```
Frontend (React)
    ├── Workflow Page
    │   ├── Eval Cases Tab (CRUD test cases)
    │   └── Eval History Tab (past runs)
    └── Dashboard Page
        ├── System Health
        ├── Queue Status (RabbitMQ)
        ├── Active Runs
        └── Recent Completions
        
Backend API (FastAPI)
    ├── /api/workflows/{id}/eval-cases (CRUD)
    ├── /api/eval-cases/{id}/run (execute)
    ├── /api/dashboard/status (real-time)
    └── /api/eval-runs (history)
    
Services
    ├── EvalRunner (execute cases)
    ├── EvalMonitor (background daemon)
    └── RabbitMQMonitor (queue stats)
    
Database
    ├── evaluation schema (new)
    ├── flow_execution schema (existing)
    └── agentic_objects schema (existing)
    
Infrastructure
    ├── Docker Compose (agentic-core)
    ├── start.sh (start all services)
    └── stop.sh (stop all services)
```

---

## 📝 Implementation Phases

### Phase 1: Database & Backend Foundation (Week 1)

**Day 1-2: Database**
- [ ] Run migration: `psql -f migrations/add_evaluation_schema_revised.sql`
- [ ] Verify tables and views created
- [ ] Test sample queries

**Day 3-4: Python Models & Basic API**
- [ ] Create `src/backend/models/evaluation.py`
  ```python
  class EvalCase(Base):
      __tablename__ = "eval_case"
      __table_args__ = {"schema": "evaluation"}
      # ... fields ...
      
      @property
      def workflow(self):
          return relationship("Flow")
  
  class EvalRun(Base):
      __tablename__ = "eval_run"
      __table_args__ = {"schema": "evaluation"}
      
      flow_execution = relationship("FlowExecution")
      
      @property
      def status(self):
          return self.flow_execution.result
      
      @property
      def duration_ms(self):
          if self.flow_execution.updated_at:
              delta = self.flow_execution.updated_at - self.flow_execution.created_at
              return int(delta.total_seconds() * 1000)
          return None
  ```

- [ ] Create `src/backend/routes/evaluation.py`
  ```python
  @router.get("/workflows/{workflow_id}/eval-cases")
  def list_eval_cases(workflow_id: UUID, db: Session = Depends(get_db)):
      return db.query(EvalCase).filter_by(workflow_id=workflow_id).all()
  
  @router.post("/workflows/{workflow_id}/eval-cases")
  def create_eval_case(workflow_id: UUID, data: EvalCaseCreate, ...):
      # ... create eval case ...
  
  @router.post("/eval-cases/{id}/run")
  async def run_eval_case(id: UUID, db: Session = Depends(get_db)):
      # Use existing WorkflowRunProcessor from process_runs.py
      runner = EvalRunner(db)
      eval_run_id = await runner.run_eval_case(id)
      return {"eval_run_id": eval_run_id}
  ```

- [ ] Add router to `src/backend/main.py`

**Day 5: EvalRunner Service**
- [ ] Create `src/backend/services/eval_runner.py`
  ```python
  class EvalRunner:
      async def run_eval_case(self, eval_case_id: UUID) -> UUID:
          # 1. Get case
          eval_case = self.db.query(EvalCase).get(eval_case_id)
          
          # 2. Create flow execution (reuse existing logic)
          flow_exec_id = self.processor.create_flow_execution(
              workflow_id=eval_case.workflow_id,
              variables=eval_case.input_variables
          )
          
          # 3. Create eval_run (just the link)
          eval_run = EvalRun(
              eval_case_id=eval_case_id,
              flow_execution_id=flow_exec_id
          )
          self.db.add(eval_run)
          self.db.commit()
          
          # 4. Publish to RabbitMQ
          await self.processor.publish_run_message(flow_exec_id)
          
          return eval_run.id
      
      async def validate_eval_run(self, eval_run_id: UUID):
          eval_run = self.db.query(EvalRun).get(eval_run_id)
          
          if eval_run.flow_execution.result != 'COMPLETE':
              return
          
          # Compare expected vs actual
          expected = eval_run.eval_case.expected_output
          actual = eval_run.flow_execution.variables
          
          passed = self._validate(expected, actual)
          
          eval_run.passed = passed
          eval_run.evaluated_at = datetime.utcnow()
          self.db.commit()
  ```

### Phase 2: Monitoring & Dashboard (Week 2)

**Day 1-2: Background Monitor**
- [ ] Create `src/backend/services/eval_monitor.py`
  ```python
  class EvalMonitor:
      async def start(self):
          while self.running:
              await self.check_pending_validations()
              await asyncio.sleep(1)
      
      async def check_pending_validations(self):
          # Find completed executions not yet validated
          pending = self.db.query(EvalRun).join(FlowExecution).filter(
              FlowExecution.result.in_(['COMPLETE', 'FAILED']),
              EvalRun.evaluated_at.is_(None)
          ).all()
          
          for eval_run in pending:
              await self.runner.validate_eval_run(eval_run.id)
  ```

- [ ] Create `src/backend/services/eval_monitor_daemon.py`
  ```python
  if __name__ == "__main__":
      monitor = EvalMonitor()
      asyncio.run(monitor.start())
  ```

**Day 3-4: Dashboard API**
- [ ] Create `src/backend/services/rabbitmq_monitor.py`
  ```python
  async def get_rabbitmq_queue_stats() -> Dict:
      # Query RabbitMQ for queue depths
      connection = await aio_pika.connect(os.getenv("RABBITMQ_URL"))
      channel = await connection.channel()
      
      stats = {}
      for queue_name in ["agentic.flyweight", "agentic.async"]:
          queue = await channel.get_queue(queue_name, passive=True)
          stats[queue_name] = {
              "pending": queue.declaration_result.message_count
          }
      
      await connection.close()
      return stats
  ```

- [ ] Add dashboard endpoint
  ```python
  @router.get("/dashboard/status")
  async def get_dashboard_status(db: Session = Depends(get_db)):
      # Active eval runs (join with flow_execution)
      active = db.query(EvalRun).join(FlowExecution).filter(
          FlowExecution.result.in_(['PENDING', 'RUNNING'])
      ).all()
      
      # Recent completions
      recent = db.query(EvalRun).join(FlowExecution).filter(
          FlowExecution.result.in_(['COMPLETE', 'FAILED'])
      ).order_by(FlowExecution.updated_at.desc()).limit(10).all()
      
      return {
          "timestamp": datetime.utcnow(),
          "active_runs": [format_run(r) for r in active],
          "recent_completions": [format_run(r) for r in recent],
          "queues": await get_rabbitmq_queue_stats(),
          "system_health": await check_system_health()
      }
  ```

**Day 5: Testing**
- [ ] Test eval case creation
- [ ] Test single case execution
- [ ] Test validation logic
- [ ] Test dashboard endpoint

### Phase 3: Frontend (Week 3)

**Day 1-2: Eval Cases Tab**
- [ ] Create `src/frontend/components/EvalCasesTab.js`
  - Table with test cases
  - Create/edit modal
  - Bulk select + run
  - Success rate display

- [ ] Create `src/frontend/components/CreateEvalCaseModal.js`
  - Form for name, description
  - Variable editor (JSON or form-based)
  - Expected output (optional)
  - Tags input

**Day 3-4: Dashboard Page**
- [ ] Create `src/frontend/pages/Dashboard.js`
  ```jsx
  export function Dashboard() {
      const [status, setStatus] = useState(null);
      
      useEffect(() => {
          const fetchStatus = async () => {
              const res = await fetch('/api/dashboard/status');
              setStatus(await res.json());
          };
          
          fetchStatus();
          const interval = setInterval(fetchStatus, 1000);
          return () => clearInterval(interval);
      }, []);
      
      return (
          <div className="dashboard">
              <SystemHealthPanel health={status?.system_health} />
              <QueueStatusPanel queues={status?.queues} />
              <ActiveRunsPanel runs={status?.active_runs} />
              <RecentCompletionsPanel completions={status?.recent_completions} />
          </div>
      );
  }
  ```

- [ ] Create panel components
- [ ] Style with CSS
- [ ] Add animations

**Day 5: Polish**
- [ ] Add loading states
- [ ] Add error handling
- [ ] Test responsiveness
- [ ] Add keyboard shortcuts

### Phase 4: Scripts & Integration (Week 4)

**Day 1-2: Update Scripts**
- [ ] Update `start.sh`
  ```bash
  #!/bin/bash
  echo "🚀 Starting Axon PFC System..."
  
  # 1. Start agentic-core
  cd ../agentic-core
  docker compose up -d
  
  # 2. Discover ports
  PG_PORT=$(docker compose port postgres 5432 | cut -d: -f2)
  RABBIT_PORT=$(docker compose port rabbitmq 5672 | cut -d: -f2)
  
  # 3. Update .env
  cd ../axon-pfc
  cat > .env << EOF
  DATABASE_URL=postgresql://postgres:postgres@localhost:$PG_PORT/invisible
  RABBITMQ_URL=amqp://guest:guest@localhost:$RABBIT_PORT/
  EOF
  
  # 4. Start backend
  source venv/bin/activate
  python src/backend/main.py > logs/backend.log 2>&1 &
  echo $! > .backend.pid
  
  # 5. Start eval monitor
  python src/backend/services/eval_monitor_daemon.py > logs/eval_monitor.log 2>&1 &
  echo $! > .eval_monitor.pid
  
  # 6. Start frontend
  cd src/frontend
  npm start > ../../logs/frontend.log 2>&1 &
  echo $! > ../../.frontend.pid
  
  echo "✨ System started!"
  echo "   Frontend:  http://localhost:3000"
  echo "   Backend:   http://localhost:8000"
  echo "   Dashboard: http://localhost:3000/dashboard"
  ```

- [ ] Update `stop.sh`
  ```bash
  #!/bin/bash
  
  # Stop all services
  [ -f .frontend.pid ] && kill $(cat .frontend.pid) && rm .frontend.pid
  [ -f .eval_monitor.pid ] && kill $(cat .eval_monitor.pid) && rm .eval_monitor.pid
  [ -f .backend.pid ] && kill $(cat .backend.pid) && rm .backend.pid
  
  # Stop containers
  cd ../agentic-core && docker compose down
  
  echo "✅ All services stopped"
  ```

**Day 3: Testing**
- [ ] End-to-end test: Create case → Run → View results
- [ ] Test with 10+ concurrent cases
- [ ] Test dashboard under load
- [ ] Test restart scenarios

**Day 4: Documentation**
- [ ] User guide for creating tests
- [ ] API documentation
- [ ] Troubleshooting guide

**Day 5: Deployment**
- [ ] Code review
- [ ] Deploy to production
- [ ] Monitor initial usage

---

## 🚀 Quick Start (For Development)

### 1. Run Migration
```bash
cd /Users/<USER>/projects/axon-pfc
export PGPORT=$(cd ../agentic-core && docker compose port postgres 5432 | cut -d: -f2)
psql -h localhost -p $PGPORT -U postgres -d invisible -f migrations/add_evaluation_schema_revised.sql
```

### 2. Test API
```python
# test_eval_api.py
import requests

# Create eval case
response = requests.post(
    "http://localhost:8000/api/workflows/YOUR_WORKFLOW_ID/eval-cases",
    json={
        "name": "Test Case 1",
        "input_variables": [
            {"variable_name": "topic", "variable_value": "test", "variable_type": "String"}
        ],
        "expected_output": {"greeting_message": "Welcome to test!"},
        "tags": ["smoke-test"]
    }
)
print(response.json())

# Run it
case_id = response.json()["id"]
run_response = requests.post(f"http://localhost:8000/api/eval-cases/{case_id}/run")
print(run_response.json())
```

### 3. Start System
```bash
./start.sh
```

---

## 📦 Dependencies

### Backend
- FastAPI / Flask (existing)
- SQLAlchemy + Alembic
- aio-pika (already added)
- Python 3.9+

### Frontend
- React (existing)
- React Router
- CSS modules

### Infrastructure
- PostgreSQL (from agentic-core)
- RabbitMQ (from agentic-core)
- Docker Compose

---

## ✅ Success Criteria

1. ✅ System starts/stops with single command
2. ✅ Can create eval cases via UI
3. ✅ Can run eval cases and see results
4. ✅ Dashboard updates in real-time (< 2s latency)
5. ✅ System handles 50+ concurrent eval runs
6. ✅ No data duplication with flow_execution

---

## 🔑 Key Design Decisions

### Why Minimal eval_run Table?
- **Single source of truth:** Execution state lives in `flow_execution`
- **Consistency:** Can't get out of sync
- **Flexibility:** Easy to query all executions (eval + manual)
- **Storage:** 60% smaller than duplicated design

### Why Separate Schema?
- **Isolation:** Evaluation concerns separate from core workflow engine
- **Evolution:** Can change eval features without touching core
- **Clarity:** Clear which tables are for testing vs production use

### Why Background Monitor?
- **Separation:** Validation logic separate from execution
- **Resilience:** Can restart validation without re-running workflows
- **Async:** Doesn't block API requests

---

## 🎯 Future Enhancements

- **Advanced Validation:** JSON schema, regex, semantic similarity
- **Test Generation:** Auto-generate cases from workflow definition
- **Regression Detection:** Alert when passing tests start failing
- **Performance Tracking:** Track execution time trends
- **CI/CD Integration:** GitHub Actions to run tests on PR
- **Notifications:** Slack/email alerts on failures
- **Live Logs:** Stream execution logs in dashboard
- **Export Reports:** PDF/CSV export of results

---

## 📊 Estimated Timeline

- **Phase 1 (Database & Backend):** 40 hours
- **Phase 2 (Monitoring):** 32 hours
- **Phase 3 (Frontend):** 32 hours
- **Phase 4 (Integration):** 24 hours

**Total:** ~4 weeks (128 hours)

---

## 📝 Checklist

**Database:**
- [ ] Run migration
- [ ] Verify tables created
- [ ] Test views

**Backend:**
- [ ] Python models
- [ ] CRUD endpoints
- [ ] EvalRunner service
- [ ] EvalMonitor daemon
- [ ] Dashboard API
- [ ] RabbitMQ integration

**Frontend:**
- [ ] Eval Cases tab
- [ ] Dashboard page
- [ ] Panel components
- [ ] Styling
- [ ] Route setup

**Scripts:**
- [ ] Update start.sh
- [ ] Update stop.sh
- [ ] Test startup
- [ ] Test shutdown

**Testing:**
- [ ] Unit tests
- [ ] Integration tests
- [ ] Load tests
- [ ] End-to-end test

**Documentation:**
- [ ] User guide
- [ ] API docs
- [ ] Troubleshooting

---

**Ready to implement!** Start with Phase 1, Day 1. 🚀

