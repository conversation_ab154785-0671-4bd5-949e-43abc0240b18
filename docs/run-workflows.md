# Running Workflows in Parallel

This document explains how to use the workflow parallel execution scripts.

## Overview

The scripts in this directory allow you to:

1. **Create workflow runs** from an existing workflow in the database
2. **Publish run messages** to RabbitMQ for processing
3. **Process runs in parallel** with controlled concurrency
4. **Monitor execution** progress in real-time

## Prerequisites

### 1. Services Must Be Running

Make sure these services are running:

```bash
# Start PostgreSQL and RabbitMQ
cd ../agentic-core
docker compose up -d postgres rabbitmq

# Start the async engine workers
docker compose up -d async-engine-flyweight async-engine-async async-engine-cpu
```

### 2. Database Must Have Workflows

You need a workflow named "test" in the database. You can create one using the Axon-PFC web interface at http://localhost:8080.

The example workflow should be a simple team workflow. See `src/backend/example_team.json` for an example.

### 3. Environment Variables

Make sure your `.env` file is configured:

```bash
AGENTIC_CORE_PATH=../agentic-core
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/invisible
RABBITMQ_URL=amqp://guest:guest@localhost:5672/
```

## Scripts

### 1. `run_workflow_parallel.py` (Recommended)

**All-in-one script** that creates runs, publishes them to RabbitMQ, and monitors their execution.

```bash
python run_workflow_parallel.py
```

**Features:**
- Creates multiple flow executions with different input sets
- Publishes them to RabbitMQ with controlled concurrency
- Monitors execution progress in real-time
- Shows completion status

**Customization:**

Edit the script to change:
- `workflow_name`: Name of the workflow to run (default: "test")
- `max_concurrent_runs`: How many runs to publish simultaneously (default: 3)
- `input_sets`: Array of input variable sets for each run

```python
input_sets = [
    {"topic": "space exploration"},
    {"topic": "ocean adventure"},
    {"topic": "mountain hiking"},
]
```

### 2. `process_runs.py`

**Publisher only** - creates runs and publishes messages to RabbitMQ.

```bash
python process_runs.py
```

**Use case:** When you want to just queue up work and let the workers process it independently.

### 3. `run_processor_worker.py`

**Worker only** - consumes messages from RabbitMQ and processes them.

```bash
python run_processor_worker.py
```

**Use case:** For testing or debugging message processing. Note that this is a simplified worker - for production, use the actual `async-engine-worker` service from agentic-core.

## How It Works

### Architecture

```
┌─────────────────────┐
│  run_workflow_      │
│  parallel.py        │
└──────────┬──────────┘
           │
           │ 1. Create FlowExecution records
           ▼
    ┌──────────────┐
    │  PostgreSQL  │
    │  Database    │
    └──────────────┘
           │
           │ 2. Publish StartFlowExecution messages
           ▼
    ┌──────────────┐
    │  RabbitMQ    │
    │  (agentic    │
    │  exchange)   │
    └──────┬───────┘
           │
           │ 3. Workers consume messages
           ▼
    ┌──────────────┐
    │ async-engine │
    │ workers      │
    │ (flyweight,  │
    │  async, cpu) │
    └──────┬───────┘
           │
           │ 4. Process workflows
           ▼
    ┌──────────────┐
    │  Update      │
    │  execution   │
    │  status      │
    └──────────────┘
```

### Message Flow

1. **Script creates FlowExecution** records in the database with `result=pending`
2. **Script publishes StartFlowExecutionMessage** to RabbitMQ (`agentic.flyweight` queue)
3. **Flyweight worker** picks up the message and starts the flow
4. **Worker creates NextFlowStepMessage** for the first step
5. **Appropriate worker** (async for teams, cpu for delegates) processes the step
6. **Worker updates** the database with execution results
7. **Script monitors** by polling the database for status changes

### Concurrency Control

The scripts use an `asyncio.Semaphore` to control how many runs are processed concurrently:

```python
semaphore = asyncio.Semaphore(max_concurrent_runs)

async with semaphore:
    # Only max_concurrent_runs tasks can be here at once
    await process_run(...)
```

This prevents overwhelming RabbitMQ or the database with too many simultaneous operations.

## Monitoring Runs

### View in Database

```bash
# Connect to PostgreSQL
psql postgresql://postgres:postgres@localhost:5432/invisible

# View flow executions
SELECT id, result, failure_reason, start_time, end_time
FROM flow_execution.flow_execution
ORDER BY start_time DESC
LIMIT 10;

# View execution steps
SELECT id, entity_type, result, start_time, end_time
FROM flow_execution.flow_execution_step
WHERE flow_execution_id = 'your-execution-id';
```

### View in GraphQL

Open http://localhost:5002/graphql/ and use:

```graphql
query {
  flowExecutionStatus(flow_execution_id: "your-execution-id") {
    id
    result
    failure_reason
    start_time
    end_time
    steps {
      id
      result
      entity_type
      start_time
      end_time
    }
  }
}
```

### View RabbitMQ Queues

Open http://localhost:15672/ (guest/guest) to see message queues and throughput.

## Troubleshooting

### Issue: "Workflow 'test' not found"

**Solution:** Create a workflow named "test" using the web interface or API:

```bash
curl -X POST http://localhost:8000/api/workflows \
  -H "Content-Type: application/json" \
  -d @src/backend/example_team.json
```

### Issue: "No tenant found in database"

**Solution:** Make sure agentic-core is properly initialized. Run migrations:

```bash
cd ../agentic-core
docker compose exec postgres psql -U postgres -c "CREATE DATABASE invisible;"
# Run the agentic-core services to trigger migrations
docker compose up -d graphql
```

### Issue: Runs stay in "pending" status

**Causes:**
1. Workers are not running
2. RabbitMQ connection failed
3. Messages are going to wrong queue

**Solution:**
```bash
# Check if workers are running
docker compose ps

# Check worker logs
docker compose logs async-engine-flyweight
docker compose logs async-engine-async

# Check RabbitMQ queues
# Open http://localhost:15672/ and verify messages are in the queues
```

### Issue: Runs fail immediately

**Causes:**
1. Workflow definition is invalid
2. Input variables don't match workflow requirements
3. Database constraints violated

**Solution:**
```bash
# Check execution logs
SELECT id, result, failure_reason, failure_detail
FROM flow_execution.flow_execution
WHERE result = 'failed';

# Check worker logs
docker compose logs async-engine-flyweight | grep ERROR
```

## Customizing for Your Workflows

### Different Workflow Name

Change the workflow name in the script:

```python
runner = WorkflowParallelRunner(
    workflow_name="your_workflow_name",  # Change this
    max_concurrent_runs=3
)
```

### Different Input Variables

The input variables depend on your workflow's variable definitions. Check your workflow's input variables and provide them:

```python
input_sets = [
    {
        "variable_name_1": "value1",
        "variable_name_2": "value2",
    },
    {
        "variable_name_1": "value3",
        "variable_name_2": "value4",
    },
]
```

### Adjust Concurrency

Change the semaphore limit:

```python
runner = WorkflowParallelRunner(
    workflow_name="test",
    max_concurrent_runs=5  # Process 5 at a time
)
```

**Note:** Higher concurrency requires more:
- Database connections
- RabbitMQ connections
- Worker capacity
- Memory

Start with 3 and increase gradually based on your system's capacity.

## Production Considerations

These scripts are for **demonstration and development** purposes. For production:

1. **Use the actual async-engine-worker** from agentic-core instead of the simplified worker
2. **Add proper error handling** and retry logic
3. **Use persistent message storage** (RabbitMQ with durable queues)
4. **Monitor** worker health and queue depths
5. **Scale workers** based on load
6. **Add authentication** for RabbitMQ and database
7. **Use connection pooling** for database access
8. **Implement circuit breakers** for external service calls
9. **Add distributed tracing** for debugging
10. **Set up alerts** for failed executions

## Example Workflow: Team Greeting

The example "test" workflow demonstrates a simple team-based workflow:

```json
{
  "description": "A workflow that uses a team of agents to create greetings",
  "variables": {
    "input": {
      "topic": {
        "type": "string",
        "default": "a wonderful day"
      }
    },
    "output": {
      "greeting_message": {
        "type": "string"
      }
    }
  },
  "steps": [
    {
      "order": 1,
      "type": "team",
      "team": {
        "name": "Greeting Team",
        "goal": "Create a creative greeting based on the topic",
        "agents": [...]
      }
    }
  ]
}
```

The script provides different topics as input:
- "space exploration"
- "ocean adventure"
- "mountain hiking"
- etc.

Each run will create a different greeting based on the topic.

## Next Steps

1. **Create your own workflow** in the web interface
2. **Modify the input_sets** to match your workflow's variables
3. **Run the script** and monitor execution
4. **Scale up** by increasing concurrency as needed
5. **Integrate** into your application or CI/CD pipeline

## Support

For issues with:
- **These scripts**: Check this README and the script comments
- **agentic-core**: See the agentic-core documentation
- **Database**: Check PostgreSQL logs
- **RabbitMQ**: Check RabbitMQ management interface

Happy workflow orchestration! 🚀

