# Agentic-Core Logging Fix: Event Loop Closed Error

## Problem

Workflows were running but logs were not visible in `/artifacts/{execution_id}/flow-execution.log`. The log files only contained `HEARTBEAT` messages instead of detailed execution events.

### Root Cause

Docker logs revealed the actual issue:
```
PUBLISH_ATTEMPT_FAILED: 'flow_event.ac685810-eaa1-402c-a3bb-fa1874d164cd.team_chatter' on 'flow_events_exchange' (attempt 1): Event loop is closed
```

**The Problem:** When Celery workers fork, they inherit the parent process's `TopicManager` singleton, which was created with an event loop. When the child process starts, that inherited event loop is closed, causing all publish attempts to fail with "Event loop is closed" errors.

**Why HEARTBEAT messages still appeared:** HEARTBEAT messages are generated by the GraphQL Log Writer subscriber (not by workers), so they were unaffected by the fork issue.

## Solution Applied

### File: `/Users/<USER>/projects/agentic-core/backend/lib/async_engine/messaging/backends/celery_backend.py`

Added a Celery `worker_process_init` signal handler to reset TopicManager singletons after each worker fork:

```python
from celery.signals import worker_process_init

class CeleryBackend(IMessagingBackend):
    def __init__(self, config: MessagingConfig):
        self.config = config
        self.app = Celery('async_engine')
        self._configure_celery()
        self._register_tasks()
        self._register_fork_handler()  # ← New
    
    def _register_fork_handler(self):
        """Register handler to reset TopicManagers after worker fork"""
        logger = logging.getLogger(__name__)
        
        @worker_process_init.connect
        def reset_event_publishers(**kwargs):
            """
            Reset TopicManager singletons after fork to avoid "Event loop is closed" errors.
            
            When Celery workers fork, they inherit the parent's TopicManager with a closed
            event loop. This handler ensures each worker creates a fresh TopicManager with
            its own event loop.
            """
            logger.info("WORKER_FORK: Resetting TopicManager singletons for new worker process")
            
            # Reset Flow Execution Event Publisher
            try:
                from messaging.flow_events.flow_execution_event_publisher import FlowExecutionEventPublisher
                FlowExecutionEventPublisher._topic_manager = None
                logger.info("WORKER_FORK: FlowExecutionEventPublisher reset successfully")
            except Exception as e:
                logger.error(f"WORKER_FORK: Failed to reset FlowExecutionEventPublisher: {e}")
            
            # Reset Debug Event Publisher
            try:
                from messaging.flow_events.debug_event_publisher import DebugEventPublisher
                DebugEventPublisher._topic_manager = None
                logger.info("WORKER_FORK: DebugEventPublisher reset successfully")
            except Exception as e:
                logger.error(f"WORKER_FORK: Failed to reset DebugEventPublisher: {e}")
```

## How It Works

1. **Before Fork:** Parent process creates `CeleryBackend`, which registers the `worker_process_init` signal handler
2. **Fork Happens:** Celery creates worker processes by forking
3. **After Fork:** The `reset_event_publishers` handler fires in each new worker
4. **Reset Singletons:** Sets `_topic_manager = None` on both event publishers
5. **Lazy Re-creation:** When workers try to publish events, `_get_topic_manager()` creates a fresh `TopicManager` with the worker's event loop
6. **Success:** Events publish successfully with fresh event loop

## Verification

To verify the fix is working:

```bash
cd /path/to/agentic-core
docker logs agentic-core-async-engine-async-1 2>&1 | grep "WORKER_FORK"
```

Expected output after first task execution:
```
[2025-10-29 10:XX:XX,XXX: INFO/ForkPoolWorker-1] WORKER_FORK: Resetting TopicManager singletons for new worker process
[2025-10-29 10:XX:XX,XXX: INFO/ForkPoolWorker-1] WORKER_FORK: FlowExecutionEventPublisher reset successfully
[2025-10-29 10:XX:XX,XXX: INFO/ForkPoolWorker-1] WORKER_FORK: DebugEventPublisher reset successfully
```

Check for successful event publishing:
```bash
docker logs agentic-core-async-engine-async-1 2>&1 | grep "PUBLISH_SUCCESS"
```

## Files Modified

- `/Users/<USER>/projects/agentic-core/backend/lib/async_engine/messaging/backends/celery_backend.py`
  - Added `from celery.signals import worker_process_init`
  - Added `self._register_fork_handler()` call in `__init__`
  - Added `_register_fork_handler()` method

## Testing

After applying this fix:
1. Restart async workers: `cd /path/to/agentic-core && docker compose restart async-engine-async`
2. Run a workflow execution
3. Check logs: Worker fork messages should appear
4. Check artifacts: `artifacts/{execution_id}/flow-execution.log` should contain detailed execution events

## Key Insights

- **The events were always being generated** - the problem was in publishing them to RabbitMQ
- **Debugging was never required** - logs work fine with `debugging_enabled=False`
- **This is a classic asyncio + multiprocessing issue** - event loops don't survive fork()
- **The fix is minimal and non-invasive** - just resets singletons after fork
- **No changes needed to axon-pfc** - all fixes are in agentic-core

## Date Fixed

October 29, 2025
