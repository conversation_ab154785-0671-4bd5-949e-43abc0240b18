# Evaluation System

Test and monitor workflow executions with automated test cases and real-time dashboards.

## 📚 Documentation

**Main Document:** [`EVAL_SYSTEM_IMPLEMENTATION_PLAN.md`](./EVAL_SYSTEM_IMPLEMENTATION_PLAN.md)

This comprehensive document includes:
- Complete database schema design
- Architecture overview
- Implementation phases with timeline
- Code examples for all components
- Quick start guide
- Success criteria

## 🚀 Quick Start

### 1. Run the Migration
```bash
cd /Users/<USER>/projects/axon-pfc
export PGPORT=$(cd ../agentic-core && docker compose port postgres 5432 | cut -d: -f2)
psql -h localhost -p $PGPORT -U postgres -d invisible -f migrations/add_evaluation_schema.sql
```

### 2. Verify Schema
```bash
psql -h localhost -p $PGPORT -U postgres -d invisible -c "\dn"
# Should see "evaluation" schema
```

### 3. Follow Implementation Plan
Open `EVAL_SYSTEM_IMPLEMENTATION_PLAN.md` and start with **Phase 1, Day 1**.

## 📁 Key Files

```
axon-pfc/
├── EVAL_SYSTEM_IMPLEMENTATION_PLAN.md    # 👈 START HERE
├── migrations/
│   └── add_evaluation_schema.sql         # Database migration
├── src/
│   ├── backend/
│   │   ├── models/evaluation.py          # (To create)
│   │   ├── routes/evaluation.py          # (To create)
│   │   └── services/
│   │       ├── eval_runner.py            # (To create)
│   │       ├── eval_monitor.py           # (To create)
│   │       └── rabbitmq_monitor.py       # (To create)
│   └── frontend/
│       ├── components/
│       │   ├── EvalCasesTab.js           # (To create)
│       │   └── Dashboard panels/         # (To create)
│       └── pages/
│           └── Dashboard.js              # (To create)
├── start.sh                              # (To update)
└── stop.sh                               # (To update)
```

## 🎯 What You're Building

### 1. Test Case Management
- Create test cases with expected inputs/outputs
- Organize tests into suites
- Tag and categorize tests

### 2. Execution System
- Run individual tests or entire suites
- Execute in parallel with concurrency control
- Reuse existing workflow execution infrastructure

### 3. Validation
- Compare actual vs expected outputs
- Track pass/fail rates
- Background validation service

### 4. Real-Time Dashboard
- Monitor active test runs
- View queue depths
- Track system health
- See recent completions

## 🔑 Key Design Decision

**No Data Duplication:** The evaluation system extends the existing `flow_execution` schema rather than duplicating it.

```
┌─────────────────────┐
│   eval_case         │  (What to test)
│   - inputs          │
│   - expected output │
└──────────┬──────────┘
           │
           │ creates
           ▼
┌─────────────────────┐
│  flow_execution     │  (Execution state - existing)
│  - status           │
│  - timing           │
│  - actual outputs   │
└──────────┬──────────┘
           │
           │ validates
           ▼
┌─────────────────────┐
│   eval_run          │  (Validation results)
│   - passed?         │
│   - validation notes│
└─────────────────────┘
```

This means:
- ✅ Single source of truth for execution state
- ✅ No redundant data
- ✅ Simpler to maintain
- ✅ Works with all existing tools

## 📊 Timeline

- **Week 1:** Database + Backend API
- **Week 2:** Monitoring + Dashboard API
- **Week 3:** Frontend UI
- **Week 4:** Integration + Testing

**Total:** ~128 hours over 4 weeks

## 🆘 Need Help?

1. **Read the plan:** `EVAL_SYSTEM_IMPLEMENTATION_PLAN.md` has detailed examples
2. **Check existing code:** `process_runs.py` shows how to create and execute workflows
3. **Test incrementally:** Get each phase working before moving to the next

---

**Ready?** Open `EVAL_SYSTEM_IMPLEMENTATION_PLAN.md` and begin! 🚀

