# Evaluation System Documentation

Complete guide to the workflow testing and evaluation system.

---

## 📋 Table of Contents

1. [Overview](#overview)
2. [Core Concepts](#core-concepts)
3. [Test Case Creation](#test-case-creation)
4. [Validation Rules](#validation-rules)
5. [Running Tests](#running-tests)
6. [Monitoring Executions](#monitoring-executions)
7. [Batch Testing](#batch-testing)
8. [Advanced Features](#advanced-features)

---

## Overview

The Evaluation System allows you to:
- **Define test cases** with expected inputs and outputs
- **Execute workflows** against test cases
- **Automatically validate** results
- **Track pass/fail rates** over time
- **Monitor execution** in real-time
- **Cancel stuck executions**

---

## Core Concepts

### Test Case (eval_case)

A test case defines:
- **Workflow** - Which workflow to test
- **Input Variables** - What inputs to provide
- **Expected Output** - What the workflow should produce
- **Validation Rules** - How to validate the output

Example:
```json
{
  "name": "Space Exploration Test",
  "workflow_id": "c1581443-952b-40ee-82f2-5e2641fbb186",
  "input_variables": {
    "topic": "space exploration"
  },
  "expected_output": {
    "greeting_message": "*",
    "conversation_log": "*"
  },
  "validation_rules": {
    "type": "exact_match",
    "ignore_case": false
  }
}
```

### Test Run (eval_run)

When you execute a test case, an `eval_run` is created:
- **Links** to the test case
- **Links** to the actual workflow execution
- **Stores** validation results (passed/failed)
- **Records** validation output and notes

### Test Suite (eval_suite)

A collection of related test cases:
- **Organizes** test cases by feature/category
- **Enables** batch execution
- **Tracks** overall suite pass rate

### Batch Run (eval_batch_run)

An execution of multiple test cases (usually a suite):
- **Groups** related executions
- **Tracks** progress (N of M completed)
- **Provides** aggregate statistics

---

## Test Case Creation

### Via UI

1. **Navigate to Workflow**
   - Open http://localhost:3000
   - Click on a workflow

2. **Go to Test Cases Tab**
   - Click "Test Cases" tab
   - See existing test cases

3. **Create New Test Case**
   - Click "Create Test Case" button
   - Fill in form:
     - **Name**: Descriptive name
     - **Description**: Optional details
     - **Input Variables**: JSON object with inputs
     - **Expected Output**: JSON object with expected results
   - Click "Create"

### Input Variables Format

Input variables must match the workflow's declared input variables.

**Example Workflow Variables:**
```json
{
  "variables": {
    "topic": {
      "type": "Input",
      "data_type": "str",
      "direction": "Input"
    }
  }
}
```

**Matching Test Case Input:**
```json
{
  "topic": "space exploration"
}
```

### Expected Output Format

#### Wildcard Matching

Use `"*"` to indicate "any value is acceptable":

```json
{
  "greeting_message": "*",
  "conversation_log": "*"
}
```

This validates that:
- Both keys exist in the output
- But doesn't check the actual values

#### Exact Matching

Provide exact expected values:

```json
{
  "status": "success",
  "count": 5
}
```

This validates that:
- `output.status === "success"`
- `output.count === 5`

#### Nested Objects

```json
{
  "result": {
    "success": true,
    "data": {
      "items": "*"
    }
  }
}
```

Validates:
- `result.success` must be `true`
- `result.data.items` can be any value

---

## Validation Rules

### Default Validation

By default, the system performs **key-based validation**:

```python
def validate(expected, actual):
    for key in expected:
        if key not in actual:
            return False, f"Missing key: {key}"
        
        if expected[key] == "*":
            continue  # Wildcard - any value OK
        
        if expected[key] != actual[key]:
            return False, f"Mismatch on {key}"
    
    return True, "Passed"
```

### Custom Validation Rules (Future)

Future enhancements could include:

#### Regex Matching
```json
{
  "validation_rules": {
    "greeting_message": {
      "type": "regex",
      "pattern": "^Hello .+ to .+!$"
    }
  }
}
```

#### Range Validation
```json
{
  "validation_rules": {
    "score": {
      "type": "range",
      "min": 0,
      "max": 100
    }
  }
}
```

#### Custom Functions
```json
{
  "validation_rules": {
    "custom_validator": "validate_greeting_format"
  }
}
```

---

## Running Tests

### Single Test Execution

**Via UI:**
1. Go to workflow → Test Cases tab
2. Find test case
3. Click "Run Test" button
4. Navigate to Dashboard to watch progress

**Via API:**
```bash
curl -X POST http://localhost:8000/api/eval-cases/{case_id}/run \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "ebd4bbe3-3065-4222-a240-e480aaef0672",
    "tenant_id": "40eaf721-236a-407e-b0f5-6e09b7cd39d8"
  }'
```

**Response:**
```json
{
  "eval_run_id": "223485dd-bf0f-4924-ae16-8d97cfea204d",
  "flow_execution_id": "17a8c341-77a8-446f-879e-2e11ffc9fb0e",
  "status": "PENDING"
}
```

### Execution Lifecycle

```
PENDING → RUNNING → COMPLETE/FAILED/CANCELLED
  ↓         ↓              ↓
  0s      1-60s         End
```

**States:**
- `PENDING` - Queued in RabbitMQ, not yet picked up
- `RUNNING` - Worker is executing the workflow
- `COMPLETE` - Execution finished successfully
- `FAILED` - Execution encountered an error
- `CANCELLED` - User cancelled the execution

### Viewing Results

**Dashboard:**
- Active Runs section shows running executions
- Recent Completions shows finished executions with pass/fail

**Database Query:**
```sql
SELECT 
    ec.name as test_case_name,
    er.passed,
    er.validation_output,
    fe.output_variables,
    fe.result
FROM evaluation.eval_run er
JOIN evaluation.eval_case ec ON er.eval_case_id = ec.id
JOIN flow_execution.flow_execution fe ON er.flow_execution_id = fe.id
WHERE er.id = 'your-eval-run-id';
```

---

## Monitoring Executions

### Dashboard Overview

**URL:** http://localhost:3000 → Dashboard

#### System Health
- **Database:** Connection status
- **RabbitMQ:** Connection status + message counts

#### Queue Status
```
agentic.async:      2 pending, 1 consumer
agentic.flyweight:  0 pending, 1 consumer
```

- **Pending**: Messages waiting to be processed
- **Consumers**: Worker instances listening

#### Active Runs

Real-time view of running executions:

| Test Case | Workflow | Status | Elapsed | Actions |
|-----------|----------|--------|---------|---------|
| Space Test | Greeting Team | RUNNING | 45s | [Cancel] |

**Features:**
- Shows elapsed time
- Updates every 2 seconds
- Cancel button for stuck workflows

#### Recent Completions

Last 20 completed executions:

| Test Case | Workflow | Status | Passed | Duration |
|-----------|----------|--------|--------|----------|
| Space Test | Greeting Team | COMPLETE | ✓ Yes | 12.3s |
| Quick Test | Greeting Team | FAILED | ✗ No | 0.9s |

**Badge Colors:**
- `COMPLETE` - Blue (successful execution)
- `FAILED` - Red (execution error)
- `CANCELLED` - Gray (user cancelled)

**Passed/Failed:**
- Green checkmark: Output validated successfully
- Red X: Output didn't match expected

---

## Cancelling Executions

### When to Cancel

Cancel an execution if:
- It's taking too long (stuck)
- You realize the test case is wrong
- The workflow has a bug causing infinite loops

### How to Cancel

**Via Dashboard:**
1. Go to Dashboard
2. Find execution in "Active Runs"
3. Click "Cancel" button
4. Confirm cancellation
5. Execution stops, appears in "Recent Completions" with CANCELLED status

**Via API:**
```bash
curl -X POST http://localhost:8000/api/eval-runs/{run_id}/cancel \
  -H "Content-Type: application/json" \
  -d '{
    "flow_execution_id": "fe8eb557-b735-4661-b901-e4f94f45922d"
  }'
```

### What Happens

1. `flow_execution.result` set to `CANCELLED`
2. `flow_execution.end_time` set to now
3. Worker detects cancellation, stops processing
4. EvalMonitor skips validation (no pass/fail)
5. Execution moves to Recent Completions

### Orphaned Messages

Cancelled executions may leave messages in RabbitMQ. To clean up:

```bash
docker exec agentic-core-rabbitmq-1 rabbitmqadmin purge queue name=agentic.async
```

---

## Batch Testing

### Creating a Test Suite

**Via API:**
```bash
curl -X POST http://localhost:8000/api/eval-suites \
  -H "Content-Type: application/json" \
  -d '{
    "workflow_id": "c1581443-952b-40ee-82f2-5e2641fbb186",
    "name": "Greeting Variations Suite",
    "description": "Tests different greeting topics",
    "test_case_ids": [
      "test-case-id-1",
      "test-case-id-2",
      "test-case-id-3"
    ]
  }'
```

### Running a Suite

**Via API:**
```bash
curl -X POST http://localhost:8000/api/eval-suites/{suite_id}/run \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "ebd4bbe3-3065-4222-a240-e480aaef0672",
    "tenant_id": "40eaf721-236a-407e-b0f5-6e09b7cd39d8"
  }'
```

This creates:
- 1 `eval_batch_run` record
- N `eval_run` records (one per test case)
- N `flow_execution` records

### Monitoring Batch Progress

**Query:**
```sql
SELECT * FROM evaluation.batch_run_progress
WHERE batch_run_id = 'your-batch-id';
```

**Returns:**
```
batch_run_id | total_cases | completed | passed | failed | pass_rate
-------------|-------------|-----------|--------|--------|----------
uuid         | 10          | 7         | 5      | 2      | 71.4%
```

---

## Advanced Features

### Historical Trends

Track pass rates over time:

```sql
SELECT 
    ec.name,
    DATE(er.evaluated_at) as date,
    COUNT(*) as total_runs,
    SUM(CASE WHEN er.passed THEN 1 ELSE 0 END) as passed_runs,
    ROUND(100.0 * SUM(CASE WHEN er.passed THEN 1 ELSE 0 END) / COUNT(*), 2) as pass_rate
FROM evaluation.eval_run er
JOIN evaluation.eval_case ec ON er.eval_case_id = ec.id
WHERE er.evaluated_at IS NOT NULL
GROUP BY ec.name, DATE(er.evaluated_at)
ORDER BY date DESC;
```

### Flaky Test Detection

Find tests that pass/fail inconsistently:

```sql
SELECT 
    ec.name,
    COUNT(*) as total_runs,
    SUM(CASE WHEN er.passed THEN 1 ELSE 0 END) as passed,
    SUM(CASE WHEN NOT er.passed THEN 1 ELSE 0 END) as failed,
    ROUND(100.0 * SUM(CASE WHEN er.passed THEN 1 ELSE 0 END) / COUNT(*), 2) as pass_rate
FROM evaluation.eval_run er
JOIN evaluation.eval_case ec ON er.eval_case_id = ec.id
WHERE er.evaluated_at IS NOT NULL
GROUP BY ec.name
HAVING COUNT(*) >= 5  -- At least 5 runs
   AND SUM(CASE WHEN er.passed THEN 1 ELSE 0 END) > 0  -- Some passes
   AND SUM(CASE WHEN NOT er.passed THEN 1 ELSE 0 END) > 0  -- Some failures
ORDER BY pass_rate;
```

### Performance Tracking

Track execution duration trends:

```sql
SELECT 
    ec.name,
    AVG(EXTRACT(EPOCH FROM (fe.end_time - fe.start_time))) as avg_duration_sec,
    MIN(EXTRACT(EPOCH FROM (fe.end_time - fe.start_time))) as min_duration_sec,
    MAX(EXTRACT(EPOCH FROM (fe.end_time - fe.start_time))) as max_duration_sec
FROM evaluation.eval_run er
JOIN evaluation.eval_case ec ON er.eval_case_id = ec.id
JOIN flow_execution.flow_execution fe ON er.flow_execution_id = fe.id
WHERE fe.result = 'COMPLETE'
GROUP BY ec.name
ORDER BY avg_duration_sec DESC;
```

### Tagging System

Organize test cases with tags:

```sql
-- Add tags when creating test case
INSERT INTO evaluation.eval_case (
    id, workflow_id, name, 
    input_variables, expected_output,
    tags
) VALUES (
    uuid_generate_v4(),
    'workflow-id',
    'Test Name',
    '{"input": "value"}',
    '{"output": "*"}',
    ARRAY['smoke', 'critical', 'v1.0']
);

-- Query by tag
SELECT * FROM evaluation.eval_case
WHERE 'smoke' = ANY(tags);
```

### Regression Detection

Detect when a previously passing test starts failing:

```sql
WITH recent_results AS (
    SELECT 
        eval_case_id,
        passed,
        evaluated_at,
        ROW_NUMBER() OVER (PARTITION BY eval_case_id ORDER BY evaluated_at DESC) as rn
    FROM evaluation.eval_run
    WHERE evaluated_at IS NOT NULL
)
SELECT 
    ec.name,
    curr.passed as latest_result,
    prev.passed as previous_result
FROM recent_results curr
JOIN recent_results prev ON curr.eval_case_id = prev.eval_case_id 
    AND prev.rn = 2
JOIN evaluation.eval_case ec ON curr.eval_case_id = ec.id
WHERE curr.rn = 1
  AND prev.passed = true
  AND curr.passed = false;  -- Was passing, now failing
```

---

## Best Practices

### 1. Test Case Naming

✅ **Good:**
- "Space Exploration - Basic Greeting"
- "User Registration - Invalid Email"
- "Checkout Flow - Applied Discount"

❌ **Bad:**
- "test1"
- "my test"
- "asdf"

### 2. Expected Outputs

✅ **Start with wildcards** while developing:
```json
{
  "greeting_message": "*",
  "conversation_log": "*"
}
```

✅ **Add specific checks** once stable:
```json
{
  "greeting_message": "*",
  "conversation_log": "*",
  "word_count": ">=50",
  "sentiment": "positive"
}
```

### 3. Test Organization

✅ **Use suites** to group related tests:
- "Smoke Tests" - Basic functionality
- "Regression Tests" - Previously fixed bugs
- "Performance Tests" - Duration checks
- "Edge Cases" - Unusual inputs

### 4. Handling Flaky Tests

If a test fails inconsistently:
1. Run it 5-10 times
2. Check if LLM outputs vary
3. Consider using wildcards for non-deterministic fields
4. Add retries for network-dependent tests

### 5. Monitoring Frequency

- **Active Runs**: Check dashboard every 30 seconds
- **Long-running tests**: Set timeout expectations (e.g., max 5 minutes)
- **Cancel stuck runs**: If elapsed > 10 minutes, something is wrong

---

## Troubleshooting

### Test Case Never Starts

**Symptoms:** Status stays "PENDING"

**Causes:**
- No workers running
- Workers crashed
- RabbitMQ connection lost

**Solution:**
```bash
# Check workers
docker ps | grep async-engine

# Check worker logs
docker compose logs async-engine-async | tail -50

# Restart workers
docker compose restart async-engine-async async-engine-flyweight
```

### Test Case Fails Immediately

**Symptoms:** Status goes PENDING → FAILED in < 1 second

**Causes:**
- Missing input variables
- Invalid workflow definition
- OpenAI API key missing

**Solution:**
```bash
# Check execution error
docker exec agentic-core-postgres-1 psql -U postgres -d invisible \
  -c "SELECT error_message FROM flow_execution.flow_execution WHERE id = 'exec-id'"

# Common errors:
# - "KeyError: 'topic'" → Missing input variable
# - "AuthenticationError" → API key not set
# - "KeyError: 'direction'" → Workflow definition issue
```

### Test Case Never Completes

**Symptoms:** Status stays "RUNNING" for > 5 minutes

**Causes:**
- Agent conversation loop (no termination)
- Waiting for user input (shouldn't happen)
- Worker deadlock

**Solution:**
```bash
# Cancel the execution
curl -X POST http://localhost:8000/api/eval-runs/{run_id}/cancel \
  -d '{"flow_execution_id": "exec-id"}'

# Fix agent termination:
# Ensure agents say "TERMINATE" when done
# Ensure agents use save_variable tool
```

### Validation Always Fails

**Symptoms:** Execution completes, but `passed = false`

**Causes:**
- Output variable names don't match
- Output format different than expected

**Solution:**
```sql
-- Check actual output
SELECT output_variables FROM flow_execution.flow_execution 
WHERE id = 'exec-id';

-- Check expected output
SELECT expected_output FROM evaluation.eval_case
WHERE id = 'case-id';

-- Check validation details
SELECT validation_output FROM evaluation.eval_run
WHERE id = 'run-id';
```

---

## API Reference

See [api-reference.md](./api-reference.md) for complete API documentation.

---

**Last Updated:** 2025-10-15  
**Version:** 1.0

