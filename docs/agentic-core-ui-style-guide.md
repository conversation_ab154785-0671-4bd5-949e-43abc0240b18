# Agentic-Core UI Style Guide

A comprehensive style reference for building UIs that match the agentic-core design system.

---

## 📋 Table of Contents

1. [Overview](#overview)
2. [Design System Foundation](#design-system-foundation)
3. [Color Palette](#color-palette)
4. [Typography](#typography)
5. [Component Library](#component-library)
6. [Layout Patterns](#layout-patterns)
7. [Spacing & Sizing](#spacing--sizing)
8. [Interactive Elements](#interactive-elements)
9. [Best Practices](#best-practices)
10. [Code Examples](#code-examples)

---

## Overview

### Design Philosophy

The agentic-core UI is built on **shadcn/ui** components with Tailwind CSS 4.x, following the **"New York"** style variant. The design emphasizes:

- **Clean, minimal aesthetics** - Subtle shadows, rounded corners, monochromatic palette
- **Professional enterprise feel** - Sophisticated without being playful
- **High information density** - Efficient use of space for complex workflows
- **Accessibility first** - ARIA labels, focus states, keyboard navigation
- **Dark mode support** - Fully implemented with CSS variables

### Tech Stack

- **Framework**: React 19.x with TypeScript
- **Build Tool**: Vite 7.x
- **Styling**: Tailwind CSS 4.x with CSS variables
- **UI Library**: shadcn/ui (New York variant)
- **Icons**: Lucide React
- **State Management**: Zustand
- **Forms**: React Hook Form + Zod
- **Charts**: Recharts
- **Animations**: tw-animate-css

---

## Design System Foundation

### Base Configuration

The design system uses **CSS custom properties** for theming, defined in `index.css`:

```css
:root {
  /* Core radius - all border-radius values derive from this */
  --radius: 0.625rem; /* 10px */
  
  /* Header height constant */
  --header-height: 64px;
  
  /* Light mode colors (using OKLCH color space) */
  --background: oklch(1 0 0);          /* Pure white */
  --foreground: oklch(0.145 0 0);      /* Near black */
  --card: oklch(1 0 0);                /* White */
  --card-foreground: oklch(0.145 0 0); /* Near black */
  --canvas: oklch(0.99 0 0);           /* Off-white background */
  --primary: oklch(0.205 0 0);         /* Dark gray/black */
  --primary-foreground: oklch(0.985 0 0); /* White */
  --muted: oklch(0.97 0 0);            /* Light gray */
  --muted-foreground: oklch(0.556 0 0); /* Medium gray */
  --border: oklch(0.922 0 0);          /* Subtle border gray */
  --ring: oklch(0.708 0 0);            /* Focus ring gray */
  
  /* Brand colors */
  --electric-blue: #0c76fe;
  --electric-blue-background: oklch(97% 3% 261deg);
  --teal: #2a9d90;
}

.dark {
  /* Dark mode colors */
  --background: oklch(0.145 0 0);      /* Near black */
  --foreground: oklch(0.985 0 0);      /* Near white */
  --card: oklch(0.205 0 0);            /* Dark gray */
  --canvas: oklch(0.19 0 0);           /* Slightly lighter than bg */
  --border: oklch(1 0 0 / 10%);       /* 10% white opacity */
  --input: oklch(1 0 0 / 15%);        /* 15% white opacity */
  --electric-blue-background: oklch(25% 12% 258deg);
}
```

### Radius System

All border-radius values are derived from the base `--radius` value:

- `--radius-sm`: `calc(var(--radius) - 4px)` → **6px** (inputs, small buttons)
- `--radius-md`: `calc(var(--radius) - 2px)` → **8px** (buttons, badges)
- `--radius-lg`: `var(--radius)` → **10px** (cards, panels)
- `--radius-xl`: `calc(var(--radius) + 4px)` → **14px** (large containers)

---

## Color Palette

### Neutral Scale

The design uses a **monochromatic neutral palette** with subtle variations:

**Light Mode:**
- Background: `oklch(1 0 0)` - Pure white
- Canvas: `oklch(0.99 0 0)` - Off-white for page backgrounds
- Card: `oklch(1 0 0)` - White cards on canvas
- Muted: `oklch(0.97 0 0)` - Light gray backgrounds
- Border: `oklch(0.922 0 0)` - Subtle borders
- Foreground: `oklch(0.145 0 0)` - Near black text

**Dark Mode:**
- Background: `oklch(0.145 0 0)` - Near black
- Canvas: `oklch(0.19 0 0)` - Slightly lighter than background
- Card: `oklch(0.205 0 0)` - Dark gray cards
- Muted: `oklch(0.269 0 0)` - Lighter gray
- Border: `oklch(1 0 0 / 10%)` - 10% white opacity
- Foreground: `oklch(0.985 0 0)` - Near white text

### Accent Colors

**Electric Blue** (Primary Brand):
- Hex: `#0c76fe`
- Usage: Published workflow badges, active states, brand accents
- Background variant: `oklch(97% 3% 261deg)` for light mode

**Teal** (Secondary):
- Hex: `#2a9d90`
- Usage: Success states, positive indicators

**Status Colors:**
- Success: `#22c55e` (green-500)
- Error/Destructive: `oklch(0.577 0.245 27.325)` (red-based)
- Warning: Chart colors for warnings

### Chart Colors

For data visualization (from `--chart-1` to `--chart-5`):
- Chart 1: `oklch(0.646 0.222 41.116)` - Orange
- Chart 2: `oklch(0.6 0.118 184.704)` - Cyan
- Chart 3: `oklch(0.398 0.07 227.392)` - Blue
- Chart 4: `oklch(0.828 0.189 84.429)` - Yellow-green
- Chart 5: `oklch(0.769 0.188 70.08)` - Yellow

---

## Typography

### Font Families

**Primary Font: Inter** (sans-serif)
```css
--font-sans: Inter, ui-sans-serif, system-ui, sans-serif, 
             "Apple Color Emoji", "Segoe UI Emoji";
```

**Brand Font: APK Praktical** (custom)
Used for logo and brand elements only:
```css
--font-apk-praktical: "APK Praktical", ui-sans-serif, system-ui, sans-serif;
```

**Monospace: Menlo/SFMono**
```css
--font-mono: Menlo, ui-monospace, SFMono-Regular, "SF Mono", 
             Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
```

### Type Scale

**Text Sizes (Tailwind classes):**
- `text-xs`: 0.75rem (12px) - Small labels, captions
- `text-sm`: 0.875rem (14px) - Body text, most UI elements
- `text-base`: 1rem (16px) - Default body text
- `text-lg`: 1.125rem (18px) - Subheadings
- `text-xl`: 1.25rem (20px) - Section headings

**Custom Sizes:**
- `text-2xs`: 0.6rem (9.6px) - Micro text
- `text-3xs`: 0.5rem (8px) - Ultra-small labels

### Font Weights

- `font-normal`: 400 (Regular) - Body text
- `font-medium`: 500 - Emphasis, labels
- `font-semibold`: 600 - Card titles, headings
- `font-bold`: 700 - Major headings, brand text

### Text Utilities

**Common Text Styles:**
```tsx
// Muted text (secondary information)
<span className="text-muted-foreground text-sm">

// Card titles
<div className="font-semibold leading-none">

// Descriptions
<div className="text-muted-foreground text-sm">

// Small labels
<span className="text-xs text-muted-foreground">
```

---

## Component Library

### Buttons

**Variants:**

1. **Default** - Primary action button
```tsx
<Button variant="default">Primary Action</Button>
```
- Background: `bg-primary`
- Text: `text-primary-foreground`
- Shadow: `shadow-xs`
- Hover: `hover:bg-primary/90`

2. **Outline** - Secondary actions
```tsx
<Button variant="outline">Secondary Action</Button>
```
- Border: `border`
- Background: `bg-background`
- Hover: `hover:bg-accent`

3. **Ghost** - Tertiary/icon buttons
```tsx
<Button variant="ghost"><Icon /></Button>
```
- No border/background
- Hover: `hover:bg-accent`

4. **Destructive** - Delete/cancel actions
```tsx
<Button variant="destructive">Delete</Button>
```
- Background: `bg-destructive`
- Text: `text-white`

**Sizes:**
- `size="sm"`: Height 32px (h-8)
- `size="default"`: Height 36px (h-9)
- `size="lg"`: Height 40px (h-10)
- `size="icon"`: Square 36x36px (size-9)

**Button Anatomy:**
```tsx
const buttonVariants = cva(
  "cursor-pointer inline-flex items-center justify-center gap-2 
   whitespace-nowrap rounded-md text-sm font-medium transition-all 
   disabled:pointer-events-none disabled:opacity-50 
   focus-visible:ring-ring/50 focus-visible:ring-[3px]"
)
```

### Cards

**Basic Card Structure:**
```tsx
<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Optional description text</CardDescription>
  </CardHeader>
  <CardContent>
    Main content here
  </CardContent>
  <CardFooter>
    Actions or metadata
  </CardFooter>
</Card>
```

**Styling:**
- Border radius: `rounded-xl` (12px)
- Padding: `py-6` (24px vertical)
- Gap: `gap-6` (24px between sections)
- Shadow: `shadow-sm`
- Border: `border`

### Inputs

**Standard Input:**
```tsx
<Input 
  type="text" 
  placeholder="Enter text..."
  className="h-9 rounded-md border"
/>
```

**Input Styling:**
- Height: `h-9` (36px)
- Padding: `px-3 py-1`
- Border: `border border-input`
- Focus: `focus-visible:ring-ring/50 focus-visible:ring-[3px]`
- Shadow: `shadow-xs`
- Dark mode: `dark:bg-input/30`

**Error State:**
- Add `aria-invalid:border-destructive`
- Add `aria-invalid:ring-destructive/20`

### Badges

**Variants:**
```tsx
// Default (black)
<Badge variant="default">Default</Badge>

// Secondary (gray)
<Badge variant="secondary">Draft</Badge>

// Custom color
<Badge className="bg-electric-blue text-white">Published</Badge>

// Destructive
<Badge variant="destructive">Error</Badge>
```

**Styling:**
- Padding: `px-2 py-0.5`
- Font size: `text-xs`
- Border radius: `rounded-md`
- Font weight: `font-medium`

### Tables

**Table Structure:**
```tsx
<Table>
  <TableHeader>
    <TableRow>
      <TableHead>Column 1</TableHead>
      <TableHead>Column 2</TableHead>
    </TableRow>
  </TableHeader>
  <TableBody>
    <TableRow>
      <TableCell>Data 1</TableCell>
      <TableCell>Data 2</TableCell>
    </TableRow>
  </TableBody>
</Table>
```

**Styling:**
- Font size: `text-sm`
- Cell padding: `p-2`
- Row hover: `hover:bg-muted/50`
- Row border: `border-b`

### Dialogs & Modals

**Dialog Pattern:**
```tsx
<Dialog open={isOpen} onOpenChange={setIsOpen}>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Dialog Title</DialogTitle>
      <DialogDescription>
        Optional description text
      </DialogDescription>
    </DialogHeader>
    {/* Content */}
    <DialogFooter>
      <Button variant="outline" onClick={onCancel}>Cancel</Button>
      <Button onClick={onConfirm}>Confirm</Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

---

## Layout Patterns

### Page Layout

**Standard Page Structure:**
```tsx
<div className="flex min-h-screen w-full flex-col">
  <HeaderBar>
    {/* Header content */}
  </HeaderBar>
  
  <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
    {/* Main content */}
  </div>
</div>
```

### Header Bar

**Header Pattern:**
```tsx
<header className="bg-background flex w-full shrink-0 items-center 
                   gap-2 border-b py-4">
  <div className="flex w-full items-center justify-between 
                  gap-1 px-4 lg:gap-2 lg:px-6">
    {/* Header content */}
  </div>
</header>
```

**Header Height:**
- Fixed height: `--header-height: 64px`
- Used for positioning: `style={{ top: "var(--header-height)" }}`

### Sidebar

**App Sidebar Structure:**
```tsx
<Sidebar variant="sidebar" collapsible="icon">
  <SidebarHeader className="h-20 bg-black">
    <Logo />
  </SidebarHeader>
  
  <SidebarContent>
    <NavMain items={navItems} />
  </SidebarContent>
  
  <SidebarFooter>
    <NavUser />
  </SidebarFooter>
</Sidebar>
```

**Sidebar Styling:**
- Background: `bg-sidebar` / `dark:bg-black`
- Border: `border-r`
- Collapsed width: Icon-only mode
- Expanded width: Full navigation

### Resizable Panels

**Split View Pattern:**
```tsx
<ResizablePanelGroup direction="horizontal">
  <ResizablePanel defaultSize={25} minSize={5}>
    {/* Left panel */}
  </ResizablePanel>
  
  <ResizableHandle withHandle />
  
  <ResizablePanel defaultSize={75} minSize={50}>
    {/* Right panel */}
  </ResizablePanel>
</ResizablePanelGroup>
```

**Vertical Splits:**
```tsx
<ResizablePanelGroup direction="vertical">
  <ResizablePanel defaultSize={70}>
    {/* Top content */}
  </ResizablePanel>
  
  <ResizableHandle withHandle />
  
  <ResizablePanel defaultSize={30} minSize={15}>
    {/* Bottom panel */}
  </ResizablePanel>
</ResizablePanelGroup>
```

---

## Spacing & Sizing

### Spacing Scale

Tailwind spacing (rem-based):
- `gap-1`: 0.25rem (4px)
- `gap-2`: 0.5rem (8px)
- `gap-4`: 1rem (16px)
- `gap-6`: 1.5rem (24px) - **Default card gap**
- `p-4`: 1rem (16px) padding
- `p-6`: 1.5rem (24px) padding - **Default card padding**
- `p-8`: 2rem (32px) padding

### Container Sizes

**Max Width Containers:**
```tsx
<div className="mx-auto max-w-screen-xl px-4">
  {/* Centered content with max width */}
</div>
```

**Common Heights:**
- Input height: `h-9` (36px)
- Button height: `h-9` (36px default)
- Header height: `64px` (CSS variable)

### Responsive Breakpoints

Tailwind breakpoints:
- `sm`: 640px
- `md`: 768px
- `lg`: 1024px
- `xl`: 1280px
- `2xl`: 1536px

**Responsive Pattern:**
```tsx
<div className="p-4 md:p-8 grid gap-4 md:grid-cols-2 lg:grid-cols-3">
  {/* Responsive grid */}
</div>
```

---

## Interactive Elements

### Focus States

**Universal Focus Ring:**
```css
focus-visible:border-ring 
focus-visible:ring-ring/50 
focus-visible:ring-[3px]
```

**Applied to:**
- Buttons
- Inputs
- Select dropdowns
- Badges (when interactive)
- Links

### Hover States

**Button Hover:**
```tsx
hover:bg-primary/90    // 90% opacity
hover:bg-accent        // Background change
```

**Table Row Hover:**
```tsx
hover:bg-muted/50     // 50% opacity
```

**Card/Link Hover:**
```tsx
hover:bg-accent hover:text-accent-foreground
```

### Disabled States

**Standard Disabled:**
```tsx
disabled:pointer-events-none 
disabled:opacity-50
```

**Applied universally** to buttons, inputs, and interactive elements.

### Transitions

**Standard Transition:**
```tsx
transition-all      // All properties
transition-colors   // Color changes only
```

**Duration:** Typically 200ms (Tailwind default)

---

## Best Practices

### Component Composition

**Always use the `cn()` utility** for merging classes:
```tsx
import { cn } from "@/lib/utils"

<div className={cn("base-classes", conditionalClass, className)}>
```

**The `cn()` function:**
```tsx
import { clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
```

### Dark Mode

**Always define both light and dark styles:**
```tsx
// Light: bg-background, Dark: handled by CSS variable
<div className="bg-background text-foreground">

// Explicit dark mode style
<div className="bg-white dark:bg-gray-900">
```

**Dark mode is handled via:**
- CSS custom properties that change with `.dark` class
- Class-based dark mode: `dark:` prefix
- Applied at root level by theme provider

### Accessibility

**Always include:**
- `aria-label` on icon-only buttons
- `aria-invalid` on form inputs with errors
- Keyboard navigation support (automatic with Radix UI)
- Focus indicators (automatic with focus-visible)

**Example:**
```tsx
<Button 
  variant="ghost" 
  size="icon"
  aria-label="Edit workflow"
>
  <PencilIcon />
</Button>
```

### Loading States

**Standard Loading Indicator:**
```tsx
<LoadingIndicator text="Loading workflow..." />
```

**Skeleton Pattern:**
```tsx
<Skeleton className="h-4 w-full" />
```

### Empty States

**Standard Empty State:**
```tsx
<div className="flex h-full items-center justify-center">
  <div className="text-center">
    <Icon className="mx-auto h-12 w-12 text-muted-foreground" />
    <h3 className="mt-4 text-sm font-semibold">No items found</h3>
    <p className="mt-2 text-sm text-muted-foreground">
      Get started by creating a new item.
    </p>
    <Button className="mt-4">Create Item</Button>
  </div>
</div>
```

---

## Code Examples

### Complete Page Example

```tsx
import { HeaderBar } from "@/components/shared/header-bar"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { PlusIcon } from "lucide-react"

export default function WorkflowsPage() {
  return (
    <div className="flex min-h-screen w-full flex-col">
      <HeaderBar>
        <div className="flex items-center gap-2">
          <h1 className="text-sm font-bold">Workflows</h1>
        </div>
        <Button>
          <PlusIcon />
          New Workflow
        </Button>
      </HeaderBar>
      
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Name</CardTitle>
              <CardDescription>
                Created 2 hours ago
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Workflow description goes here
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
```

### Form Example

```tsx
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useForm } from "react-hook-form"

export function WorkflowForm() {
  const { register, handleSubmit } = useForm()
  
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Workflow Name</Label>
        <Input 
          id="name"
          type="text"
          placeholder="Enter name..."
          {...register("name")}
        />
      </div>
      
      <div className="flex gap-2 justify-end">
        <Button variant="outline" type="button">
          Cancel
        </Button>
        <Button type="submit">
          Save Workflow
        </Button>
      </div>
    </form>
  )
}
```

### Status Badge Example

```tsx
import { Badge } from "@/components/ui/badge"

export function WorkflowStatusBadge({ status }: { status: string }) {
  if (status === "published") {
    return (
      <Badge className="bg-electric-blue text-white">
        Published
      </Badge>
    )
  }
  
  if (status === "draft") {
    return <Badge variant="secondary">Draft</Badge>
  }
  
  if (status === "archived") {
    return (
      <Badge variant="secondary" className="bg-muted-foreground text-white">
        Archived
      </Badge>
    )
  }
  
  return <Badge variant="default">{status}</Badge>
}
```

### Responsive Grid Example

```tsx
<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
  <Card className="lg:col-span-4">
    {/* Takes 4 columns on large screens */}
  </Card>
  
  <Card className="lg:col-span-3">
    {/* Takes 3 columns on large screens */}
  </Card>
</div>
```

### Toast Notifications

```tsx
import { toast } from "sonner"

// Success toast
toast.success("Workflow saved", {
  description: "Your changes have been saved successfully"
})

// Error toast
toast.error("Failed to save", {
  description: "Please try again later"
})
```

---

## Quick Reference

### Most Common Classes

**Layout:**
- `flex items-center gap-2` - Horizontal flex with gap
- `flex flex-col gap-4` - Vertical flex with gap
- `grid gap-4 md:grid-cols-2` - Responsive grid

**Spacing:**
- `p-4 md:p-8` - Responsive padding
- `space-y-4` - Vertical spacing between children
- `gap-2`, `gap-4`, `gap-6` - Flex/grid gaps

**Typography:**
- `text-sm font-medium` - Standard label
- `text-sm text-muted-foreground` - Secondary text
- `font-semibold leading-none` - Card title

**Borders & Shadows:**
- `border rounded-md` - Standard border
- `border-b` - Bottom border only
- `shadow-xs` - Subtle shadow
- `shadow-sm` - Card shadow

**Colors:**
- `bg-background text-foreground` - Page background
- `bg-card text-card-foreground` - Card
- `bg-muted` - Muted background
- `bg-electric-blue text-white` - Brand accent

---

## File Structure Reference

When building new features, follow this structure:

```
src/
├── components/
│   ├── ui/                    # shadcn/ui components
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   └── ...
│   ├── shared/                # Reusable components
│   │   ├── header-bar.tsx
│   │   ├── loading-indicator.tsx
│   │   └── ...
│   └── feature-name/          # Feature-specific components
│       ├── feature-page.tsx
│       ├── feature-sidebar.tsx
│       └── ...
├── lib/
│   └── utils.ts               # cn() utility
├── hooks/                     # Custom hooks
├── contexts/                  # React contexts
└── index.css                  # Global styles
```

---

## Summary Checklist

When building UI components, ensure:

- ✅ Use CSS variables for colors (`bg-background`, `text-foreground`)
- ✅ Support dark mode (test with `.dark` class)
- ✅ Include focus states (`focus-visible:ring-[3px]`)
- ✅ Apply consistent border radius (`rounded-md`, `rounded-xl`)
- ✅ Use `cn()` utility for class merging
- ✅ Follow spacing scale (4px, 8px, 16px, 24px)
- ✅ Include accessibility attributes (`aria-label`, `aria-invalid`)
- ✅ Use Lucide icons consistently
- ✅ Apply hover and disabled states
- ✅ Match typography scale and weights
- ✅ Use shadcn/ui components as base
- ✅ Include loading and empty states
- ✅ Test responsive breakpoints

---

**Version:** 1.0  
**Last Updated:** October 29, 2025  
**Based On:** agentic-core v0.0.6

