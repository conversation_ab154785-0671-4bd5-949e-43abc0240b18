# API Reference

Complete REST API documentation for Axon-PFC.

---

## Base URL

```
http://localhost:8000
```

---

## Authentication

Currently: **No authentication** (development mode)

For production, add:
- JWT token authentication
- API keys
- OAuth 2.0

---

## Workflows API

### List Workflows

```http
GET /api/workflows
```

**Response:**
```json
[
  {
    "id": "c1581443-952b-40ee-82f2-5e2641fbb186",
    "name": "Greeting Team Workflow",
    "description": "Multi-agent greeting generator",
    "created_at": "2025-10-15T10:00:00Z",
    "is_active": true
  }
]
```

---

### Get Workflow Detail

```http
GET /api/workflows/{workflow_id}
```

**Response:**
```json
{
  "id": "c1581443-952b-40ee-82f2-5e2641fbb186",
  "name": "Greeting Team Workflow",
  "description": "Multi-agent greeting generator",
  "definition": {
    "type": "team",
    "variables": {
      "topic": {
        "type": "Input",
        "data_type": "str",
        "direction": "Input"
      },
      "greeting_message": {
        "type": "Output",
        "data_type": "str",
        "direction": "Output"
      }
    },
    "agents": [
      {
        "id": "agent-uuid",
        "name": "coordinator",
        "model": "gpt-4o",
        "skills": "...",
        "persona": "..."
      }
    ],
    "termination_condition": {
      "type": "regex",
      "pattern": "TERMINATE"
    }
  },
  "created_at": "2025-10-15T10:00:00Z",
  "updated_at": "2025-10-15T10:05:00Z"
}
```

---

### Create Workflow

```http
POST /api/workflows
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "My Workflow",
  "description": "Description here",
  "definition": {
    "type": "team",
    "variables": {
      "input_var": {
        "type": "Input",
        "data_type": "str",
        "direction": "Input"
      }
    },
    "agents": [
      {
        "name": "agent1",
        "model": "gpt-4o",
        "skills": "...",
        "persona": "..."
      }
    ]
  },
  "user_id": "user-uuid",
  "tenant_id": "tenant-uuid"
}
```

**Response:**
```json
{
  "id": "new-workflow-uuid",
  "name": "My Workflow",
  "version_id": "version-uuid",
  "version_number": 1
}
```

---

### Update Workflow

```http
PUT /api/workflows/{workflow_id}
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Updated Name",
  "description": "Updated description",
  "definition": {
    // Full workflow definition
  }
}
```

**Response:**
```json
{
  "id": "workflow-uuid",
  "name": "Updated Name",
  "version_id": "new-version-uuid",
  "version_number": 2
}
```

---

### Get Workflow Versions

```http
GET /api/workflows/{workflow_id}/versions
```

**Response:**
```json
[
  {
    "version_id": "version-uuid-2",
    "version_number": 2,
    "created_at": "2025-10-15T10:10:00Z"
  },
  {
    "version_id": "version-uuid-1",
    "version_number": 1,
    "created_at": "2025-10-15T10:00:00Z"
  }
]
```

---

## Evaluation API

### List Test Cases

```http
GET /api/eval-cases?workflow_id={workflow_id}
```

**Query Parameters:**
- `workflow_id` (required) - Filter by workflow

**Response:**
```json
[
  {
    "id": "case-uuid-1",
    "workflow_id": "workflow-uuid",
    "name": "Space Exploration Test",
    "description": "Tests space topic greeting",
    "input_variables": {
      "topic": "space exploration"
    },
    "expected_output": {
      "greeting_message": "*"
    },
    "is_active": true,
    "created_at": "2025-10-15T10:00:00Z",
    "stats": {
      "total_runs": 5,
      "passed_runs": 4,
      "pass_rate": 80.0
    }
  }
]
```

---

### Create Test Case

```http
POST /api/eval-cases
Content-Type: application/json
```

**Request Body:**
```json
{
  "workflow_id": "workflow-uuid",
  "name": "Test Name",
  "description": "Optional description",
  "input_variables": {
    "topic": "space exploration"
  },
  "expected_output": {
    "greeting_message": "*",
    "conversation_log": "*"
  },
  "validation_rules": {},
  "tags": ["smoke", "v1.0"],
  "user_id": "user-uuid"
}
```

**Response:**
```json
{
  "id": "new-case-uuid",
  "workflow_id": "workflow-uuid",
  "name": "Test Name",
  "created_at": "2025-10-15T10:00:00Z"
}
```

---

### Get Test Case

```http
GET /api/eval-cases/{case_id}
```

**Response:**
```json
{
  "id": "case-uuid",
  "workflow_id": "workflow-uuid",
  "name": "Space Exploration Test",
  "description": "Tests space topic greeting",
  "input_variables": {
    "topic": "space exploration"
  },
  "expected_output": {
    "greeting_message": "*"
  },
  "validation_rules": {},
  "tags": ["smoke"],
  "is_active": true,
  "created_at": "2025-10-15T10:00:00Z",
  "updated_at": "2025-10-15T10:05:00Z"
}
```

---

### Update Test Case

```http
PUT /api/eval-cases/{case_id}
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Updated Name",
  "description": "Updated description",
  "input_variables": {
    "topic": "updated input"
  },
  "expected_output": {
    "greeting_message": "*"
  },
  "is_active": true
}
```

**Response:**
```json
{
  "id": "case-uuid",
  "name": "Updated Name",
  "updated_at": "2025-10-15T10:15:00Z"
}
```

---

### Run Test Case

```http
POST /api/eval-cases/{case_id}/run
Content-Type: application/json
```

**Request Body:**
```json
{
  "user_id": "user-uuid",
  "tenant_id": "tenant-uuid"
}
```

**Response:**
```json
{
  "eval_run_id": "run-uuid",
  "flow_execution_id": "execution-uuid",
  "status": "PENDING",
  "created_at": "2025-10-15T10:20:00Z"
}
```

---

### Cancel Eval Run

```http
POST /api/eval-runs/{run_id}/cancel
Content-Type: application/json
```

**Request Body:**
```json
{
  "flow_execution_id": "execution-uuid"
}
```

**Response:**
```json
{
  "message": "Execution cancelled successfully",
  "eval_run_id": "run-uuid",
  "flow_execution_id": "execution-uuid"
}
```

**Error Responses:**

```json
// 404 - Not found
{
  "detail": "Eval run not found"
}

// 400 - Already completed
{
  "detail": "Execution is already COMPLETE, cannot cancel"
}
```

---

## Dashboard API

### Get Dashboard Status

```http
GET /api/dashboard/status
```

**Response:**
```json
{
  "timestamp": "2025-10-15T10:25:00.123456",
  "system_health": {
    "database": "healthy",
    "rabbitmq": "healthy",
    "rabbitmq_connected": true
  },
  "queues": {
    "agentic.async": {
      "pending": 2,
      "consumers": 1,
      "error": null
    },
    "agentic.flyweight": {
      "pending": 0,
      "consumers": 1,
      "error": null
    }
  },
  "active_runs": [
    {
      "eval_run_id": "run-uuid",
      "test_case_name": "Space Exploration Test",
      "workflow_name": "Greeting Team Workflow",
      "status": "RUNNING",
      "started_at": "2025-10-15T10:20:00Z",
      "elapsed_seconds": 305.5,
      "batch_run_id": null,
      "batch_name": null,
      "flow_execution_id": "execution-uuid"
    }
  ],
  "recent_completions": [
    {
      "eval_run_id": "run-uuid",
      "test_case_name": "Quick Test",
      "workflow_name": "Greeting Team Workflow",
      "execution_status": "COMPLETE",
      "started_at": "2025-10-15T10:15:00Z",
      "completed_at": "2025-10-15T10:15:12Z",
      "duration_ms": 12345.67,
      "passed": true
    },
    {
      "eval_run_id": "run-uuid-2",
      "test_case_name": "Failed Test",
      "workflow_name": "Greeting Team Workflow",
      "execution_status": "FAILED",
      "started_at": "2025-10-15T10:10:00Z",
      "completed_at": "2025-10-15T10:10:01Z",
      "duration_ms": 1234.56,
      "passed": false
    }
  ]
}
```

**Field Descriptions:**

- `system_health`
  - `database`: "healthy" or "error: <message>"
  - `rabbitmq`: "healthy" or "error: <message>"
  - `rabbitmq_connected`: boolean

- `queues.{queue_name}`
  - `pending`: Number of messages waiting
  - `consumers`: Number of workers listening
  - `error`: Error message if queue check failed

- `active_runs[]`
  - `elapsed_seconds`: Time since execution started
  - `flow_execution_id`: ID for cancellation

- `recent_completions[]`
  - `execution_status`: COMPLETE, FAILED, or CANCELLED
  - `passed`: true/false (validation result), null if CANCELLED
  - `duration_ms`: Total execution time in milliseconds

---

### Get Recent Completions

```http
GET /api/recent-completions?limit={limit}
```

**Query Parameters:**
- `limit` (optional) - Default: 20, Max: 100

**Response:**
```json
[
  {
    "eval_run_id": "run-uuid",
    "test_case_name": "Test Name",
    "workflow_name": "Workflow Name",
    "execution_status": "COMPLETE",
    "started_at": "2025-10-15T10:15:00Z",
    "completed_at": "2025-10-15T10:15:12Z",
    "duration_ms": 12345.67,
    "passed": true
  }
]
```

---

## Test Suites API (Future)

### Create Suite

```http
POST /api/eval-suites
Content-Type: application/json
```

**Request Body:**
```json
{
  "workflow_id": "workflow-uuid",
  "name": "Smoke Tests",
  "description": "Core functionality tests",
  "test_case_ids": [
    "case-uuid-1",
    "case-uuid-2",
    "case-uuid-3"
  ],
  "user_id": "user-uuid"
}
```

**Response:**
```json
{
  "id": "suite-uuid",
  "name": "Smoke Tests",
  "test_case_count": 3,
  "created_at": "2025-10-15T10:00:00Z"
}
```

---

### Run Suite

```http
POST /api/eval-suites/{suite_id}/run
Content-Type: application/json
```

**Request Body:**
```json
{
  "user_id": "user-uuid",
  "tenant_id": "tenant-uuid"
}
```

**Response:**
```json
{
  "batch_run_id": "batch-uuid",
  "suite_id": "suite-uuid",
  "total_cases": 3,
  "eval_run_ids": [
    "run-uuid-1",
    "run-uuid-2",
    "run-uuid-3"
  ],
  "started_at": "2025-10-15T10:20:00Z"
}
```

---

### Get Batch Run Status

```http
GET /api/eval-batch-runs/{batch_run_id}
```

**Response:**
```json
{
  "batch_run_id": "batch-uuid",
  "suite_id": "suite-uuid",
  "suite_name": "Smoke Tests",
  "total_cases": 3,
  "completed": 2,
  "running": 1,
  "passed": 1,
  "failed": 1,
  "pass_rate": 50.0,
  "started_at": "2025-10-15T10:20:00Z",
  "status": "RUNNING"
}
```

---

## Error Responses

All endpoints may return these error responses:

### 400 Bad Request

```json
{
  "detail": "Invalid request parameters"
}
```

**Common causes:**
- Invalid UUID format
- Missing required fields
- Invalid JSON

---

### 404 Not Found

```json
{
  "detail": "Resource not found"
}
```

**Common causes:**
- Invalid workflow ID
- Invalid test case ID
- Deleted resource

---

### 500 Internal Server Error

```json
{
  "detail": "Internal server error"
}
```

**Common causes:**
- Database connection lost
- RabbitMQ connection lost
- Unexpected exception

Check `backend.log` for details.

---

## Rate Limiting

Currently: **No rate limiting**

For production, consider:
- 100 requests/minute per IP
- 1000 requests/hour per user
- Use Redis for distributed rate limiting

---

## Pagination

Currently: **No pagination** (returns all results)

For production, add pagination to list endpoints:

```http
GET /api/workflows?page=1&page_size=20
```

**Response Headers:**
```
X-Total-Count: 157
X-Page: 1
X-Page-Size: 20
X-Total-Pages: 8
```

---

## Filtering & Sorting

### Current Support

- `workflow_id` filter on test cases
- `limit` on recent completions

### Future Enhancements

```http
GET /api/eval-cases?workflow_id=uuid&is_active=true&tags=smoke,critical&sort=-created_at
```

**Query Parameters:**
- `workflow_id` - Filter by workflow
- `is_active` - Filter by active status
- `tags` - Comma-separated tag list
- `sort` - Sort field (prefix with `-` for descending)

---

## WebSocket API (Future)

### Dashboard Real-time Updates

```javascript
const ws = new WebSocket('ws://localhost:8000/ws/dashboard');

ws.onmessage = (event) => {
  const status = JSON.parse(event.data);
  console.log('Active runs:', status.active_runs);
  console.log('Recent completions:', status.recent_completions);
};
```

**Benefits:**
- Real-time updates (no polling)
- Lower server load
- Instant notifications

---

## Client Libraries

### Python

```python
import requests

class AxonPFCClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
    
    def list_workflows(self):
        response = requests.get(f"{self.base_url}/api/workflows")
        response.raise_for_status()
        return response.json()
    
    def create_test_case(self, workflow_id, name, inputs, expected):
        data = {
            "workflow_id": workflow_id,
            "name": name,
            "input_variables": inputs,
            "expected_output": expected,
            "user_id": "your-user-id"
        }
        response = requests.post(
            f"{self.base_url}/api/eval-cases",
            json=data
        )
        response.raise_for_status()
        return response.json()
    
    def run_test_case(self, case_id):
        data = {
            "user_id": "your-user-id",
            "tenant_id": "your-tenant-id"
        }
        response = requests.post(
            f"{self.base_url}/api/eval-cases/{case_id}/run",
            json=data
        )
        response.raise_for_status()
        return response.json()

# Usage
client = AxonPFCClient()
workflows = client.list_workflows()
print(workflows)
```

---

### JavaScript/Node.js

```javascript
class AxonPFCClient {
  constructor(baseURL = 'http://localhost:8000') {
    this.baseURL = baseURL;
  }

  async listWorkflows() {
    const response = await fetch(`${this.baseURL}/api/workflows`);
    if (!response.ok) throw new Error('Failed to fetch workflows');
    return response.json();
  }

  async createTestCase(workflowId, name, inputs, expected) {
    const response = await fetch(`${this.baseURL}/api/eval-cases`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        workflow_id: workflowId,
        name: name,
        input_variables: inputs,
        expected_output: expected,
        user_id: 'your-user-id'
      })
    });
    if (!response.ok) throw new Error('Failed to create test case');
    return response.json();
  }

  async runTestCase(caseId) {
    const response = await fetch(
      `${this.baseURL}/api/eval-cases/${caseId}/run`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          user_id: 'your-user-id',
          tenant_id: 'your-tenant-id'
        })
      }
    );
    if (!response.ok) throw new Error('Failed to run test case');
    return response.json();
  }
}

// Usage
const client = new AxonPFCClient();
const workflows = await client.listWorkflows();
console.log(workflows);
```

---

## Webhooks (Future)

Register webhooks to receive notifications:

```http
POST /api/webhooks
Content-Type: application/json
```

**Request:**
```json
{
  "url": "https://your-server.com/webhook",
  "events": [
    "eval_run.completed",
    "eval_run.failed",
    "batch_run.completed"
  ],
  "secret": "your-webhook-secret"
}
```

**Webhook Payload:**
```json
{
  "event": "eval_run.completed",
  "timestamp": "2025-10-15T10:30:00Z",
  "data": {
    "eval_run_id": "run-uuid",
    "test_case_name": "Test Name",
    "passed": true,
    "duration_ms": 12345.67
  }
}
```

---

**Last Updated:** 2025-10-15  
**Version:** 1.0

