# Eval Case Generation - N/A Value Fix

## Issue

When auto-generating test cases for workflows, the system was skipping many valid test cases with warnings like:

```
⚠️ Skipping test case 'Google AI Advances' - placeholder value: 
Revenue: $257 billion, ARR: $100 billion, Users: N/A, Funding: N/A, Margins: 29%
```

## Root Cause

The validation code was **too strict** - it rejected ANY value containing "N/A", even when "N/A" was a valid part of structured data.

For example, for a company like Google:
- `Users: N/A` is reasonable (which users? Search? Gmail? Android? - it's ambiguous)
- `Funding: N/A` is valid (public company, doesn't have funding rounds)

The AI was correctly generating realistic data, but the validation was incorrectly treating "N/A" as a placeholder.

## The Fix

### 1. Smarter Validation (line 920-945 in `evaluation.py`)

**Before**: Rejected any string containing "n/a"
```python
if any(placeholder in lower_value for placeholder in ['[', ']', 'placeholder', 'example', 'n/a', 'tbd']):
    # Skip this test case
```

**After**: Only rejects values that are ENTIRELY placeholders
```python
# Skip if the entire value is just a placeholder
if stripped_value.lower() in ['n/a', 'tbd', 'placeholder', 'example', 'todo', '[placeholder]']:
    # Skip - this is just a placeholder
    
# Skip if it contains obvious placeholder markers
if any(marker in lower_value for marker in ['[placeholder]', '[example]', '[tbd]', 'todo:', 'fixme:']):
    # Skip - contains placeholder markers
```

**Now Accepts**:
- ✅ `"Revenue: $257B, ARR: $100B, Users: N/A, Funding: N/A, Margins: 29%"` (structured data with some N/A fields)
- ✅ `"Status: Active, Priority: N/A, Location: USA"` (N/A as part of structured data)

**Still Rejects**:
- ❌ `"N/A"` (entire value is just N/A)
- ❌ `"[PLACEHOLDER]"` (obvious placeholder marker)
- ❌ `"TODO: add value"` (placeholder marker)
- ❌ `"TBD"` (entire value is placeholder)

### 2. Improved AI Prompt (line 851-878)

Updated the prompt to clarify when N/A is acceptable:

```
4. For fields where data is not applicable, you MAY use "N/A" within 
   structured data (e.g., "Revenue: $100M, Funding: N/A" is acceptable)
5. DO NOT use entire values that are just "N/A" - provide real data 
   whenever possible
```

## Result

Now the system:
- ✅ Accepts realistic test cases where some fields genuinely don't apply
- ✅ Still rejects obvious placeholder values
- ✅ Generates more valid test cases (fewer skipped)
- ✅ Better reflects real-world data scenarios

## Examples

### Before (All Skipped):
```
⚠️ Skipping test case 'Apple Revenue Surge' - placeholder value: Revenue: $365B, ARR: $100B, Users: 1.5B, Funding: N/A, Margins: 42%
⚠️ Skipping test case 'Google AI Advances' - placeholder value: Revenue: $257B, ARR: $100B, Users: N/A, Funding: N/A, Margins: 29%
⚠️ Skipping test case 'Tesla EV Expansion' - placeholder value: Revenue: $53B, ARR: $30B, Users: 2M, Funding: N/A, Margins: 25%
```

### After (Accepted):
```
✅ Created test case 'Apple Revenue Surge'
✅ Created test case 'Google AI Advances'
✅ Created test case 'Tesla EV Expansion'
```

## Testing

To test the fix, generate test cases for any workflow:

1. Go to a workflow detail page
2. Click "Test Cases" tab
3. Click "🤖 Generate 10 Tests"
4. Check the backend logs - should see fewer skip warnings
5. More test cases should be created successfully

## Related Files

- `src/backend/routes/evaluation.py` (lines 920-945, 851-878)
- Function: `generate_eval_cases_for_workflow()`

## Notes

This fix makes the validation more pragmatic - it understands that "N/A" can be a valid value in certain contexts, not always a placeholder. The system now distinguishes between:

- **Placeholder**: `"N/A"` or `"TBD"` as the entire value
- **Valid Data**: `"Revenue: $100M, Employees: 500, Funding: N/A"` where N/A indicates "not applicable" for a specific field

