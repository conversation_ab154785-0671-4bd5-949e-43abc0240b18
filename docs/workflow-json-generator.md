# Workflow JSON Generator

## Overview

The Workflow JSON Generator is an AI-powered tool that automatically designs agentic workflows from specification documents. It takes a Word document (.docx) containing your requirements and generates a complete workflow JSON that can be executed by the agentic-core platform.

## Features

- **AI-Powered Design**: Uses GPT-5-mini to analyze specifications and design appropriate multi-agent workflows
- **Team-Based Workflows**: Automatically creates teams of specialized agents for each workflow step
- **Async Generation**: Background processing with real-time status updates
- **Direct Database Integration**: Generated workflows can be saved directly to the database
- **JSON Preview**: View and copy the generated JSON before creating the workflow

## How It Works

### 1. Document Upload

Upload a Word document (.docx) containing your workflow specification. The specification should describe:
- What the workflow needs to accomplish
- Input data requirements
- Expected outputs
- Any specific processing steps

### 2. AI Analysis

The AI agent analyzes your specification and:
- Identifies the key tasks and objectives
- Breaks down the work into logical steps
- Designs specialized agent teams for each step
- Defines appropriate agent models (GPT-4o, GPT-5-mini, etc.)
- Creates clear goals and success criteria

### 3. Workflow Generation

The system generates a complete workflow JSON including:
- Input and output variables
- Ordered steps (each as a team)
- Agent definitions with personas and skills
- Termination conditions
- Variable passing between steps

### 4. Review and Create

- Review the generated JSON
- Copy it for external use, or
- Save it directly to the database
- View the created workflow immediately

## Usage

### Frontend UI

1. Click **"🚀 Generate from Doc"** in the navigation menu
2. Upload your specification document (.docx)
3. Enter a workflow name
4. Optionally specify a namespace (defaults to "default")
5. Click **"✨ Generate Workflow"**
6. Wait 30-60 seconds for generation (with status updates)
7. Review the generated JSON
8. Click **"💾 Create Workflow in Database"** to save

### API Endpoints

#### Generate Workflow

```bash
POST /workflow-generation/generate
Content-Type: multipart/form-data

file: <specification.docx>
workflow_name: "My Workflow" (optional)
```

Returns:
```json
{
  "task_id": "uuid",
  "status": "queued",
  "message": "Workflow generation started"
}
```

#### Check Status

```bash
GET /workflow-generation/status/{task_id}
```

Returns:
```json
{
  "task_id": "uuid",
  "status": "completed|processing|failed",
  "message": "Status message",
  "workflow_json": { ... },  // Only when completed
  "error": "Error message"   // Only when failed
}
```

#### List Tasks

```bash
GET /workflow-generation/tasks
```

Returns list of all generation tasks with their status.

## Specification Document Guidelines

For best results, your specification document should include:

### 1. Clear Objective
Describe what the workflow should accomplish in 2-3 sentences.

**Example:**
```
This workflow analyzes financial statements and generates a comprehensive 
risk assessment report. It should process balance sheets, income statements, 
and cash flow data to identify potential financial risks.
```

### 2. Input Requirements
List the data or files the workflow will receive.

**Example:**
```
Inputs:
- Balance sheet (Excel or CSV)
- Income statement (Excel or CSV)
- Cash flow statement (Excel or CSV)
- Company information (name, industry, size)
```

### 3. Processing Steps
Describe the main steps or phases of work.

**Example:**
```
Steps:
1. Validate and normalize all financial data
2. Calculate key financial ratios and metrics
3. Identify risk indicators and anomalies
4. Assess overall risk level and assign ratings
5. Generate narrative risk assessment report
```

### 4. Output Requirements
Specify what the workflow should produce.

**Example:**
```
Outputs:
- Risk assessment score (1-10)
- List of identified risks with severity
- Detailed risk analysis report (PDF)
- Recommended mitigation actions
```

### 5. Quality Criteria (Optional)
Describe what makes a successful execution.

**Example:**
```
Success Criteria:
- All data validated with no errors
- Risk scores justified by specific metrics
- Report is clear and actionable
- Recommendations are specific and prioritized
```

## Generated Workflow Structure

The AI generates workflows following this structure:

```json
{
  "description": "Clear description of what the workflow does",
  "variables": {
    "input": {
      "variable_name": {
        "type": "string|int|float|boolean",
        "description": "What this variable is for",
        "default": "optional_default_value"
      }
    },
    "output": {
      "result_name": {
        "type": "string",
        "description": "What this result contains"
      }
    }
  },
  "steps": [
    {
      "order": 1,
      "type": "team",
      "team": {
        "name": "Step 1 Team Name",
        "goal": "What this team needs to accomplish",
        "results": "Expected output from this team",
        "termination": {
          "regex": ["DONE", "COMPLETE"]
        },
        "agents": [
          {
            "name": "agent_role",
            "model": "gpt-4o",
            "persona": "You are a [role description]",
            "skills": "Specific capabilities and tools"
          }
        ],
        "variables": {}
      }
    }
  ]
}
```

## Architecture

### Backend Components

1. **WorkflowJSONGenerator** (`services/workflow_json_generator.py`)
   - AI agent for workflow design
   - Document text extraction
   - Async task management

2. **Routes** (`routes/workflow_json_generation.py`)
   - File upload endpoint
   - Status polling endpoint
   - Workflow creation endpoint

### Frontend Components

1. **WorkflowJSONGenerator Component** (`app.js`)
   - File upload form
   - Status polling with spinner
   - JSON display with syntax highlighting
   - One-click workflow creation

### Agent Design

The workflow designer agent uses:
- **Model**: GPT-5-mini for fast, efficient generation
- **Instructions**: Comprehensive guidelines for workflow design
- **Timeout**: 120 seconds for complex specifications
- **Error Handling**: Robust parsing and validation

## Tips for Best Results

1. **Be Specific**: The more detail you provide, the better the AI can design an appropriate workflow

2. **Structure Your Doc**: Use headings and sections to organize your specification

3. **Include Examples**: Show sample inputs/outputs when possible

4. **Define Success**: Explain what a successful execution looks like

5. **Iterate**: Review the generated workflow and refine your specification if needed

## Troubleshooting

### Generation Takes Too Long
- Simplify your specification
- Break into smaller workflows if very complex
- Check that specification is clear and concise

### Generated Workflow Not Quite Right
- Add more specific details to your specification
- Explicitly state the number and types of agents needed
- Clarify the relationships between steps

### JSON Parsing Error
- The AI occasionally returns malformed JSON
- Try regenerating with a clearer specification
- Manually fix the JSON if minor issues

## Future Enhancements

Planned improvements:
- Support for PDF specifications
- Template-based generation
- Workflow validation before creation
- Version comparison for iterative refinement
- Direct editing of generated JSON in UI

