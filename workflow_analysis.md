# Workflow Analysis - Current 10 Test Cases

## Summary of Files Generated

| Test Case | MD File | DOCX File | Logo Downloaded | Status |
|-----------|---------|-----------|-----------------|--------|
| 1. LVMH (French Luxury) | 1.7K ✅ | 38K ✅ | ❌ No | **SUCCESS** |
| 2. <PERSON> (German Automaker) | 1.5K ✅ | 1.5K ❌ | ✅ Yes (20K vw_logo.png) | **FAILED - Corrupted (Plain Text)** |
| 3. LVMH #2 | 1.7K ✅ | 0B ❌ | ✅ Yes (27K ge_logo.png) | **FAILED - Empty File** |
| 4. Sony (Japanese Tech) | 1.6K ✅ | 42K ✅ | ✅ Yes (3.8K sony_logo.png) | **SUCCESS** |
| 5. Nike (Sports Brand) | 1.2K ✅ | 41K ✅ | ✅ Yes (2.9K nike_logo.png) | **SUCCESS** |
| 6. Samsung (Korean Electronics) | 1.4K ✅ | 38K ✅ | ❌ No | **SUCCESS** |
| 7. Te<PERSON> (Automotive Icon) | 2.7K ✅ | 45K ✅ | ✅ Yes (6.1K tesla_logo.png) | **SUCCESS** |
| 8. GM (Automotive) | 2.2K ✅ | 48K ✅ | ✅ Yes (9.2K gm_logo.png) | **SUCCESS** |
| 9. *(No files)* | ❌ | ❌ | ❌ No | **FAILED - No Output** |
| 10. Starbucks (Coffee Chain) | 1.6K ✅ | 39K ✅ | ❌ No | **SUCCESS** |

**Success Rate: 7/10 (70%)**
**Failures: 3/10 (30%)**

---

## Failure Analysis

### **Root Cause: Workflow Orchestration Problem**

The current workflow being executed has a critical flaw where:
1. The `researcher` agent is attempting to save the DOCX file directly using `save_output(is_text=false)`
2. The `doc_creator` agent is **NOT** calling the `docx_conversion` tool
3. This leads to the error: **"a bytes-like object is required, not 'str'"**

---

## Detailed Failure Cases

### **Case #2: Volkswagen (Corrupted DOCX - Plain Text)**

**Symptoms:**
- DOCX file is 1.5K (same size as MD file)
- File type: `Unicode text, UTF-8 text` (not a Word document)
- Logo was successfully downloaded (20K vw_logo.png)

**What Happened:**
1. `researcher` agent created `research.md` successfully
2. `doc_creator` asked for DOCX conversion
3. `researcher` agent tried to save DOCX using `save_output(is_text=false)` **→ ERROR**
4. Agent retried 10+ times, always got: `"a bytes-like object is required, not 'str'"`
5. Eventually switched to `save_output(is_text=true)` which succeeded
6. Result: Plain text file with `.docx` extension

**Logs:**
```
[2025-10-23 21:52:00] {"role": "assistant", "tool_calls": [{"function": {"arguments": '{"content":"# Volkswagen...","filename":"research.docx","is_text":false}', "name": "save_output"}}]}
[2025-10-23 21:52:00] {"content": "a bytes-like object is required, not 'str'", "role": "tool"}
[2025-10-23 21:52:00] Agent: "It seems there was an issue with saving the DOCX file. Let me try a different approach..."
[2025-10-23 21:52:00] → (Repeats 10+ times with same error)
[2025-10-23 21:52:00] {"role": "assistant", "tool_calls": [{"function": {"arguments": '{"content":"# Volkswagen...","filename":"research.docx","is_text":true}', "name": "save_output"}}]}
[2025-10-23 21:52:00] {"content": "None", "role": "tool"} ✅ Success (but saved as plain text)
```

---

### **Case #3: GE/LVMH (0B Empty DOCX)**

**Symptoms:**
- DOCX file is 0 bytes (completely empty)
- Logo was successfully downloaded (27K ge_logo.png)
- `docx_conversion` tool was actually called

**What Happened:**
1. `researcher` created `research.md` successfully
2. Logo was fetched and inserted successfully: `✅ Logo inserted successfully from: .../ge_logo.png`
3. `docx_conversion` tool started: `🔧 DOCX Conversion Started - Input: research.md, Output: research.docx`
4. **Company name extraction bug:** Extracted `'LVMH  OnePage Brief'` instead of `'LVMH'`
5. **Logs were truncated** - but the file ended up being 0B

**Possible Causes:**
- `doc.save()` succeeded but `BytesIO` or file flush failed
- Python-docx crash during image insertion
- File I/O race condition (buffer not flushed)
- Event loop deadlock with `async` worker pool

---

### **Case #9: No Output**

**Symptoms:**
- No files generated at all (no MD, no DOCX, no logo)

**What Happened:**
- Workflow likely crashed or never started
- No logs found for this execution ID
- Possibly a database/queue issue

---

## MD File Structure Analysis

All successful MD files follow the correct structure:

```markdown
# <Company> — One‑Page Brief

## Executive Summary
(80-120 words)

## Key Metrics
- (5 bullets, one line each)

## Recent Developments
- (3 bullets, one line each)

## Outlook
- (2 bullets, one line each)

## Sources
- (3 URLs or titles)
```

**No structural differences** between successful and failed cases.

---

## DOCX Conversion Tool Logs

### **Successful Cases (Sony, Nike, Tesla, GM, Samsung, Starbucks):**
- Tool executed correctly
- Company logo fetched and inserted (when available)
- File saved with proper size (38-48K)
- File type: `Microsoft OOXML`

### **Failed Cases:**
- **VW:** Tool was **NEVER CALLED** - `researcher` tried to use `save_output` instead
- **GE:** Tool was **CALLED** but file ended up 0B (likely crash or buffer flush issue)

### **Company Name Extraction Bug:**
```
📝 Cleaned company name: 'LVMH  OnePage Brief'  ❌ WRONG
Should be: 'LVMH' ✅
```

The `extract_company_name` function is including extra text from the heading beyond the company name.

---

## Underlying Issues

### **1. Workflow Orchestration (CRITICAL)**
- The workflow being executed does NOT match the intended design
- `researcher` agent has `save_output` in its tools and is trying to create DOCX directly
- `doc_creator` agent is NOT calling `docx_conversion` tool
- This is the **PRIMARY ROOT CAUSE** of failures

### **2. Company Name Extraction Bug**
- Regex in `extract_company_name()` is not removing heading suffix properly
- Includes " — One‑Page Brief" or similar text after the company name
- Affects logo fetching (though it can still work with domain mapping)

### **3. File I/O Race Condition (0B Files)**
- `doc.save()` reports success but file is not fully written
- Likely due to `BytesIO` or OS buffer not being flushed
- Need `time.sleep(0.1)` + re-open verification

### **4. Async Worker Event Loop Deadlock**
- The `async` worker pool is incompatible with nested async operations (MCP calls)
- Leads to event loop crashes and log publishing failures
- Need to switch to `cpu` worker pool

---

## Recommended Fixes

### **Priority 1: Fix Workflow Orchestration (CRITICAL)**
1. Update the workflow JSON to ensure:
   - `researcher` agent has ONLY `["OpenAI_Websearch", "save_output"]` tools
   - `doc_creator` agent has ONLY `["docx_conversion"]` tool
   - Remove `save_output` from `doc_creator`'s tools
2. Upload the correct `company_onepager_workflow_v2.json` to the UI
3. Delete the old "Business_Research" workflow from the database

### **Priority 2: Fix Company Name Extraction**
```python
def extract_company_name(markdown_text):
    lines = markdown_text.splitlines()
    for line in lines[:10]:
        if line.startswith('#'):
            heading = line.lstrip('#').strip()
            
            # Remove common suffixes FIRST
            for suffix in [' — One‑Page Brief', ' Research Report', ' Company Report', ...]:
                heading = heading.replace(suffix, '')
            
            # Remove everything after colon
            if ':' in heading:
                heading = heading.split(':')[0].strip()
            
            # Remove everything after comma
            heading = heading.split(',')[0].strip()
            
            # Remove special characters except spaces and hyphens
            heading = re.sub(r'[^a-zA-Z0-9\\s\\-&]', '', heading).strip()
            
            if heading and len(heading) > 1:
                return heading
    
    return None
```

### **Priority 3: Fix 0B File Bug**
```python
# In docx_conversion tool
doc.save(output_path)
time.sleep(0.1)  # Ensure OS buffer flush

# Verify file was actually written
with open(output_path, 'rb') as verify_file:
    verify_file.seek(0, 2)
    file_size = verify_file.tell()

if file_size == 0:
    raise ValueError("Generated DOCX file is empty")
```

### **Priority 4: Switch to CPU Worker Pool**
```yaml
# In docker-compose.yml
services:
  async-engine-async:
    environment:
      WORKER_POOL: cpu  # Changed from: async
```

---

## Summary

**The workflow is fundamentally broken** because the `researcher` agent is trying to save DOCX files directly instead of letting the `doc_creator` agent call the `docx_conversion` tool. This is causing 30% of test cases to fail with corrupted or empty DOCX files.

The fix is to **upload the correct workflow JSON to the UI** and ensure the old workflow is deleted from the database.

