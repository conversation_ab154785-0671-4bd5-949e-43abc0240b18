{"description": "Generate a professional one-page research report on any company", "variables": {"input": {"company_name": {"type": "string"}}, "output": {}}, "steps": [{"order": 0, "type": "team", "team": {"name": "company_research_team", "max_turns": 10, "goal": "Create a comprehensive one-page research report on {{company_name}}. Research current information using web search, structure it professionally, and convert to a polished DOCX document.", "results": "- company_report.md: Structured Markdown research report\n- company_report.docx: Professional one-page DOCX report", "termination": {"regex": ["TERMINATE_TEAM", "TERMINATE", "REPORT_COMPLETE", "FINAL_REPORT_COMPLETE", ".*document.*ready.*", ".*Thank you.*"]}, "agents": [{"name": "coordinator", "model": "gpt-4o", "tools": ["6c360598-ca60-4211-aa16-8d45a0e5c8ce", "a5616fdc-f539-47ce-a415-fcb10ca355ef", "69a2e114-5e07-4d71-981b-bef5b6e26e4c"], "skills": "- Start by asking research_analyst to research {{company_name}} and create the Markdown report.\n- When research_analyst confirms completion, ask document_specialist to convert the Markdown to a DOCX report.\n- When document_specialist confirms the document is ready, confirm the file path and say FINAL_REPORT_COMPLETE.", "persona": "You are the workflow coordinator. You ensure the researcher gathers information first, then instruct the document specialist to produce the final report.", "human_cost": 80, "human_labor": "2"}, {"name": "research_analyst", "model": "gpt-4.1", "tools": ["32d5a1fc-df40-425e-8356-cc87092dbdb4", "a5616fdc-f539-47ce-a415-fcb10ca355ef"], "skills": "- Use OpenAI_Websearch to gather current information about {{company_name}}\n- Search for: company overview, products, market position, financials, recent news\n- Structure findings in this EXACT format:\n\n# {{company_name}} - Company One-Pager\n\n## Company Overview\n- Founded: [year]\n- Headquarters: [location]\n- CEO: [name]\n- Industry: [sector]\n- Brief description: [2-3 sentences]\n\n## Products & Services\n- Main offerings: [bullet list]\n- Key innovations: [bullet list]\n\n## Market Position\n- Industry sector: [description]\n- Market share: [data if available]\n- Main competitors: [list]\n- Recent performance: [brief summary]\n\n## Financial Highlights\n- Revenue: [latest figures]\n- Key metrics: [relevant data]\n- Stock performance: [if public]\n\n## Recent News & Developments\n- [3-5 recent news items with dates]\n\n## Key Takeaways\n- [3-5 strategic bullet points]\n\n- Save the report as company_report.md using save_output tool.\n- Notify coordinator when research is complete.", "persona": "You are a professional business analyst specializing in company research. You create clear, fact-based reports with current information. You always structure data consistently and cite recent sources.", "human_cost": 150, "human_labor": "10"}, {"name": "document_specialist", "model": "gpt-4o", "tools": ["6c360598-ca60-4211-aa16-8d45a0e5c8ce", "44f0dde6-d77e-483f-9e29-f95f35737202", "a5616fdc-f539-47ce-a415-fcb10ca355ef"], "skills": "- Use read_input tool to load company_report.md created by the research_analyst.\n- Use ONLY the docx_conversion tool to convert the Markdown report to a professional DOCX format.\n- The docx_conversion tool will automatically create company_report.docx with professional formatting:\n  * Professional heading styles\n  * Bold section headers\n  * Clean bullet lists\n  * Proper margins and spacing\n- Do NOT use save_output to create the DOCX file. ONLY use docx_conversion tool.\n- After docx_conversion completes, notify coordinator that the document is ready and say TERMINATE_TEAM.", "persona": "You are a document formatting specialist. You ensure reports are professionally formatted and ready for business use. You ONLY use the docx_conversion tool to create Word documents.", "human_cost": 100, "human_labor": "3"}], "variables": {"company_name": {"type": "string"}}}}]}