{"description": "Research workflow using web search - clean JSON with tool names only!", "variables": {"input": {"company_name": {"type": "string"}}, "output": {}}, "steps": [{"order": 0, "type": "team", "team": {"name": "search_team", "max_turns": 20, "goal": "Research {{company_name}} using the web search tool, assemble findings in Markdown, then convert to a ONE-PAGE DOCX report (target 300–400 words).", "results": "- research.md: Markdown file containing structured research results\n- research.docx: Final formatted DOCX report", "termination": {"regex": ["TERMINATE_TEAM", "TERMINATE", ".*Goodbye.*", ".*model.*saved.*", ".*Thank you.*", "FINAL_REPORT_COMPLETE"]}, "agents": [{"name": "coordinator", "model": "gpt-4o", "tools": ["read_input"], "skills": "- Start by asking the researcher to perform the search and write a Markdown summary for '{{company_name}}'.\n- When the researcher confirms completion, ask the doc_creator to convert the Markdown to a DOCX report.\n- When done, confirm the file path and say FINAL_REPORT_COMPLETE.\n- DO NOT use any tools yourself - just coordinate.", "persona": "You are the workflow coordinator. You ensure the researcher gathers information first, then instruct the doc_creator to produce the final report.", "human_cost": 100, "human_labor": "3"}, {"name": "researcher", "model": "gpt-4o", "tools": ["OpenAI_Websearch", "save_output"], "skills": "ONE-PAGE SUMMARY CONSTRAINTS (STRICT):\n- Use OpenAI_Websearch to gather 3–5 reliable sources for '{{company_name}}'.\n- Produce a ONE-PAGE Markdown summary with a TOTAL length of 300–400 words.\n- Structure EXACTLY as follows (keep within limits):\n  # {{company_name}} — One‑Page Brief\n  ## Executive Summary (80–120 words)\n  ## Key Metrics (max 5 bullets, one line each)\n  ## Recent Developments (max 3 bullets, one line each)\n  ## Outlook (max 2 bullets, one line each)\n  ## Sources (3 short URLs or titles)\n- Be concise; no tables, no images, no code blocks; avoid redundancy.\n- Save to research.md via save_output (is_text=true).\n- Confirm completion to coordinator.", "persona": "You are a professional research analyst with access to a web search tool.", "human_cost": 150, "human_labor": "8"}, {"name": "doc_creator", "model": "gpt-4o", "tools": ["docx_conversion"], "skills": "YOUR ONLY JOB:\n- Call docx_conversion(markdown_file='research.md', output_docx='research.docx') to convert the file.\n- The docx_conversion tool will automatically:\n  * Read the research.md file created by the researcher\n  * Extract the company name from the heading\n  * Fetch and insert the company logo\n  * Apply Access Capital branding\n  * Save the formatted DOCX file\n- After conversion completes, say TERMINATE_TEAM.\n\nDO NOT:\n- Do NOT call save_output (you don't have this tool)\n- Do NOT call read_input (docx_conversion handles file reading)\n- Do NOT modify or rewrite the Markdown content\n- Do NOT add extra content", "persona": "You are a document conversion specialist. Your ONLY job is to call the docx_conversion tool to convert research.md to research.docx. You do NOT save files or modify content.", "human_cost": 120, "human_labor": "5"}], "variables": {"company_name": {"type": "string"}}}}]}