# Axon-PFC: Workflow Execution & Evaluation Platform

A comprehensive system for creating, executing, testing, and monitoring AI agent workflows powered by **agentic-core**.

---

## 🎯 What is Axon-PFC?

Axon-PFC provides a complete workflow lifecycle management system:

- **🔧 Create Workflows** - Define multi-agent teams via JSON or UI
- **▶️  Execute Workflows** - Run workflows via agentic-core's engine
- **✅ Test Workflows** - Create test cases with expected inputs/outputs
- **📊 Monitor Executions** - Real-time dashboard with system health
- **🔍 Validate Results** - Automatic output validation and pass/fail tracking

---

## 🚀 Quick Start

### Prerequisites

- Python 3.13+
- Docker & Docker Compose
- [agentic-core](https://github.com/your-org/agentic-core) installed at `/Users/<USER>/projects/agentic-core`
- OpenAI API key

### Installation

```bash
# 1. Clone repository
git clone <repo-url> axon-pfc
cd axon-pfc

# 2. Setup agentic-core (see SETUP.md for details)
cd ../agentic-core
# Apply pyproject.toml changes
# Add OPENAI_API_KEY to .env
docker compose up -d

# 3. Setup axon-pfc
cd ../axon-pfc
python3.13 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 4. Configure environment
cp .env.example .env
# Edit .env with your database/rabbitmq ports

# 5. Run database migration
python -c "
from sqlalchemy import create_engine, text
from dotenv import load_dotenv
import os
load_dotenv()
engine = create_engine(os.getenv('DATABASE_URL'))
with open('migrations/add_evaluation_schema.sql', 'r') as f:
    migration_sql = f.read()
with engine.begin() as conn:
    conn.execute(text(migration_sql))
    print('✅ Migration completed')
"

# 6. Start services
./start.sh
```

### Access

- **Axon-PFC UI**: http://localhost:8080
- **Agentic-Core UI**: http://localhost:3001
- **API Docs**: http://localhost:8000/docs
- **Dashboard**: http://localhost:8080 → Dashboard button

---

## 📚 Documentation

### Setup & Configuration
- **[SETUP.md](./SETUP.md)** - Complete installation guide with agentic-core integration
- **[.env.example](./.env.example)** - Environment variable template

### Architecture & Design
- **[docs/architecture.md](./docs/architecture.md)** - System architecture and component design
- **[docs/agentic-core-changes.md](./docs/agentic-core-changes.md)** - Required changes to agentic-core

### User Guides
- **[docs/workflow-guide.md](./docs/workflow-guide.md)** - Creating and configuring workflows
- **[docs/evaluation-system.md](./docs/evaluation-system.md)** - Testing and validation system
- **[docs/ppt-ingestion.md](./docs/ppt-ingestion.md)** - PPT/PDF tool and service
- **[docs/api-reference.md](./docs/api-reference.md)** - Complete REST API documentation

### Reference
- **[docs/quick-start.md](./docs/quick-start.md)** - Quick reference guide
- **[docs/implementation-plan.md](./docs/implementation-plan.md)** - Original implementation plan

---

## 🏗️ System Architecture

```
┌─────────────────────────────────────────┐
│           Frontend (React)              │
│  • Workflow Management                  │
│  • Test Case Creation                   │
│  • Real-time Dashboard                  │
└──────────────┬──────────────────────────┘
               │ HTTP/REST API
┌──────────────┴──────────────────────────┐
│         Backend (FastAPI)               │
│  • Workflow CRUD API                    │
│  • Test Execution API                   │
│  • Dashboard API                        │
│  • EvalRunner Service                   │
│  • EvalMonitor Daemon                   │
└──────────────┬──────────────────────────┘
               │
     ┌─────────┴──────────┐
     │                    │
┌────▼────────┐  ┌────────▼─────────┐
│ PostgreSQL  │  │    RabbitMQ      │
│ (agentic-   │  │  (agentic-core)  │
│  core)      │  │                  │
└─────────────┘  └──────┬───────────┘
                        │
            ┌───────────┴───────────┐
            │                       │
     ┌──────▼──────┐       ┌───────▼──────┐
     │ Async Worker│       │Flyweight     │
     │  (Docker)   │       │Worker (Docker)│
     └─────────────┘       └──────────────┘
```

---

## ✨ Key Features

### Workflow Management
- **JSON-based definitions** - Human-readable workflow format
- **Multi-agent teams** - Coordinate multiple AI agents
- **Version control** - Track workflow changes over time
- **Import/Export** - Share workflows as JSON files

### Test Case System
- **Input/Output validation** - Define expected behavior
- **Wildcard matching** - Flexible output validation
- **Test suites** - Group related test cases
- **Batch execution** - Run multiple tests simultaneously

### Real-time Dashboard
- **System health** - Database and RabbitMQ status
- **Queue monitoring** - Message counts and consumer status
- **Active runs** - Watch executions in progress
- **Recent completions** - View pass/fail history
- **Cancellation** - Stop stuck workflows

### Automatic Validation
- **Background daemon** - Monitors completed executions
- **Output comparison** - Validates against expected results
- **Pass/fail tracking** - Historical success rates
- **Detailed reports** - Validation output and notes

### Document Processing (PPT/PDF Ingestion)
- **Registered tool** - Available in Agentic-Core UI Tools tab
- **Multi-format support** - Process PPTX and PDF files
- **OCR extraction** - Extract text using PyMuPDF
- **Vision enhancement** - Use GPT-4 Vision for complex content
- **CLI & API** - Command-line tool and REST endpoints
- **Workflow integration** - Use as a tool in any workflow

---

## 🛠️ Technology Stack

### Backend
- **FastAPI** - Modern Python web framework
- **SQLAlchemy** - ORM for database access
- **Pydantic** - Data validation
- **aio-pika** - Async RabbitMQ client

### Frontend
- **React** (via CDN) - UI framework
- **Vanilla JavaScript** - No build process
- **CSS3** - Modern styling

### Infrastructure
- **PostgreSQL** - Relational database
- **RabbitMQ** - Message broker
- **Docker** - Containerization (agentic-core)
- **Agentic-Core** - Workflow execution engine

---

## 📊 Project Structure

```
axon-pfc/
├── SETUP.md                    # Complete setup guide
├── README.md                   # This file
├── start.sh                    # Start all services
├── stop.sh                     # Stop all services
├── requirements.txt            # Python dependencies
├── .env.example                # Environment template
│
├── docs/                       # Documentation
│   ├── architecture.md         # System design
│   ├── workflow-guide.md       # Creating workflows
│   ├── evaluation-system.md    # Testing system
│   ├── api-reference.md        # API docs
│   └── ...
│
├── migrations/                 # Database migrations
│   └── add_evaluation_schema.sql
│
├── src/
│   ├── backend/                # FastAPI backend
│   │   ├── main.py             # API server
│   │   ├── models/             # SQLAlchemy models
│   │   ├── routes/             # API endpoints
│   │   ├── services/           # Background services
│   │   └── workflow_json_serializer.py
│   │
│   └── frontend/               # React frontend
│       ├── index.html          # Main page
│       ├── app.js              # React app
│       └── style.css           # Styles
│
├── scripts/                    # Utility scripts (legacy)
│   └── README.md               # Script documentation
│
└── tests/                      # Unit tests
    ├── test_api.py
    └── test_json_serializer.py
```

---

## 🔧 Usage Examples

### Create a Workflow

```bash
curl -X POST http://localhost:8000/api/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Greeting Generator",
    "definition": {
      "type": "team",
      "variables": {
        "topic": {"type": "Input", "data_type": "str", "direction": "Input"},
        "greeting": {"type": "Output", "data_type": "str", "direction": "Output"}
      },
      "agents": [
        {
          "name": "greeter",
          "model": "gpt-4o",
          "skills": "Create friendly greetings. Use save_variable and say TERMINATE.",
          "persona": "Warm and welcoming"
        }
      ]
    },
    "user_id": "your-user-id",
    "tenant_id": "your-tenant-id"
  }'
```

### Create a Test Case

```bash
curl -X POST http://localhost:8000/api/eval-cases \
  -H "Content-Type: application/json" \
  -d '{
    "workflow_id": "workflow-uuid",
    "name": "Basic Greeting Test",
    "input_variables": {"topic": "space exploration"},
    "expected_output": {"greeting": "*"},
    "user_id": "your-user-id"
  }'
```

### Run a Test

```bash
curl -X POST http://localhost:8000/api/eval-cases/{case-id}/run \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "your-user-id",
    "tenant_id": "your-tenant-id"
  }'
```

### Monitor Dashboard

```bash
curl http://localhost:8000/api/dashboard/status | jq
```

---

## 🧪 Running Tests

```bash
# Unit tests
pytest tests/

# Integration tests (requires running services)
pytest tests/ --integration

# API tests
pytest tests/test_api.py -v
```

---

## 🐛 Troubleshooting

### Common Issues

#### "Failed to fetch" when creating test cases
```bash
# Check backend logs
tail -f backend.log

# Verify database connection
docker exec agentic-core-postgres-1 psql -U postgres -d invisible -c "SELECT 1"
```

#### Workflows fail with "AuthenticationError"
```bash
# Check OpenAI API key in agentic-core
cd ../agentic-core
cat .env | grep OPENAI_API_KEY

# Restart workers
docker compose restart async-engine-async async-engine-flyweight
```

#### Workflows get stuck
```bash
# Cancel via dashboard or API
curl -X POST http://localhost:8000/api/eval-runs/{run-id}/cancel \
  -d '{"flow_execution_id": "exec-id"}'
```

See **[SETUP.md](./SETUP.md)** for complete troubleshooting guide.

---

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

---

## 📝 API Documentation

Full API documentation available at:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **[docs/api-reference.md](./docs/api-reference.md)**

---

## 🔒 Security Considerations

- **API Keys**: Store in `.env` (gitignored)
- **Database**: localhost only (not exposed publicly)
- **CORS**: Configure for production
- **Authentication**: Add JWT/OAuth for production

---

## 📈 Performance

- **Backend**: ~100 concurrent requests (single instance)
- **Dashboard**: 2-second polling interval
- **Workers**: Scalable via Docker Compose replicas

For scaling strategies, see **[docs/architecture.md](./docs/architecture.md)**.

---

## 🗺️ Roadmap

- [ ] WebSocket-based dashboard (real-time updates)
- [ ] Batch test execution UI
- [ ] Historical trend charts
- [ ] Custom validation rules
- [ ] Workflow templates library
- [ ] Multi-user authentication
- [ ] Cloud deployment guides

---

## 📄 License

MIT License - see [LICENSE](./LICENSE) file for details

---

## 🙏 Acknowledgments

- **agentic-core** - Workflow execution engine
- **FastAPI** - Web framework
- **React** - Frontend library
- **OpenAI** - LLM provider

---

## 📞 Support

- **Documentation**: See [docs/](./docs/) directory
- **Issues**: GitHub Issues
- **Setup Help**: See [SETUP.md](./SETUP.md)

---

**Last Updated:** 2025-10-15  
**Version:** 1.0.0
