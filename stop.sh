#!/bin/bash
# Stop all Axon-PFC services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Stopping Axon-PFC Services ===${NC}"

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
AGENTIC_CORE_PATH="${AGENTIC_CORE_PATH:-../agentic-core}"

# Stop frontend
if [ -f "$PROJECT_ROOT/.frontend.pid" ]; then
    FRONTEND_PID=$(cat "$PROJECT_ROOT/.frontend.pid")
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        echo -e "${YELLOW}Stopping frontend (PID: $FRONTEND_PID)...${NC}"
        kill $FRONTEND_PID 2>/dev/null || true
        echo -e "${GREEN}✓ Frontend stopped${NC}"
    fi
    rm "$PROJECT_ROOT/.frontend.pid"
fi

# Stop eval monitor
if [ -f "$PROJECT_ROOT/.eval_monitor.pid" ]; then
    EVAL_MONITOR_PID=$(cat "$PROJECT_ROOT/.eval_monitor.pid")
    if kill -0 $EVAL_MONITOR_PID 2>/dev/null; then
        echo -e "${YELLOW}Stopping eval monitor (PID: $EVAL_MONITOR_PID)...${NC}"
        kill $EVAL_MONITOR_PID 2>/dev/null || true
        echo -e "${GREEN}✓ Eval monitor stopped${NC}"
    fi
    rm "$PROJECT_ROOT/.eval_monitor.pid"
fi

# Stop GraphQL log writer
if [ -f "$PROJECT_ROOT/.graphql_log_writer.pid" ]; then
    LOG_WRITER_PID=$(cat "$PROJECT_ROOT/.graphql_log_writer.pid")
    if kill -0 $LOG_WRITER_PID 2>/dev/null; then
        echo -e "${YELLOW}Stopping GraphQL log writer (PID: $LOG_WRITER_PID)...${NC}"
        kill $LOG_WRITER_PID 2>/dev/null || true
        echo -e "${GREEN}✓ GraphQL log writer stopped${NC}"
    fi
    rm "$PROJECT_ROOT/.graphql_log_writer.pid"
fi



# Stop backend
if [ -f "$PROJECT_ROOT/.backend.pid" ]; then
    BACKEND_PID=$(cat "$PROJECT_ROOT/.backend.pid")
    if kill -0 $BACKEND_PID 2>/dev/null; then
        echo -e "${YELLOW}Stopping backend (PID: $BACKEND_PID)...${NC}"
        kill $BACKEND_PID 2>/dev/null || true
        echo -e "${GREEN}✓ Backend stopped${NC}"
    fi
    rm "$PROJECT_ROOT/.backend.pid"
fi

# Kill any remaining processes on ports
echo -e "${YELLOW}Checking for any remaining processes on ports 8000 and 8080...${NC}"
lsof -ti:8000 | xargs kill -9 2>/dev/null || true
lsof -ti:8080 | xargs kill -9 2>/dev/null || true

# Stop docker containers
if [ -d "$AGENTIC_CORE_PATH" ]; then
    echo -e "${YELLOW}Stopping Docker containers (postgres, rabbitmq, graphql, ui, workers)...${NC}"
    cd "$AGENTIC_CORE_PATH"
    
    # Use the same ports override file if it exists
    PORTS_OVERRIDE="$PROJECT_ROOT/docker-compose.ports.yml"
    if [ -f "$PORTS_OVERRIDE" ]; then
        docker compose -f docker-compose.yml -f "$PORTS_OVERRIDE" stop postgres rabbitmq graphql ui async-engine-async async-engine-flyweight 2>/dev/null || true
    else
        docker compose stop postgres rabbitmq graphql ui async-engine-async async-engine-flyweight 2>/dev/null || true
    fi
    echo -e "${GREEN}✓ Docker containers stopped${NC}"
fi

echo -e "${GREEN}=== All services stopped ===${NC}"


