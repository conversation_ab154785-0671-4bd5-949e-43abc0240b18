{"description": "Research workflow using web search - now with tool names instead of UUIDs!", "variables": {"input": {"topic": {"type": "string"}}, "output": {}}, "steps": [{"order": 0, "type": "team", "team": {"name": "search_team", "goal": "Research a topic using the web search tool, assemble findings in Markdown, then convert to a DOCX report.\n{{topic}}", "results": "- research.md: Markdown file containing structured research results\n- research.docx: Final formatted DOCX report", "termination": {"regex": ["TERMINATE_TEAM", "TERMINATE", ".*Goodbye.*", ".*model.*saved.*", ".*Thank you.*"]}, "agents": [{"name": "coordinator", "model": "gpt-4o", "tools": ["save_output", "save_variable", "read_input"], "skills": "- Start by asking the researcher to perform the search and write a Markdown summary.\n- When the researcher confirms completion, ask the doc_creator to convert the Markdown to a DOCX report.\n- When done, confirm the file path and say FINAL_REPORT_COMPLETE.", "persona": "You are the workflow coordinator. You ensure the researcher gathers information first, then instruct the doc_creator to produce the final report.", "human_cost": 100, "human_labor": "3"}, {"name": "researcher", "model": "gpt-4.1", "tools": ["OpenAI_Websearch", "save_output"], "skills": "- Use the web_search tool to gather 3–5 current and reliable sources for '{{topic}}'.\n- Summarize key findings in Markdown format (headings, bullet points, and citations).\n- Save the result to research.md using the save_output tool.\n- Notify coordinator when done.", "persona": "You are a professional research analyst with access to a web search tool.", "human_cost": 150, "human_labor": "8"}, {"name": "doc_creator", "model": "gpt-4o", "tools": ["save_output", "save_variable", "read_input", "docx_conversion"], "skills": "- Load research.md created by the researcher.\n- Use the doc_conversion tool to convert it into a formatted DOCX report titled '{{topic}} Report'.\n- Save output as research.docx.\n- Notify coordinator when finished and say TERMINATE_TEAM.", "persona": "You are a document generation specialist skilled at converting Markdown to polished reports.", "human_cost": 120, "human_labor": "5"}], "variables": {"topic": {"type": "string"}}}}]}