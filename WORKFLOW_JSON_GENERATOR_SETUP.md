# Workflow JSON Generator - Setup & Testing

## What Was Built

A complete AI-powered workflow generator that takes Word documents with specifications and generates executable workflow JSON files.

### Components Created

1. **Backend Service** (`src/backend/services/workflow_json_generator.py`)
   - AI agent using GPT-5-mini to design workflows
   - Word document text extraction
   - Async task management with status tracking

2. **API Routes** (`src/backend/routes/workflow_json_generation.py`)
   - `POST /workflow-generation/generate` - Upload and start generation
   - `GET /workflow-generation/status/{task_id}` - Poll generation status
   - `GET /workflow-generation/tasks` - List all tasks
   - `GET /workflow-generation/health` - Health check

3. **Frontend UI** (updates to `src/frontend/app.js`)
   - New "🚀 Generate from Doc" button in navigation
   - Complete workflow generation UI with file upload
   - Real-time status updates with polling
   - JSON preview and one-click workflow creation

4. **Documentation**
   - `docs/workflow-json-generator.md` - Complete usage guide
   - `docs/workflow-json-generator-troubleshooting.md` - Troubleshooting guide
   - This file - Setup and testing instructions

## Fixing "Failed to get status" Error

I've made several improvements to help diagnose this error:

### 1. Better Error Handling in Backend

**Added to `workflow_json_generation.py`:**
- Try/catch around all endpoints
- Detailed error messages in responses
- Import error handling with fallbacks
- Print statements for debugging

### 2. Better Error Handling in Frontend

**Updated `app.js`:**
- Captures actual API error messages
- Logs errors to console for debugging
- Shows detailed error messages to user

### 3. Diagnostic Tools

**Created test script** (`scripts/test_workflow_generator.py`):
- Tests all components systematically
- Creates test documents
- Runs full generation cycle
- Shows detailed output at each step

## How to Test and Diagnose

### Quick Diagnostic Check

```bash
# 1. Activate virtual environment
cd /Users/<USER>/projects/axon-pfc
source venv/bin/activate

# 2. Verify imports work
python -c "import sys; sys.path.insert(0, 'src/backend'); \
  from services.workflow_json_generator import get_workflow_json_generator; \
  print('✅ Service OK')"

# 3. Verify route loads
python -c "import sys; sys.path.insert(0, 'src/backend'); \
  from routes.workflow_json_generation import router; \
  print(f'✅ Route OK - {len(router.routes)} endpoints')"

# 4. Check if backend is running
curl http://localhost:8000/workflow-generation/health
# Should return: {"status":"healthy","service":"workflow-json-generation"}
```

### Run Full Test Suite

```bash
cd /Users/<USER>/projects/axon-pfc
source venv/bin/activate
python scripts/test_workflow_generator.py
```

This will:
1. ✅ Test imports
2. ✅ Initialize generator
3. ✅ Create test document
4. ✅ Extract text
5. ✅ Generate workflow JSON (30-60s)
6. ✅ Display results

### Start Backend Server

If backend isn't running:

```bash
cd /Users/<USER>/projects/axon-pfc
source venv/bin/activate
cd src/backend
python main.py
```

Backend should show:
```
INFO:     Started server process
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000
```

### Test API Endpoints Directly

```bash
# Create a test document first
python3 << 'EOF'
from docx import Document
doc = Document()
doc.add_heading('Test Workflow', 0)
doc.add_paragraph('Objective: Process data')
doc.add_paragraph('Inputs: data_file')
doc.add_paragraph('Steps: 1. Load data 2. Process 3. Output')
doc.add_paragraph('Outputs: result')
doc.save('/tmp/test.docx')
print('✅ Created /tmp/test.docx')
EOF

# Upload and generate
curl -X POST http://localhost:8000/workflow-generation/generate \
  -F "file=@/tmp/test.docx" \
  -F "workflow_name=Test" \
  -v

# This should return JSON with a task_id
# Copy the task_id and check status:
curl http://localhost:8000/workflow-generation/status/PASTE_TASK_ID_HERE
```

### Test via Frontend

1. **Start backend** (if not already running)
2. **Open browser** to: `http://localhost:8000/src/frontend/index.html`
3. **Click** "🚀 Generate from Doc" button
4. **Upload** a .docx file with a simple spec
5. **Watch** the status updates

**Open browser DevTools (F12)** and check:
- **Console tab**: Look for JavaScript errors or API errors
- **Network tab**: Check the actual HTTP requests and responses
  - Find the `/workflow-generation/generate` request
  - Find the `/workflow-generation/status/{id}` requests
  - Click on them to see request/response details

## Common Issues and Solutions

### Issue: "Failed to get status"

**Most likely causes:**

1. **Backend not running**
   ```bash
   curl http://localhost:8000/health
   # If this fails, start the backend
   ```

2. **Route not loaded** (need to restart backend)
   ```bash
   curl http://localhost:8000/workflow-generation/health
   # If 404, restart: pkill -f "python.*main.py" then start again
   ```

3. **Task ID doesn't exist**
   - The generation endpoint might have failed
   - Check backend logs for errors during /generate

4. **CORS issue**
   - Check browser console for CORS errors
   - Make sure accessing from `http://localhost:8000`

### Issue: "Workflow JSON Generator service not available"

**Cause**: Import failed

**Solution**:
```bash
# Reinstall dependencies
cd /Users/<USER>/projects/axon-pfc
source venv/bin/activate
pip install -r requirements.txt

# Verify imports
python -c "from docx import Document; print('✅ python-docx')"
python -c "from agents import Agent; print('✅ openai-agents')"
```

### Issue: Generation stuck in "processing"

**Cause**: Agent execution failed or timed out

**Solution**: Check backend logs for:
- OpenAI API errors
- Timeout errors
- JSON parsing errors

**Verify OpenAI API key**:
```bash
cat .env | grep OPENAI_API_KEY
# Should show: OPENAI_API_KEY=sk-...
```

### Issue: JSON parsing error

**Cause**: AI returned invalid JSON

**Solution**: This is rare but can happen. The agent has been instructed to return valid JSON, but if it fails:
1. Try again with a clearer specification
2. Check backend logs for the raw response
3. The system will show the error message

## Environment Requirements

- ✅ Python 3.10 or higher
- ✅ Virtual environment activated
- ✅ All dependencies installed: `pip install -r requirements.txt`
- ✅ OpenAI API key set in `.env`
- ✅ Backend server running on port 8000
- ✅ Network access for OpenAI API calls

## Verify Everything is Working

Run this complete check:

```bash
#!/bin/bash
echo "🔍 Checking Workflow JSON Generator Setup..."
echo ""

cd /Users/<USER>/projects/axon-pfc
source venv/bin/activate

# 1. Python version
echo "1. Python version:"
python --version
echo ""

# 2. Dependencies
echo "2. Checking dependencies:"
python -c "from docx import Document; print('  ✅ python-docx')" 2>&1
python -c "from agents import Agent; print('  ✅ openai-agents')" 2>&1
python -c "import openai; print('  ✅ openai')" 2>&1
echo ""

# 3. Service imports
echo "3. Service imports:"
python -c "import sys; sys.path.insert(0, 'src/backend'); from services.workflow_json_generator import get_workflow_json_generator; print('  ✅ Service OK')" 2>&1
echo ""

# 4. Route imports
echo "4. Route imports:"
python -c "import sys; sys.path.insert(0, 'src/backend'); from routes.workflow_json_generation import router; print('  ✅ Routes OK')" 2>&1
echo ""

# 5. OpenAI API key
echo "5. OpenAI API key:"
if grep -q "OPENAI_API_KEY=" .env; then
    echo "  ✅ API key configured in .env"
else
    echo "  ❌ API key NOT found in .env"
fi
echo ""

# 6. Backend status
echo "6. Backend server:"
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "  ✅ Backend is running"
    curl -s http://localhost:8000/workflow-generation/health | python -m json.tool
else
    echo "  ❌ Backend is NOT running"
    echo "     Start with: cd src/backend && python main.py"
fi
echo ""

echo "✅ Setup check complete!"
echo ""
echo "To test the workflow generator, run:"
echo "  python scripts/test_workflow_generator.py"
```

Save this as `scripts/check_setup.sh` and run it:

```bash
chmod +x scripts/check_setup.sh
./scripts/check_setup.sh
```

## Next Steps

Once everything is working:

1. **Create specification documents** in Word format
2. **Use the frontend UI** to generate workflows
3. **Review generated JSON** before creating
4. **Save to database** and execute workflows

See `docs/workflow-json-generator.md` for detailed usage instructions.

## Getting Help

If you're still having issues after following this guide:

1. **Check backend logs** - actual error messages are there
2. **Check browser console** - shows client-side errors
3. **Run test script** - `python scripts/test_workflow_generator.py`
4. **Check troubleshooting guide** - `docs/workflow-json-generator-troubleshooting.md`

