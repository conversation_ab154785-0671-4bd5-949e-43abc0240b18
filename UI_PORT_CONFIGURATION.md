# UI Pod Port Configuration - Summary

## Issue Discovered
The agentic-core UI pod was not being properly managed by the axon-pfc start/stop scripts, even though it was configured in the port override file.

## Root Cause
The `start.sh` and `stop.sh` scripts were missing the `ui` service from their Docker Compose commands, so the UI container was not being started/stopped with the other services.

## Port Configuration

### Static Port Mappings (`docker-compose.ports.yml`)
The following ports are fixed for local development:

| Service | Container Port | Host Port | URL |
|---------|----------------|-----------|-----|
| postgres | 5432 | 5432 | localhost:5432 |
| rabbitmq | 5672, 15672 | 5672, 15672 | localhost:5672 |
| graphql | 8000 | 5002 | http://localhost:5002/graphql/ |
| **ui** | **8080** | **3001** | **http://localhost:3001** |

### How Port Fixing Works

1. **Agentic-Core Default**: Uses dynamic ports (Docker assigns random ports)
2. **Axon-PFC Override**: Uses `docker-compose.ports.yml` to fix ports
3. **Start Script**: Applies the override with `-f docker-compose.yml -f docker-compose.ports.yml`

### UI Configuration Details

#### Nginx Proxy Configuration
The UI uses nginx to proxy requests to backend services. Key routes:

- `/graphql` → `http://graphql:8000/graphql` (GraphQL API)
- `/auth` → `http://graphql:8000/auth` (Authentication)
- `/files` → `http://file_service:8001` (File service)
- `/dev` → `http://host.docker.internal:3000` (Development server)

#### Docker Networking
- The UI communicates with GraphQL using Docker's **internal DNS** (`graphql:8000`)
- This resolves to the GraphQL container's IP address within the Docker network
- When GraphQL restarts, its IP may change, so nginx needs to resolve the DNS again
- **Solution**: Restart the UI container after restarting GraphQL to refresh DNS cache

## Changes Made

### 1. `start.sh` - Added UI to startup sequence
**Before:**
```bash
docker compose -f docker-compose.yml -f "$PORTS_OVERRIDE" up -d postgres rabbitmq graphql async-engine-async async-engine-flyweight
```

**After:**
```bash
docker compose -f docker-compose.yml -f "$PORTS_OVERRIDE" up -d postgres rabbitmq graphql ui async-engine-async async-engine-flyweight
```

### 2. `stop.sh` - Added UI to shutdown sequence
**Before:**
```bash
docker compose -f docker-compose.yml -f "$PORTS_OVERRIDE" stop postgres rabbitmq graphql async-engine-async async-engine-flyweight
```

**After:**
```bash
docker compose -f docker-compose.yml -f "$PORTS_OVERRIDE" stop postgres rabbitmq graphql ui async-engine-async async-engine-flyweight
```

### 3. Updated startup messages to reflect UI
- Added "GraphQL: http://localhost:5002/graphql/" to services list
- Added "Agentic UI: http://localhost:3001" to services list
- Added "Agentic-Core UI: http://localhost:3001" to browser URLs

## Verification

### Check UI Status
```bash
cd /Users/<USER>/projects/agentic-core
docker compose ps ui
```

### Check UI Logs
```bash
cd /Users/<USER>/projects/agentic-core
docker compose logs ui -f
```

### Test GraphQL Connectivity
```bash
# From host machine
curl -X POST http://localhost:5002/graphql/ \
  -H "Content-Type: application/json" \
  -d '{"query":"{ __typename }"}' \
  -u system:foo

# From UI container
docker compose exec ui curl http://graphql:8000/graphql/ \
  -X POST \
  -H "Content-Type: application/json" \
  -d '{"query":"{ __typename }"}' \
  -u system:foo
```

## Common Issues

### 404 Errors from GraphQL
**Symptom**: UI shows 404 errors when trying to connect to GraphQL

**Cause**: GraphQL container IP changed but nginx cached the old IP

**Solution**:
```bash
cd /Users/<USER>/projects/agentic-core
docker compose restart graphql
docker compose restart ui
```

### UI Not Starting
**Symptom**: UI container not running after `./start.sh`

**Cause**: Missing from docker-compose command (now fixed)

**Solution**: Use the updated `start.sh` script

### UI Using Wrong Port (Dynamic instead of 3001)
**Symptom**: UI container shows a random port like `58711` instead of `3001`

**Cause**: Container was created before port override was properly configured

**Solution**: Force recreate the container with the correct port mapping:
```bash
cd /Users/<USER>/projects/agentic-core
docker compose -f docker-compose.yml -f /Users/<USER>/projects/axon-pfc/docker-compose.ports.yml up -d --force-recreate ui
```

**Note**: Simply running `up -d` won't change the ports of an existing container. You must use `--force-recreate` to rebuild it with the new port configuration.

## Additional Services in Agentic-Core

The docker-compose configuration includes other services not managed by axon-pfc:
- `async-engine-cpu`: CPU-bound async tasks
- `dead-letter`: Dead letter queue handler
- `file_service`: File upload/download service (port 8001)
- `engine`: Main workflow engine
- `spectaql`: GraphQL documentation generator

These services use dynamic ports unless explicitly overridden in `docker-compose.ports.yml`.

## Testing After Restart

After running `./start.sh`, verify all services:

1. **Axon Frontend**: http://localhost:8080
2. **Axon Dashboard**: http://localhost:8080/dashboard
3. **Agentic-Core UI**: http://localhost:3001
4. **GraphQL**: http://localhost:5002/graphql/

All services should be accessible and the UI should show no 404 errors in the browser console.

### Quick Port Verification
```bash
# Check all container ports
docker ps --format "table {{.Names}}\t{{.Ports}}" | grep -E "postgres|rabbitmq|graphql|ui"

# Expected output:
# agentic-core-postgres-1   0.0.0.0:5432->5432/tcp
# agentic-core-rabbitmq-1   0.0.0.0:5672->5672/tcp, 0.0.0.0:15672->15672/tcp
# agentic-core-graphql-1    0.0.0.0:5002->8000/tcp
# agentic-core-ui-1         0.0.0.0:3001->8080/tcp
```

