#!/bin/bash
# Monitor MCP Server Calls During Workflow Execution

echo "🔍 Monitoring MCP Server Calls..."
echo "=================================="
echo ""
echo "This will show:"
echo "  • Tool executions (web_search, doc_conversion, etc.)"
echo "  • MCP client calls"
echo "  • Agent interactions"
echo ""
echo "Press Ctrl+C to stop monitoring"
echo ""
sleep 2

cd ../agentic-core

# Monitor async-engine-async logs for tool calls and MCP interactions
docker compose logs -f --tail=100 async-engine-async 2>&1 | grep --line-buffered -E "(tool_call|web_search|doc_conversion|save_output|read_input|MCP|mcp_client|Tool execution|function_call)" --color=always

