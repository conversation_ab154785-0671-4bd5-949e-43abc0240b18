# PPT Ingestion Tool - Now in Agentic-Core UI! ✅

## 🎉 Success!

The **ppt_ingestion** tool has been registered and should now appear in your **Agentic-Core UI → Tools tab**!

## 📊 Tool Details

- **Tool Name**: `ppt_ingestion`
- **Tool ID**: `98179f53-26bf-42cc-9416-aca200f4636b`
- **Version ID**: `95926e2d-c830-4112-9aaf-1c34bf9a6eaa`
- **Category**: `document_processing`
- **Description**: Process PowerPoint (PPTX) or PDF presentations. Extracts text via OCR using PyMuPDF and enhances with GPT-4 Vision to handle complex content like tables, charts, and graphs.

## 🔍 Where to Find It

1. Open your **Agentic-Core UI**
2. Go to the **Tools tab**
3. Look for `ppt_ingestion` in the list

## 🚀 How to Use It

### In Workflows

Add the tool to any agent in your workflows:

```json
{
  "agents": [
    {
      "name": "document_processor",
      "model": "gpt-4o",
      "persona": "You are a document analyst",
      "skills": "Process presentations and analyze content",
      "tools": ["ppt_ingestion", "save_output"]
    }
  ]
}
```

### Tool Parameters

The tool accepts:
- **file_path** (string, required): Path to the PPTX or PDF file
- **use_vision** (boolean, optional): Use GPT-4 Vision for enhancement (default: true)

### Example Workflow

```json
{
  "name": "Presentation Analyzer",
  "description": "Process and analyze presentations",
  "variables": {
    "input": {
      "presentation_file": {
        "type": "string",
        "description": "Path to presentation"
      }
    },
    "output": {
      "analysis": {
        "type": "string"
      }
    }
  },
  "steps": [
    {
      "order": 1,
      "type": "team",
      "team": {
        "name": "Analysis Team",
        "goal": "Process the presentation and provide insights",
        "agents": [
          {
            "name": "analyzer",
            "model": "gpt-4o",
            "persona": "Expert presentation analyst",
            "skills": "Use ppt_ingestion tool to process ${presentation_file}. Then analyze the extracted content and save insights using save_variable('analysis', '<your analysis>'). Say TERMINATE when done.",
            "tools": ["ppt_ingestion", "save_variable"]
          }
        ],
        "variables": {
          "presentation_file": {
            "type": "string"
          }
        }
      }
    }
  ]
}
```

## 🔧 How It Works

When an agent uses the `ppt_ingestion` tool:

1. **Input**: Agent provides file path to PPTX/PDF
2. **Processing**: 
   - File is sent to the PPT Ingestion API
   - PyMuPDF extracts text via OCR
   - GPT-4o Vision analyzes images for tables/charts
3. **Output**: Agent receives extracted text and slide data
4. **Next Steps**: Agent can analyze, summarize, or transform the content

## 📡 Backend API

The tool connects to: `http://localhost:8000/ppt-ingestion/process`

Make sure your backend is running:
```bash
cd /Users/<USER>/axon-pfc
./start.sh
```

## 🧪 Testing the Tool

### Test via Agentic-Core UI

1. Create a new workflow in the UI
2. Add an agent with `ppt_ingestion` tool
3. Run the workflow with a test presentation
4. Check the output

### Test the API Directly

```bash
curl -X POST "http://localhost:8000/ppt-ingestion/process" \
  -F "file=@/path/to/presentation.pptx" \
  -F "use_vision=true"
```

## 📝 What Gets Extracted

For each slide/page:
- **OCR Text**: Basic text extracted from the slide
- **Enhanced Text**: Improved text with:
  - Corrected OCR errors
  - Table contents described
  - Chart/graph data explained
  - Visual layout information

## 💡 Use Cases

1. **Presentation Summarization**
   - Extract key points from decks
   - Generate executive summaries
   
2. **Financial Report Analysis**
   - Extract data from quarterly reports
   - Analyze trends in financial presentations

3. **Content Transformation**
   - Convert presentations to documents
   - Extract and reformat content

4. **Data Extraction**
   - Pull table data from slides
   - Extract metrics from charts

## 🔄 Re-registering

If you need to update or re-register the tool:

```bash
# First, delete the old tool (if needed)
psql -U postgres -d invisible -c "DELETE FROM agentic_objects.tool WHERE name = 'ppt_ingestion'"

# Then re-register
python scripts/register_ppt_tool.py
```

## 📚 Related Files

- **Registration Script**: `scripts/register_ppt_tool.py`
- **Service Implementation**: `src/backend/services/ppt_ingestion.py`
- **API Routes**: `src/backend/routes/ppt_ingestion.py`
- **CLI Tool**: `scripts/process_presentation.py`
- **Documentation**: `docs/ppt-ingestion.md`

## ✅ Verification

Verify the tool is registered:

```bash
python -c "
from sqlalchemy import create_engine, text
engine = create_engine('postgresql://postgres:postgres@localhost:5432/invisible')
with engine.connect() as conn:
    result = conn.execute(text(\"SELECT name FROM agentic_objects.tool WHERE name = 'ppt_ingestion'\"))
    print('Tool registered:', result.fetchone() is not None)
"
```

## 🎯 Next Steps

1. **Check the UI**: Open Agentic-Core and verify `ppt_ingestion` appears in Tools tab
2. **Create a workflow**: Use the example above or create your own
3. **Test it**: Process a sample presentation
4. **Build pipelines**: Combine with other tools for powerful workflows

## 🐛 Troubleshooting

**Tool not appearing in UI?**
- Refresh the browser
- Check the database connection
- Verify registration: See "Verification" section above

**API not responding?**
- Make sure backend is running: `./start.sh`
- Check `backend.log` for errors
- Verify port 8000 is accessible

**Processing fails?**
- Check OpenAI API key is set
- Verify file path is absolute
- Ensure file is PPTX or PDF format

## 📞 Support

- **Main README**: [README.md](README.md)
- **Setup Guide**: [SETUP.md](SETUP.md)
- **API Docs**: http://localhost:8000/docs

---

**Your PPT ingestion tool is live and ready to use in the Agentic-Core UI! 🎊**

