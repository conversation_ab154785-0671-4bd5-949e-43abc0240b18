# Database connection
# Fixed port 5432 (set in docker-compose.ports.yml)
# Note: This is auto-generated by start.sh
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/invisible

# RabbitMQ connection
# Fixed ports 5672 (AMQP) and 15672 (Management UI) set in docker-compose.ports.yml
# Note: This is auto-generated by start.sh
RABBITMQ_URL=amqp://guest:guest@localhost:5672/

# Path to agentic-core (relative or absolute)
AGENTIC_CORE_PATH=../agentic-core

# Enable auto-reload for development
ENABLE_RELOAD=true

# OpenAI API Key (optional, for testing workflows that use OpenAI)
# OPENAI_API_KEY=your_openai_api_key_here
