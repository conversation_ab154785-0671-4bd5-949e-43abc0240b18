# Workflow JSON Generator - Rewrite Summary

## Overview

The `workflow_json_generator.py` service has been updated to generate Axon workflows that follow the new team-based authoring guidelines. 

**IMPORTANT DISTINCTION:**
- **OpenAI Agents SDK structure** (how the generator is implemented): Sequential 4-agent handoff pattern
- **Axon workflow structure** (what the generator outputs): Teams with coordinator + specialist pattern

## Key Changes

### 1. OpenAI Agents SDK Structure (Unchanged)

The generator itself uses the same pattern as `workflow_spec_generator.py`:
- Sequential handoff between 4 individual agents
- Each agent produces structured Pydantic output
- Runner.run() executes each agent in sequence

The 4 agents are:
1. **Specification Analyzer** - Analyzes specs and extracts requirements
2. **Workflow Architect** - Designs workflow structure with team-based steps
3. **Team Designer** - Designs teams with coordinator+specialist pattern
4. **JSON Compiler** - Compiles final JSON following Axon guidelines

### 2. Agent Instructions Updated for Axon Output

All agent instructions have been updated to understand and generate workflows following <PERSON><PERSON>'s new guidelines:

#### Specification Analyzer
- Analyzes spec documents as before
- Identifies inputs, outputs, requirements

#### Workflow Architect
- Now explicitly told that ALL steps must be "team" type
- Designs 2-5 logical team steps
- Each step will have a coordinator + specialists

#### Team Designer
- **CRITICAL UPDATE**: Designs teams following Axon coordinator/specialist pattern
- Each team has coordinator as first agent
- Coordinator uses `{roles}`, `{history}`, `{participants}` for routing
- Specialists have procedural skills: "When coordinator asks: 1) X, 2) Y, 3) Z"
- Clear termination patterns (TERMINATE)

#### JSON Compiler  
- Compiles everything into valid Axon workflow JSON
- Ensures coordinator is first agent in every team
- Validates all Axon requirements are met

### 3. Generated Axon Workflows Follow New Guidelines

The key change is in what the generator **outputs** - Axon workflows that:

**Team Structure:**
- All steps are `type: "team"` (no delegates)
- First agent in each team is a coordinator/leader
- Remaining agents are specialists with focused skills

**Leader Agents:**
- Named with `_coordinator` suffix
- Use `gpt-5-mini` model (routing is simple)
- Include `{roles}`, `{history}`, `{participants}` in skills
- Coordinate work, don't create content
- Route tasks to specialists

**Specialist Agents:**
- Use `gpt-5` for complex tasks (analysis, writing, reasoning)
- Use `gpt-5-mini` for simple tasks (extraction, validation)
- Have clear, focused personas
- Skills written as procedural steps
- Include `save_variable` instructions when needed
- Clear handoff phrases to aid coordination

**Termination:**
- Use unambiguous patterns like "TERMINATE"
- Clear instructions on when to emit termination signals
- Typically final specialist or leader emits the signal

**Variables:**
- Explicit typing (string, integer, float, boolean)
- Clear descriptions
- Proper input/output separation
- Global inputs available to all teams

### 4. Documentation Updates

The module docstring now clearly explains:
- The coordinator/specialist pattern used by the generator
- The structure of generated workflows
- Key features of the new approach

### 5. Code Comments Enhanced

All code comments and progress messages updated to reflect:
- "Coordinator delegating to..." pattern
- Team-based coordination language
- New guidelines terminology

## Generated Workflow Structure

Workflows generated by this service now follow this pattern:

```json
{
  "description": "Clear workflow description",
  "variables": {
    "input": {
      "input_name": {
        "type": "string",
        "description": "What this input is for"
      }
    },
    "output": {
      "output_name": {
        "type": "string",
        "description": "What this output contains"
      }
    }
  },
  "steps": [
    {
      "order": 1,
      "type": "team",
      "team": {
        "name": "Team Name",
        "goal": "What the team should accomplish (may reference {{variables}})",
        "results": "What the team produces",
        "termination": {
          "regex": ["TERMINATE"]
        },
        "agents": [
          {
            "name": "coordinator_name",
            "model": "gpt-4o",
            "persona": "Coordinator who delegates. Never creates content.",
            "skills": "1) Read variables\n2) Ask @specialist1 to do X\n3) Ask @specialist2 to do Y\n4) When done, say TERMINATE\n\n{roles}\n{history}\n{participants}"
          },
          {
            "name": "specialist_name",
            "model": "gpt-4o",
            "persona": "Expert in specific task",
            "skills": "When coordinator asks:\n1) Do specific task\n2) Use save_variable if needed\n3) Hand back to coordinator"
          }
        ],
        "variables": {}
      }
    }
  ]
}
```

## Benefits of the New Approach

1. **Clear Role Separation**: Leaders coordinate, specialists execute
2. **Better Collaboration**: Explicit handoff patterns and communication
3. **Proper Variable Management**: save_variable for outputs, clear input/output contracts
4. **Reliable Termination**: Unambiguous completion signals
5. **Scalable Patterns**: Easy to add more specialists to teams
6. **Outcome-Oriented**: Focus on what needs to be accomplished, not implementation details

## Backward Compatibility

The JSON structure remains compatible with the existing workflow execution system. The changes are in:
- How teams are structured internally (coordinator + specialists)
- How agent instructions are written (more explicit coordination)
- The patterns used for termination and variable handling

## Testing Recommendations

When testing the updated generator:

1. **Generate a simple workflow** - Verify coordinator/specialist pattern
2. **Generate a complex workflow** - Check that all teams follow the pattern
3. **Check termination patterns** - Ensure TERMINATE is used consistently
4. **Verify variable handling** - Check save_variable usage in specialist skills
5. **Validate leader skills** - Ensure {roles}, {history}, {participants} are included

## Files Modified

- `/Users/<USER>/projects/axon-pfc/src/backend/services/workflow_json_generator.py`
  - Updated module docstring
  - Rewrote `_init_agents()` method with new agent instructions
  - Updated `generate_workflow_json_async()` with new coordination language
  - Enhanced code comments throughout

## Next Steps

The workflow spec generator (`workflow_spec_generator.py`) was NOT modified per user request. If needed in the future, it could be updated to follow the same pattern.

---

Generated: October 30, 2025

