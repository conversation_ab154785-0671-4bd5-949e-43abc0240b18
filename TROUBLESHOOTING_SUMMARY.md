# Troubleshooting Summary - "Failed to get status" Error

## Current Status: ✅ All Systems Operational

I've verified that your Workflow JSON Generator is **fully set up and working**:

✅ Python 3.13.3  
✅ All dependencies installed (python-docx, openai-agents, openai)  
✅ Service imports correctly  
✅ Routes load successfully (5 endpoints)  
✅ OpenAI API key configured  
✅ Backend server running on port 8000  
✅ Main health endpoint: healthy  
✅ Workflow generation endpoint: healthy  

## What I Fixed

### 1. Better Error Handling in Backend
- Added try/catch blocks around all endpoints
- Improved error messages to show actual errors
- Added graceful import fallbacks
- Added detailed logging

**File**: `src/backend/routes/workflow_json_generation.py`

### 2. Better Error Reporting in Frontend
- Captures detailed API error messages
- Logs to browser console for debugging
- Shows specific error details to user instead of generic message

**File**: `src/frontend/app.js` (lines 1770-1796)

### 3. Created Diagnostic Tools

**Setup Check Script**: `scripts/check_setup.sh`
```bash
./scripts/check_setup.sh
```
Checks all components and shows their status.

**Full Test Script**: `scripts/test_workflow_generator.py`
```bash
python scripts/test_workflow_generator.py
```
Tests the complete workflow generation cycle end-to-end.

## Understanding "Failed to get status"

This error occurs when the frontend tries to poll the status endpoint but gets an error response. Common causes:

### Scenario 1: Backend Restarted Mid-Generation
**Symptom**: Upload succeeds, but status polling fails  
**Cause**: Backend restarted, task ID lost from memory  
**Solution**: Tasks are stored in memory, restart clears them  
**Fix**: Use the frontend to generate again

### Scenario 2: Task Doesn't Exist
**Symptom**: Immediate "failed to get status"  
**Cause**: Upload failed but didn't show error  
**Solution**: Check browser Network tab for the actual upload error

### Scenario 3: CORS/Network Issue
**Symptom**: CORS error in browser console  
**Cause**: Accessing frontend from wrong origin  
**Solution**: Access via `http://localhost:8000/src/frontend/index.html`

### Scenario 4: Agent Execution Failed
**Symptom**: Status works initially, then fails  
**Cause**: Agent threw exception during generation  
**Solution**: Check backend logs for the error

## How to Debug When You See This Error

### Step 1: Check Browser DevTools

1. Open **DevTools** (F12)
2. Go to **Console** tab
3. Look for the actual error message (now logged with `console.error`)
4. Go to **Network** tab
5. Find the failed request
6. Click on it and check:
   - **Response** tab: Shows the actual API error
   - **Headers** tab: Confirms the URL is correct

### Step 2: Check Backend Logs

Look at your terminal where the backend is running. You should see:
```
[WorkflowJSONGenerator] Starting workflow design for task abc-123...
```

If you see errors, they'll show up here with full tracebacks.

### Step 3: Test Directly

```bash
# Create a test task
curl -X POST http://localhost:8000/workflow-generation/generate \
  -F "file=@/tmp/test.docx" \
  -F "workflow_name=Test" \
  | jq

# Copy the task_id from response, then:
curl http://localhost:8000/workflow-generation/status/TASK_ID_HERE | jq
```

This bypasses the frontend and shows the raw API response.

## Testing Your Setup

### Quick Test (2 minutes)

```bash
cd /Users/<USER>/projects/axon-pfc
source venv/bin/activate

# Create test document
python3 << 'EOF'
from docx import Document
doc = Document()
doc.add_heading('Test Workflow', 0)
doc.add_paragraph('Objective: Process data and generate reports')
doc.add_paragraph('Inputs: data_file (CSV or Excel)')
doc.add_paragraph('Steps: 1. Load data 2. Analyze 3. Report')
doc.add_paragraph('Outputs: report_text (summary)')
doc.save('/tmp/quick_test.docx')
print('✅ Created test doc')
EOF

# Test via API
TASK_ID=$(curl -s -X POST http://localhost:8000/workflow-generation/generate \
  -F "file=@/tmp/quick_test.docx" \
  -F "workflow_name=Quick Test" | jq -r '.task_id')

echo "Task ID: $TASK_ID"
echo "Polling status..."

# Poll for 60 seconds
for i in {1..30}; do
  STATUS=$(curl -s http://localhost:8000/workflow-generation/status/$TASK_ID | jq -r '.status')
  MSG=$(curl -s http://localhost:8000/workflow-generation/status/$TASK_ID | jq -r '.message')
  echo "[$i/30] $STATUS - $MSG"
  
  if [ "$STATUS" = "completed" ]; then
    echo "✅ Success!"
    curl -s http://localhost:8000/workflow-generation/status/$TASK_ID | jq '.workflow_json.steps | length'
    echo "steps generated"
    break
  elif [ "$STATUS" = "failed" ]; then
    echo "❌ Failed"
    curl -s http://localhost:8000/workflow-generation/status/$TASK_ID | jq '.error'
    break
  fi
  
  sleep 2
done
```

### Full Test (includes validation)

```bash
cd /Users/<USER>/projects/axon-pfc
source venv/bin/activate
python scripts/test_workflow_generator.py
```

This runs a complete test cycle and validates the output.

## Using the Frontend

Now that everything is verified working:

1. **Open browser**: http://localhost:8000/src/frontend/index.html
2. **Click**: "🚀 Generate from Doc" button
3. **Upload**: A .docx file with your workflow specification
4. **Enter**: Workflow name
5. **Click**: "✨ Generate Workflow"
6. **Wait**: 30-60 seconds (status updates every 2 seconds)
7. **Review**: Generated JSON
8. **Click**: "💾 Create Workflow in Database" to save

**If you see "failed to get status"**:
- Open DevTools Console (F12) immediately
- Look for the specific error message (now logged with details)
- Check the Network tab for the actual HTTP error
- Share the specific error message for more targeted help

## What to Check If Still Having Issues

### 1. Browser Console Shows Specific Error
The error message will now be much more detailed. It might say:
- "Task not found" → Upload might have failed
- "Error getting status: [specific error]" → Backend issue
- CORS error → Wrong origin

### 2. Network Tab Shows Red Request
- Click on the failed request
- Check the Response tab for detailed error
- Check the Status code (404 = not found, 500 = server error)

### 3. Backend Shows Error in Logs
- Look for Python tracebacks
- OpenAI API errors
- JSON parsing errors

## Documentation

- **Usage Guide**: `docs/workflow-json-generator.md`
- **Troubleshooting**: `docs/workflow-json-generator-troubleshooting.md`
- **Setup**: `WORKFLOW_JSON_GENERATOR_SETUP.md`
- **This File**: `TROUBLESHOOTING_SUMMARY.md`

## Summary

Your system is fully operational! The "failed to get status" error can happen for various reasons, but with the improved error handling I've added:

1. ✅ You'll see **specific error messages** in the browser console
2. ✅ Backend logs will show **detailed tracebacks**
3. ✅ You have **diagnostic scripts** to test each component
4. ✅ API returns **helpful error messages** instead of generic failures

**Try generating a workflow now** and if you see the error again, check:
1. Browser DevTools Console for the specific error
2. Backend logs for any Python errors
3. Network tab for the actual API response

The error message will now tell you exactly what went wrong!

