# Axon-PFC Backend

FastAPI REST API for managing agentic-core workflows.

## Components

### main.py

FastAPI application with endpoints:
- `GET /api/workflows` - List all workflows
- `GET /api/workflows/{id}` - Get workflow by ID
- `POST /api/workflows` - Create new workflow
- `PUT /api/workflows/{id}` - Update workflow
- `DELETE /api/workflows/{id}` - Delete (archive) workflow
- `GET /health` - Health check

### workflow_json_serializer.py

JSON serialization for workflows:
- `flow_to_json()` - Convert database models to JSON
- `json_to_flow()` - Create/update database models from JSON
- Teams are fully inlined, Delegates/Sub-workflows referenced by ID

### example_team.json

Example workflow with a team of agents. This is the workflow definition only.

To create via API, wrap it with metadata:

```json
{
  "name": "Greeting Team Workflow",
  "workflow": { ... contents of example_team.json ... }
}
```

## Running Locally

From project root:
```bash
./start.sh
```

Or manually:
```bash
source venv/bin/activate
cd src/backend
python main.py
```

API documentation: http://localhost:8000/docs

## Database

Connects to PostgreSQL via `DATABASE_URL` environment variable. Tables are created via Alembic migrations from agentic-core.

## Dependencies

See `requirements.txt` in project root:
- fastapi / uvicorn
- sqlalchemy / psycopg2-binary
- alembic (for migrations)
- python-dotenv
