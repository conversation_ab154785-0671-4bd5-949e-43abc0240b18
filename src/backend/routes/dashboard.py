"""
Dashboard API routes.

Provides real-time system status for monitoring:
- Active eval runs
- Recent completions
- Queue statistics
- System health
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import text
from pydantic import BaseModel

# Add agentic-core to path
project_root = Path(__file__).parent.parent.parent.parent.resolve()
agentic_core_path_str = os.getenv("AGENTIC_CORE_PATH", "../agentic-core")
agentic_core_path = Path(agentic_core_path_str)
if not agentic_core_path.is_absolute():
    agentic_core_path = (project_root / agentic_core_path).resolve()
backend_path = agentic_core_path / "backend"
sys.path.insert(0, str(backend_path / "lib"))
sys.path.insert(0, str(backend_path))

# Import services
sys.path.insert(0, str(project_root / "src" / "backend"))
from services.rabbitmq_monitor import get_rabbitmq_queue_stats, check_rabbitmq_health


router = APIRouter(prefix="/api/dashboard", tags=["dashboard"])


# Pydantic models
class QueueStats(BaseModel):
    """Queue statistics."""
    pending: int
    consumers: int
    error: Optional[str] = None


class ActiveRun(BaseModel):
    """Active eval run info."""
    eval_run_id: str
    test_case_name: str
    workflow_name: str
    status: str
    started_at: datetime
    elapsed_seconds: float
    batch_run_id: Optional[str] = None
    batch_name: Optional[str] = None
    flow_execution_id: str


class RecentCompletion(BaseModel):
    """Recent completion info."""
    eval_run_id: str
    test_case_name: str
    workflow_name: str
    execution_status: str
    started_at: datetime
    completed_at: Optional[datetime]
    duration_ms: Optional[float]
    passed: Optional[bool]


class SystemHealth(BaseModel):
    """System health status."""
    database: str
    rabbitmq: str
    rabbitmq_connected: bool
    timestamp: datetime


class DashboardStatus(BaseModel):
    """Complete dashboard status."""
    timestamp: datetime
    active_runs: List[ActiveRun]
    recent_completions: List[RecentCompletion]
    queues: Dict[str, QueueStats]
    system_health: SystemHealth


# Database dependency
def get_db():
    """Get database session dependency."""
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker
    
    DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/invisible")
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(bind=engine)
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@router.get("/status", response_model=DashboardStatus)
async def get_dashboard_status(db: Session = Depends(get_db)):
    """
    Get complete dashboard status.
    
    Returns:
        - Active eval runs (currently executing)
        - Recent completions (last 20)
        - Queue statistics from RabbitMQ
        - System health status
    """
    timestamp = datetime.utcnow()
    
    # Get active runs from view
    active_query = text("""
        SELECT 
            er.id as eval_run_id,
            ec.name as test_case_name,
            f.name as workflow_name,
            fe.result as status,
            fe.start_time as started_at,
            EXTRACT(EPOCH FROM (NOW() - fe.start_time)) as elapsed_seconds,
            er.batch_run_id,
            eb.name as batch_name,
            er.flow_execution_id
        FROM evaluation.eval_run er
        JOIN evaluation.eval_case ec ON er.eval_case_id = ec.id
        JOIN flow_execution.flow_execution fe ON er.flow_execution_id = fe.id
        JOIN agentic_objects.flow f ON ec.workflow_id = f.id
        LEFT JOIN evaluation.eval_batch_run eb ON er.batch_run_id = eb.id
        WHERE fe.result IN ('PENDING', 'RUNNING', 'PAUSED')
        AND fe.end_time IS NULL
        ORDER BY fe.start_time DESC
        LIMIT 50
    """)
    
    active_results = db.execute(active_query).fetchall()
    active_runs = [
        {
            "eval_run_id": str(row[0]),
            "test_case_name": row[1],
            "workflow_name": row[2],
            "status": row[3],
            "started_at": row[4],
            "elapsed_seconds": float(row[5]) if row[5] else 0,
            "batch_run_id": str(row[6]) if row[6] else None,
            "batch_name": row[7],
            "flow_execution_id": str(row[8])
        }
        for row in active_results
    ]
    
    # Get recent completions
    recent_query = text("""
        SELECT 
            eval_run_id, eval_case_name, workflow_name, execution_status,
            started_at, completed_at, duration_ms, passed
        FROM evaluation.eval_run_detail
        WHERE execution_status IN ('COMPLETE', 'FAILED', 'CANCELLED')
        ORDER BY completed_at DESC NULLS LAST
        LIMIT 20
    """)
    
    recent_results = db.execute(recent_query).fetchall()
    recent_completions = [
        {
            "eval_run_id": str(row[0]),
            "test_case_name": row[1],
            "workflow_name": row[2],
            "execution_status": row[3],
            "started_at": row[4],
            "completed_at": row[5],
            "duration_ms": float(row[6]) if row[6] else None,
            "passed": row[7]
        }
        for row in recent_results
    ]
    
    # Get RabbitMQ stats
    try:
        queue_stats = await get_rabbitmq_queue_stats()
    except Exception as e:
        queue_stats = {
            "error": str(e),
            "agentic.flyweight": {"pending": 0, "consumers": 0, "error": "Failed to fetch"},
            "agentic.async": {"pending": 0, "consumers": 0, "error": "Failed to fetch"}
        }
    
    # Check system health
    database_health = "healthy"
    try:
        db.execute(text("SELECT 1"))
    except:
        database_health = "unhealthy"
    
    rabbitmq_health_status = await check_rabbitmq_health()
    rabbitmq_health = "healthy" if rabbitmq_health_status.get("healthy") else "unhealthy"
    rabbitmq_connected = rabbitmq_health_status.get("connected", False)
    
    system_health = {
        "database": database_health,
        "rabbitmq": rabbitmq_health,
        "rabbitmq_connected": rabbitmq_connected,
        "timestamp": timestamp
    }
    
    return {
        "timestamp": timestamp,
        "active_runs": active_runs,
        "recent_completions": recent_completions,
        "queues": queue_stats,
        "system_health": system_health
    }


@router.get("/active-runs", response_model=List[ActiveRun])
async def get_active_runs(db: Session = Depends(get_db)):
    """Get only active runs."""
    query = text("""
        SELECT 
            eval_run_id, test_case_name, workflow_name, status,
            started_at, elapsed_seconds, batch_run_id, batch_name
        FROM evaluation.active_eval_runs
        ORDER BY started_at DESC
        LIMIT 100
    """)
    
    results = db.execute(query).fetchall()
    return [
        {
            "eval_run_id": str(row[0]),
            "test_case_name": row[1],
            "workflow_name": row[2],
            "status": row[3],
            "started_at": row[4],
            "elapsed_seconds": float(row[5]) if row[5] else 0,
            "batch_run_id": str(row[6]) if row[6] else None,
            "batch_name": row[7]
        }
        for row in results
    ]


@router.get("/recent-completions", response_model=List[RecentCompletion])
async def get_recent_completions(limit: int = 20, db: Session = Depends(get_db)):
    """Get recent completions."""
    query = text("""
        SELECT 
            eval_run_id, eval_case_name, workflow_name, execution_status,
            started_at, completed_at, duration_ms, passed
        FROM evaluation.eval_run_detail
        WHERE execution_status IN ('COMPLETE', 'FAILED', 'CANCELLED')
        ORDER BY completed_at DESC NULLS LAST
        LIMIT :limit
    """)
    
    results = db.execute(query, {"limit": limit}).fetchall()
    return [
        {
            "eval_run_id": str(row[0]),
            "test_case_name": row[1],
            "workflow_name": row[2],
            "execution_status": row[3],
            "started_at": row[4],
            "completed_at": row[5],
            "duration_ms": float(row[6]) if row[6] else None,
            "passed": row[7]
        }
        for row in results
    ]


@router.get("/health", response_model=SystemHealth)
async def get_system_health(db: Session = Depends(get_db)):
    """Get system health status."""
    timestamp = datetime.utcnow()
    
    # Check database
    database_health = "healthy"
    try:
        db.execute(text("SELECT 1"))
    except:
        database_health = "unhealthy"
    
    # Check RabbitMQ
    rabbitmq_health_status = await check_rabbitmq_health()
    rabbitmq_health = "healthy" if rabbitmq_health_status.get("healthy") else "unhealthy"
    rabbitmq_connected = rabbitmq_health_status.get("connected", False)
    
    return {
        "database": database_health,
        "rabbitmq": rabbitmq_health,
        "rabbitmq_connected": rabbitmq_connected,
        "timestamp": timestamp
    }

