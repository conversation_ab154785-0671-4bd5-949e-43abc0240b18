"""
Workflow JSON Generation API Routes

FastAPI routes for generating workflow JSONs from specification documents.
"""

import os
import sys
import tempfile
from typing import Optional
from pathlib import Path
from uuid import uuid4

from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from pydantic import BaseModel

# Add current directory to path for imports
current_dir = Path(__file__).parent.parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

# Import from services module
try:
    from services.workflow_json_generator import get_workflow_json_generator
except ImportError as e:
    print(f"Warning: Could not import workflow_json_generator: {e}")
    # Create a dummy function for development
    def get_workflow_json_generator():
        raise HTTPException(status_code=503, detail="Workflow JSON Generator service not available")


router = APIRouter(prefix="/workflow-generation", tags=["workflow-generation"])


class GenerationResponse(BaseModel):
    """Response model for workflow generation request."""
    task_id: str
    status: str
    message: str


class TaskStatusResponse(BaseModel):
    """Response model for task status."""
    task_id: str
    status: str
    message: str
    workflow_name: Optional[str] = None
    workflow_json: Optional[dict] = None
    created_at: Optional[str] = None
    completed_at: Optional[str] = None
    error: Optional[str] = None


@router.post("/generate", response_model=GenerationResponse)
async def generate_workflow_from_spec(
    file: UploadFile = File(...),
    workflow_name: Optional[str] = Form(None)
):
    """
    Generate a workflow JSON from a specification document.
    
    Args:
        file: The uploaded specification file (.docx)
        workflow_name: Optional name for the workflow
    
    Returns:
        GenerationResponse with task_id for tracking progress
    """
    # Validate file type
    if not file.filename:
        raise HTTPException(status_code=400, detail="No filename provided")
    
    file_extension = Path(file.filename).suffix.lower()
    if file_extension != ".docx":
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type: {file_extension}. Only .docx files are supported."
        )
    
    # Save uploaded file to temporary location
    with tempfile.NamedTemporaryFile(
        delete=False,
        suffix=file_extension
    ) as tmp_file:
        tmp_path = tmp_file.name
        contents = await file.read()
        tmp_file.write(contents)
    
    try:
        # Initialize service
        generator = get_workflow_json_generator()
        
        # Use provided workflow name or derive from filename
        if not workflow_name:
            workflow_name = Path(file.filename).stem.replace("_", " ").replace("-", " ").title()
        
        # Start generation (async background task)
        task_id = generator.generate_workflow_json(
            spec_file_path=tmp_path,
            workflow_name=workflow_name
        )
        
        return GenerationResponse(
            task_id=task_id,
            status="queued",
            message="Workflow generation started. Check status with task_id."
        )
    
    except Exception as e:
        # Clean up temporary file on error
        if os.path.exists(tmp_path):
            os.unlink(tmp_path)
        raise HTTPException(status_code=500, detail=f"Generation failed: {str(e)}")


@router.get("/status/{task_id}", response_model=TaskStatusResponse)
async def get_generation_status(task_id: str):
    """
    Get the status of a workflow generation task.
    
    Args:
        task_id: The task ID returned from /generate
    
    Returns:
        TaskStatusResponse with current status and result (if completed)
    """
    try:
        generator = get_workflow_json_generator()
        task = generator.get_task_status(task_id)
        
        if not task:
            raise HTTPException(status_code=404, detail=f"Task not found: {task_id}")
        
        return TaskStatusResponse(
            task_id=task["id"],
            status=task["status"],
            message=task["message"],
            workflow_name=task.get("workflow_name"),
            workflow_json=task.get("workflow_json"),
            created_at=task.get("created_at"),
            completed_at=task.get("completed_at"),
            error=task.get("error")
        )
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error getting task status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting status: {str(e)}")


@router.get("/tasks")
async def list_generation_tasks():
    """
    List all workflow generation tasks.
    
    Returns:
        List of all tasks with their current status
    """
    generator = get_workflow_json_generator()
    tasks = generator.list_tasks()
    return {"tasks": tasks}


@router.post("/create-workflow")
async def create_workflow_from_json(
    workflow_name: str = Form(...),
    workflow_json: str = Form(...),
    namespace: str = Form("default")
):
    """
    Create a workflow in the database from generated JSON.
    
    Args:
        workflow_name: Name for the workflow
        workflow_json: The workflow JSON string
        namespace: Namespace for the workflow (default: "default")
    
    Returns:
        Created workflow information
    """
    import json
    from uuid import UUID
    
    # Import database components
    try:
        from database.agentic_objects.models import Flow, FlowVersion
        from database.session import get_db
        from workflow_json_serializer import json_to_flow
        
        # Parse JSON
        try:
            workflow_data = json.loads(workflow_json)
        except json.JSONDecodeError as e:
            raise HTTPException(status_code=400, detail=f"Invalid JSON: {str(e)}")
        
        # Get database session
        db = next(get_db())
        
        try:
            # Use system user ID (should exist from setup)
            # In production, use authenticated user
            system_user_id = UUID("00000000-0000-0000-0000-000000000001")
            
            # Create workflow
            flow, flow_version = json_to_flow(
                db=db,
                json_data=workflow_data,
                user_id=system_user_id,
                name=workflow_name,
                namespace_name=namespace,
                is_draft=False
            )
            
            db.commit()
            
            return {
                "success": True,
                "workflow_id": str(flow.id),
                "workflow_version_id": str(flow_version.id),
                "workflow_name": flow.name,
                "message": "Workflow created successfully"
            }
        
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create workflow: {str(e)}")
        finally:
            db.close()
    
    except ImportError as e:
        raise HTTPException(
            status_code=500,
            detail="Database not available. This endpoint requires agentic-core database connection."
        )


@router.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "workflow-json-generation"}

