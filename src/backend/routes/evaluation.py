"""
Evaluation API routes.

Provides REST API endpoints for:
- Creating, reading, updating, deleting eval cases
- Running eval cases (single or batch)
- Viewing eval run results and statistics
"""

import os
import sys
from pathlib import Path
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

# Add agentic-core to path
project_root = Path(__file__).parent.parent.parent.parent.resolve()
agentic_core_path_str = os.getenv("AGENTIC_CORE_PATH", "../agentic-core")
agentic_core_path = Path(agentic_core_path_str)
if not agentic_core_path.is_absolute():
    agentic_core_path = (project_root / agentic_core_path).resolve()
backend_path = agentic_core_path / "backend"
sys.path.insert(0, str(backend_path / "lib"))
sys.path.insert(0, str(backend_path))

# Import models
from models.evaluation import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy import text

router = APIRouter(prefix="/api", tags=["evaluation"])


# Pydantic models for API
class EvalCaseCreate(BaseModel):
    """Request model for creating an eval case."""
    name: str = Field(..., description="Test case name")
    description: Optional[str] = Field(None, description="Test case description")
    input_variables: List[Dict[str, Any]] = Field(..., description="Input variables (same format as FlowExecution.variables)")
    expected_output: Optional[Dict[str, Any]] = Field(None, description="Expected outputs for validation")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Custom validation rules")
    tags: Optional[List[str]] = Field(None, description="Tags for categorization")


class EvalCaseUpdate(BaseModel):
    """Request model for updating an eval case."""
    name: Optional[str] = None
    description: Optional[str] = None
    input_variables: Optional[List[Dict[str, Any]]] = None
    expected_output: Optional[Dict[str, Any]] = None
    validation_rules: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    is_active: Optional[bool] = None


class EvalCaseResponse(BaseModel):
    """Response model for eval case."""
    id: str
    workflow_id: str
    name: str
    description: Optional[str]
    input_variables: List[Dict[str, Any]]
    expected_output: Optional[Dict[str, Any]]
    validation_rules: Optional[Dict[str, Any]]
    tags: Optional[List[str]]
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    # Statistics (populated from eval_case_stats view if available)
    total_runs: Optional[int] = None
    passed_validations: Optional[int] = None
    failed_validations: Optional[int] = None
    success_rate: Optional[float] = None
    
    class Config:
        from_attributes = True


class EvalRunResponse(BaseModel):
    """Response model for eval run."""
    id: str
    eval_case_id: str
    eval_case_name: Optional[str] = None
    flow_execution_id: str
    batch_run_id: Optional[str] = None
    
    # From flow_execution (populated via join)
    execution_status: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration_ms: Optional[float] = None
    output_variables: Optional[List[Dict[str, Any]]] = None
    error_message: Optional[str] = None
    
    # Validation results
    passed: Optional[bool] = None
    validation_output: Optional[Dict[str, Any]] = None
    validation_notes: Optional[str] = None
    evaluated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class RunEvalCaseRequest(BaseModel):
    """Request model for running an eval case."""
    eval_case_ids: List[str] = Field(..., description="List of eval case IDs to run")
    batch_name: Optional[str] = Field(None, description="Optional batch name for grouping")


class RunEvalCaseResponse(BaseModel):
    """Response model for run eval case."""
    batch_run_id: Optional[str] = None
    eval_run_ids: List[str] = Field(..., description="List of created eval run IDs")
    message: str


# Database dependency
def get_db():
    """Get database session dependency."""
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker
    
    DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/invisible")
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(bind=engine)
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_system_user_id(db: Session) -> UUID:
    """Get system user ID."""
    from database.users.models import User
    system_user = db.query(User).filter(User.email == "system@localhost").first()
    if not system_user:
        raise HTTPException(status_code=500, detail="System user not found")
    return system_user.id


# ============================================================================
# Eval Case Endpoints
# ============================================================================

@router.get("/workflows/{workflow_id}/eval-cases", response_model=List[EvalCaseResponse])
async def list_eval_cases(workflow_id: str, db: Session = Depends(get_db)):
    """List all eval cases for a workflow."""
    try:
        workflow_version_uuid = UUID(workflow_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid workflow ID format")
    
    # Get flow_id from flow_version_id
    from database.agentic_objects.models import FlowVersion
    flow_version = db.query(FlowVersion).filter(FlowVersion.id == workflow_version_uuid).first()
    if not flow_version:
        raise HTTPException(status_code=404, detail="Workflow not found")
    
    flow_id = flow_version.flow_id
    
    # Query eval cases
    cases = db.query(EvalCase).filter(EvalCase.workflow_id == flow_id).all()
    
    # Get statistics from view
    result = []
    for case in cases:
        case_dict = {
            "id": str(case.id),
            "workflow_id": str(case.workflow_id),
            "name": case.name,
            "description": case.description,
            "input_variables": case.input_variables,
            "expected_output": case.expected_output,
            "validation_rules": case.validation_rules,
            "tags": case.tags or [],
            "is_active": case.is_active,
            "created_at": case.created_at,
            "updated_at": case.updated_at
        }
        
        # Get stats from view
        try:
            stats_query = text("""
                SELECT total_runs, passed_validations, failed_validations, success_rate
                FROM evaluation.eval_case_stats
                WHERE id = :case_id
            """)
            stats = db.execute(stats_query, {"case_id": case.id}).first()
            if stats:
                case_dict["total_runs"] = stats[0]
                case_dict["passed_validations"] = stats[1]
                case_dict["failed_validations"] = stats[2]
                case_dict["success_rate"] = float(stats[3]) if stats[3] else None
        except:
            pass
        
        result.append(case_dict)
    
    return result


@router.post("/workflows/{workflow_id}/eval-cases", response_model=EvalCaseResponse, status_code=status.HTTP_201_CREATED)
async def create_eval_case(workflow_id: str, data: EvalCaseCreate, db: Session = Depends(get_db)):
    """Create a new eval case."""
    try:
        workflow_version_uuid = UUID(workflow_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid workflow ID format")
    
    # Get flow_id from flow_version_id (workflow_id is actually flow_version_id from frontend)
    from database.agentic_objects.models import FlowVersion
    flow_version = db.query(FlowVersion).filter(FlowVersion.id == workflow_version_uuid).first()
    if not flow_version:
        raise HTTPException(status_code=404, detail="Workflow not found")
    
    flow_id = flow_version.flow_id
    
    # Get system user
    user_id = get_system_user_id(db)
    
    # Check if case with same name exists
    existing = db.query(EvalCase).filter(
        EvalCase.workflow_id == flow_id,
        EvalCase.name == data.name
    ).first()
    
    if existing:
        raise HTTPException(status_code=400, detail=f"Eval case with name '{data.name}' already exists for this workflow")
    
    # Create eval case
    eval_case = EvalCase(
        workflow_id=flow_id,
        name=data.name,
        description=data.description,
        input_variables=data.input_variables,
        expected_output=data.expected_output,
        validation_rules=data.validation_rules,
        tags=data.tags or [],
        created_by=user_id
    )
    
    db.add(eval_case)
    db.commit()
    db.refresh(eval_case)
    
    return {
        "id": str(eval_case.id),
        "workflow_id": str(eval_case.workflow_id),
        "name": eval_case.name,
        "description": eval_case.description,
        "input_variables": eval_case.input_variables,
        "expected_output": eval_case.expected_output,
        "validation_rules": eval_case.validation_rules,
        "tags": eval_case.tags or [],
        "is_active": eval_case.is_active,
        "created_at": eval_case.created_at,
        "updated_at": eval_case.updated_at,
        "total_runs": 0,
        "passed_validations": 0,
        "failed_validations": 0,
        "success_rate": None
    }


@router.get("/eval-cases/{case_id}", response_model=EvalCaseResponse)
async def get_eval_case(case_id: str, db: Session = Depends(get_db)):
    """Get a specific eval case."""
    try:
        case_uuid = UUID(case_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid case ID format")
    
    case = db.query(EvalCase).filter(EvalCase.id == case_uuid).first()
    if not case:
        raise HTTPException(status_code=404, detail="Eval case not found")
    
    # Build response
    case_dict = {
        "id": str(case.id),
        "workflow_id": str(case.workflow_id),
        "name": case.name,
        "description": case.description,
        "input_variables": case.input_variables,
        "expected_output": case.expected_output,
        "validation_rules": case.validation_rules,
        "tags": case.tags or [],
        "is_active": case.is_active,
        "created_at": case.created_at,
        "updated_at": case.updated_at
    }
    
    # Get stats
    try:
        stats_query = text("""
            SELECT total_runs, passed_validations, failed_validations, success_rate
            FROM evaluation.eval_case_stats
            WHERE id = :case_id
        """)
        stats = db.execute(stats_query, {"case_id": case.id}).first()
        if stats:
            case_dict["total_runs"] = stats[0]
            case_dict["passed_validations"] = stats[1]
            case_dict["failed_validations"] = stats[2]
            case_dict["success_rate"] = float(stats[3]) if stats[3] else None
    except:
        pass
    
    return case_dict


@router.put("/eval-cases/{case_id}", response_model=EvalCaseResponse)
async def update_eval_case(case_id: str, data: EvalCaseUpdate, db: Session = Depends(get_db)):
    """Update an eval case."""
    try:
        case_uuid = UUID(case_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid case ID format")
    
    case = db.query(EvalCase).filter(EvalCase.id == case_uuid).first()
    if not case:
        raise HTTPException(status_code=404, detail="Eval case not found")
    
    # Update fields
    if data.name is not None:
        case.name = data.name
    if data.description is not None:
        case.description = data.description
    if data.input_variables is not None:
        case.input_variables = data.input_variables
    if data.expected_output is not None:
        case.expected_output = data.expected_output
    if data.validation_rules is not None:
        case.validation_rules = data.validation_rules
    if data.tags is not None:
        case.tags = data.tags
    if data.is_active is not None:
        case.is_active = data.is_active
    
    db.commit()
    db.refresh(case)
    
    return await get_eval_case(case_id, db)


@router.delete("/eval-cases/{case_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_eval_case(case_id: str, db: Session = Depends(get_db)):
    """Delete an eval case."""
    try:
        case_uuid = UUID(case_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid case ID format")
    
    case = db.query(EvalCase).filter(EvalCase.id == case_uuid).first()
    if not case:
        raise HTTPException(status_code=404, detail="Eval case not found")
    
    db.delete(case)
    db.commit()
    
    return None


# ============================================================================
# Eval Run Endpoints
# ============================================================================

@router.post("/eval-cases/run", response_model=RunEvalCaseResponse)
async def run_eval_cases(data: RunEvalCaseRequest, db: Session = Depends(get_db)):
    """
    Run one or more eval cases.
    
    This creates flow executions and publishes them to RabbitMQ for processing.
    """
    # Import runner
    from services.eval_runner import EvalRunner
    
    try:
        case_uuids = [UUID(case_id) for case_id in data.eval_case_ids]
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid case ID format")
    
    # Verify cases exist
    cases = db.query(EvalCase).filter(EvalCase.id.in_(case_uuids)).all()
    if len(cases) != len(case_uuids):
        raise HTTPException(status_code=404, detail="One or more eval cases not found")

    # Create runner with debugging disabled (no pausing)
    # Logs are captured automatically via GraphQL Log Writer service
    runner = EvalRunner(db, enable_debugging=False)
    
    try:
        # Run cases
        eval_run_ids = await runner.run_eval_cases(case_uuids, batch_name=data.batch_name)
        
        return {
            "batch_run_id": None,  # TODO: Return batch_run_id if batch_name was provided
            "eval_run_ids": [str(run_id) for run_id in eval_run_ids],
            "message": f"Successfully queued {len(eval_run_ids)} eval case(s) for execution"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error running eval cases: {str(e)}")


@router.get("/eval-runs/{run_id}", response_model=EvalRunResponse)
async def get_eval_run(run_id: str, db: Session = Depends(get_db)):
    """Get a specific eval run with execution details."""
    try:
        run_uuid = UUID(run_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid run ID format")
    
    # Query from eval_run_detail view
    query = text("""
        SELECT 
            eval_run_id, eval_case_id, eval_case_name, flow_execution_id,
            execution_status, started_at, completed_at, duration_ms,
            output_variables, error_message, passed, validation_output,
            validation_notes, evaluated_at, batch_run_id
        FROM evaluation.eval_run_detail
        WHERE eval_run_id = :run_id
    """)
    
    result = db.execute(query, {"run_id": run_uuid}).first()
    if not result:
        raise HTTPException(status_code=404, detail="Eval run not found")
    
    return {
        "id": str(result[0]),
        "eval_case_id": str(result[1]),
        "eval_case_name": result[2],
        "flow_execution_id": str(result[3]),
        "execution_status": result[4],
        "started_at": result[5],
        "completed_at": result[6],
        "duration_ms": float(result[7]) if result[7] else None,
        "output_variables": result[8],
        "error_message": result[9],
        "passed": result[10],
        "validation_output": result[11],
        "validation_notes": result[12],
        "evaluated_at": result[13],
        "batch_run_id": str(result[14]) if result[14] else None
    }


class EvalRunDetailResponse(BaseModel):
    """Detailed response model for eval run with logs and conversation."""
    id: str
    eval_case_id: str
    eval_case_name: str
    workflow_name: str
    flow_execution_id: str
    
    # Execution details
    execution_status: str
    started_at: datetime
    completed_at: Optional[datetime]
    duration_ms: Optional[float]
    
    # Input/Output
    input_variables: List[Dict[str, Any]]
    output_variables: Optional[List[Dict[str, Any]]]
    expected_output: Optional[Dict[str, Any]]
    
    # Validation
    passed: Optional[bool]
    validation_output: Optional[Dict[str, Any]]
    validation_notes: Optional[str]
    evaluated_at: Optional[datetime]
    
    # Error info
    error_message: Optional[str]
    failure_detail: Optional[Dict[str, Any]]
    
    # Batch info
    batch_run_id: Optional[str]
    batch_name: Optional[str]
    
    class Config:
        from_attributes = True


@router.get("/eval-runs/{run_id}/logs")
async def get_eval_run_logs(run_id: str, db: Session = Depends(get_db)):
    """
    Get execution logs for an eval run from the artifacts directory.
    Returns the conversation/execution log if available.
    """
    try:
        run_uuid = UUID(run_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid run ID format")
    
    # Query to get log_file path
    query = text("""
        SELECT 
            fe.log_file,
            er.flow_execution_id
        FROM evaluation.eval_run er
        JOIN flow_execution.flow_execution fe ON er.flow_execution_id = fe.id
        WHERE er.id = :run_id
    """)
    
    result = db.execute(query, {"run_id": run_uuid}).first()
    if not result:
        raise HTTPException(status_code=404, detail="Eval run not found")
    
    log_file_path = result[0]
    flow_execution_id = str(result[1])
    
    # Get artifacts directory from environment or use default
    agentic_core_path_str = os.getenv("AGENTIC_CORE_PATH", "../agentic-core")
    agentic_core_path = Path(agentic_core_path_str)
    if not agentic_core_path.is_absolute():
        agentic_core_path = (project_root / agentic_core_path).resolve()
    
    artifacts_dir = agentic_core_path / "artifacts"
    
    # Try multiple possible log locations
    possible_paths = []
    if log_file_path:
        possible_paths.append(artifacts_dir / log_file_path)
    
    # Also try flow_execution_id directly
    # GraphQL log writer creates flow-execution.log (with dash!)
    possible_paths.append(artifacts_dir / flow_execution_id / "flow-execution.log")
    possible_paths.append(artifacts_dir / flow_execution_id / "flow_execution.log")
    possible_paths.append(artifacts_dir / flow_execution_id / "conversation.log")
    possible_paths.append(artifacts_dir / flow_execution_id / "execution.log")
    
    # Try to read from any of the possible locations
    log_content = None
    log_path_used = None
    
    for path in possible_paths:
        if path.exists() and path.is_file():
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    log_content = f.read()
                log_path_used = str(path)
                break
            except Exception as e:
                logger.warning(f"Failed to read log file {path}: {e}")
                continue
    
    return {
        "log_content": log_content,
        "log_path": log_path_used,
        "configured_path": log_file_path,
        "artifacts_dir": str(artifacts_dir),
        "found": log_content is not None
    }


@router.get("/eval-runs/{run_id}/details", response_model=EvalRunDetailResponse)
async def get_eval_run_details(run_id: str, db: Session = Depends(get_db)):
    """
    Get complete eval run details including logs, conversation, and execution trace.
    This endpoint provides all data needed for the run detail page.
    """
    try:
        run_uuid = UUID(run_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid run ID format")
    
    # Query comprehensive data
    query = text("""
        SELECT 
            er.id as eval_run_id,
            er.eval_case_id,
            ec.name as eval_case_name,
            ec.input_variables,
            ec.expected_output,
            
            -- Flow execution details
            er.flow_execution_id,
            fe.result as execution_status,
            fe.start_time as started_at,
            fe.end_time as completed_at,
            EXTRACT(EPOCH FROM (fe.end_time - fe.start_time)) * 1000 as duration_ms,
            fe.variables as all_variables,
            fe.failure_reason as error_message,
            fe.failure_detail,
            
            -- Validation results
            er.passed,
            er.validation_output,
            er.validation_notes,
            er.evaluated_at,
            
            -- Workflow info
            f.name as workflow_name,
            
            -- Batch info
            er.batch_run_id,
            eb.name as batch_name
            
        FROM evaluation.eval_run er
        JOIN evaluation.eval_case ec ON er.eval_case_id = ec.id
        JOIN flow_execution.flow_execution fe ON er.flow_execution_id = fe.id
        JOIN agentic_objects.flow f ON ec.workflow_id = f.id
        LEFT JOIN evaluation.eval_batch_run eb ON er.batch_run_id = eb.id
        WHERE er.id = :run_id
    """)
    
    result = db.execute(query, {"run_id": run_uuid}).first()
    if not result:
        raise HTTPException(status_code=404, detail="Eval run not found")
    
    # Split variables into input and output based on step_id
    # Variables with step_id=None are inputs, others are outputs
    all_variables = result[10] or []
    input_vars = [v for v in all_variables if v.get('step_id') is None]
    output_vars = [v for v in all_variables if v.get('step_id') is not None]
    
    # Normalize failure_detail to always be a dict or None
    failure_detail = result[12]
    if failure_detail is not None and not isinstance(failure_detail, dict):
        # Convert string errors to dict format
        failure_detail = {"error": str(failure_detail)}
    
    return {
        "id": str(result[0]),
        "eval_case_id": str(result[1]),
        "eval_case_name": result[2],
        "workflow_name": str(result[17]),
        "flow_execution_id": str(result[5]),
        
        "execution_status": result[6],
        "started_at": result[7],
        "completed_at": result[8],
        "duration_ms": float(result[9]) if result[9] else None,
        
        "input_variables": input_vars if input_vars else result[3],  # Fallback to eval_case inputs
        "output_variables": output_vars,
        "expected_output": result[4],
        
        "passed": result[13],
        "validation_output": result[14],
        "validation_notes": result[15],
        "evaluated_at": result[16],
        
        "error_message": result[11],
        "failure_detail": failure_detail,
        
        "batch_run_id": str(result[18]) if result[18] else None,
        "batch_name": result[19]
    }


@router.get("/eval-cases/{case_id}/runs", response_model=List[EvalRunResponse])
async def list_eval_runs_for_case(case_id: str, limit: int = 50, db: Session = Depends(get_db)):
    """List eval runs for a specific eval case."""
    try:
        case_uuid = UUID(case_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid case ID format")
    
    # Query from eval_run_detail view
    query = text("""
        SELECT 
            eval_run_id, eval_case_id, eval_case_name, flow_execution_id,
            execution_status, started_at, completed_at, duration_ms,
            output_variables, error_message, passed, validation_output,
            validation_notes, evaluated_at, batch_run_id
        FROM evaluation.eval_run_detail
        WHERE eval_case_id = :case_id
        ORDER BY started_at DESC
        LIMIT :limit
    """)
    
    results = db.execute(query, {"case_id": case_uuid, "limit": limit}).fetchall()
    
    return [
        {
            "id": str(row[0]),
            "eval_case_id": str(row[1]),
            "eval_case_name": row[2],
            "flow_execution_id": str(row[3]),
            "execution_status": row[4],
            "started_at": row[5],
            "completed_at": row[6],
            "duration_ms": float(row[7]) if row[7] else None,
            "output_variables": row[8],
            "error_message": row[9],
            "passed": row[10],
            "validation_output": row[11],
            "validation_notes": row[12],
            "evaluated_at": row[13],
            "batch_run_id": str(row[14]) if row[14] else None
        }
        for row in results
    ]


class CancelEvalRunRequest(BaseModel):
    """Request model for canceling an eval run."""
    flow_execution_id: str = Field(..., description="Flow execution ID to cancel")


@router.post("/eval-runs/{run_id}/cancel")
async def cancel_eval_run(run_id: str, request: CancelEvalRunRequest, db: Session = Depends(get_db)):
    """
    Cancel a running eval execution.
    
    This will:
    1. Mark the flow_execution as CANCELLED in agentic-core
    2. The eval_monitor will pick up the change and update the eval_run status
    """
    try:
        run_uuid = UUID(run_id)
        execution_uuid = UUID(request.flow_execution_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid ID format")
    
    # Verify the eval run exists and is active
    eval_run = db.query(EvalRun).filter(EvalRun.id == run_uuid).first()
    if not eval_run:
        raise HTTPException(status_code=404, detail="Eval run not found")
    
    # Check if execution is still running
    check_query = text("""
        SELECT result FROM flow_execution.flow_execution 
        WHERE id = :exec_id
    """)
    result = db.execute(check_query, {"exec_id": execution_uuid}).first()
    
    if not result:
        raise HTTPException(status_code=404, detail="Flow execution not found")
    
    if result[0] not in ['PENDING', 'RUNNING']:
        raise HTTPException(status_code=400, detail=f"Execution is already {result[0]}, cannot cancel")
    
    # Update the flow_execution to CANCELLED
    # Using FlowResult enum from agentic-core
    update_query = text("""
        UPDATE flow_execution.flow_execution
        SET result = 'CANCELLED',
            end_time = NOW(),
            cancelled_by = NULL
        WHERE id = :exec_id
    """)
    
    db.execute(update_query, {"exec_id": execution_uuid})
    db.commit()
    
    return {
        "message": "Execution cancelled successfully",
        "eval_run_id": str(run_uuid),
        "flow_execution_id": str(execution_uuid)
    }


# ============================================================================
# Auto-Generate Test Cases
# ============================================================================

class GenerateTestCasesRequest(BaseModel):
    """Request model for generating test cases."""
    count: int = Field(10, ge=1, le=50, description="Number of test cases to generate")
    

class GenerateTestCasesResponse(BaseModel):
    """Response model for generated test cases."""
    workflow_id: str
    test_cases: List[EvalCaseResponse]
    count: int


@router.post("/workflows/{workflow_id}/eval-cases/generate", response_model=GenerateTestCasesResponse)
async def generate_test_cases(
    workflow_id: str,
    request: GenerateTestCasesRequest,
    db: Session = Depends(get_db)
):
    """
    Auto-generate test cases for a workflow using LLM.
    
    This endpoint:
    1. Reads the workflow to extract input variable schema
    2. Uses OpenAI GPT to generate diverse test cases
    3. Creates eval_case records in the database
    4. Returns the created test cases
    """
    import openai
    import json
    from workflow_json_serializer import flow_to_json
    from database.agentic_objects.models import Flow, FlowVersion
    
    # Get workflow - workflow_id is actually the flow_version.id from the frontend
    version_uuid = UUID(workflow_id)
    flow_version = db.query(FlowVersion).filter(FlowVersion.id == version_uuid).first()
    
    if not flow_version:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Workflow {workflow_id} not found"
        )
    
    # Get flow
    flow = db.query(Flow).filter(Flow.id == flow_version.flow_id).first()
    
    if not flow:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Flow not found for workflow {workflow_id}"
        )
    
    # Convert to JSON format
    workflow_json = flow_to_json(db, flow, flow_version)
    workflow_name = flow.name
    created_by_user = flow.created_by  # Use the workflow creator as the test case creator
    
    # Extract input variables schema
    input_vars = workflow_json.get("variables", {}).get("input", {})
    
    if not input_vars:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Workflow has no input variables defined"
        )
    
    # Prepare prompt for LLM
    # Check if this is a company research workflow
    is_company_workflow = any(
        'company' in var_name.lower() 
        for var_name in input_vars.keys()
    )
    
    additional_instructions = ""
    if is_company_workflow:
        additional_instructions = """
IMPORTANT FOR COMPANY NAMES:
- Use ONLY real, well-known companies (e.g., Apple, Microsoft, Tesla, Amazon, Google, IBM, Nike, Starbucks, etc.)
- Mix large corporations, tech companies, retail brands, automotive companies
- Include both American and international companies
- DO NOT use fictional companies (e.g., Wayne Enterprises, Stark Industries)
- DO NOT use made-up names (e.g., TechCorp, XYZ Inc, ABC Company)
- DO NOT use placeholder names or empty strings
- Every company name must be a real, publicly known business
"""
    
    prompt = f"""You are a QA engineer creating diverse test cases for a workflow.

Workflow: {workflow_name}
Description: {workflow_json.get('description', 'N/A')}

Input Variables Schema:
{json.dumps(input_vars, indent=2)}
{additional_instructions}
Generate {request.count} diverse test cases. Each test case should have:
1. A descriptive name (short, 2-4 words)
2. Unique input values that test different scenarios
3. Real, meaningful data (not placeholders like "[PLACEHOLDER]" or "TODO")
4. For fields where data is not applicable, you MAY use "N/A" within structured data (e.g., "Revenue: $100M, Funding: N/A" is acceptable)
5. DO NOT use entire values that are just "N/A" - provide real data whenever possible

Return ONLY a JSON array of test cases in this exact format:
[
  {{
    "name": "test case name",
    "input_variables": {{
      "variable_name": "value"
    }}
  }}
]

Make the test cases varied and interesting. Use realistic data.

Return ONLY the JSON array, no explanation."""
    
    # Call OpenAI API
    openai_client = openai.OpenAI()
    
    try:
        response = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a helpful QA engineer. Always return valid JSON."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.8,  # Higher temperature for more creativity
            max_tokens=2000
        )
        
        # Parse response
        content = response.choices[0].message.content.strip()
        
        # Remove markdown code blocks if present
        if content.startswith("```"):
            content = content.split("```")[1]
            if content.startswith("json"):
                content = content[4:]
            content = content.strip()
        
        test_cases_data = json.loads(content)
        
        if not isinstance(test_cases_data, list):
            raise ValueError("Response is not a list")
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate test cases: {str(e)}"
        )
    
    # Create eval cases in database
    created_cases = []
    
    for tc_data in test_cases_data[:request.count]:  # Limit to requested count
        # Validate input variables - skip if any are empty or invalid
        input_vars_dict = tc_data.get("input_variables", {})
        
        # Check for empty or invalid values
        has_invalid_values = False
        for var_name, var_value in input_vars_dict.items():
            if not var_value or (isinstance(var_value, str) and not var_value.strip()):
                print(f"⚠️  Skipping test case '{tc_data.get('name')}' - empty value for '{var_name}'")
                has_invalid_values = True
                break
            # Check for common placeholder patterns
            # But allow "N/A" when it's part of structured data (e.g., "Revenue: $X, Users: N/A")
            if isinstance(var_value, str):
                lower_value = var_value.lower()
                # More strict checking: skip only if the ENTIRE value is a placeholder
                # or if it contains obvious placeholder markers
                stripped_value = var_value.strip()
                
                # Skip if the entire value is just a placeholder
                if stripped_value.lower() in ['n/a', 'tbd', 'placeholder', 'example', 'todo', '[placeholder]']:
                    print(f"⚠️  Skipping test case '{tc_data.get('name')}' - placeholder value: {var_value}")
                    has_invalid_values = True
                    break
                
                # Skip if it contains obvious placeholder markers (but allow "N/A" in structured data)
                if any(marker in lower_value for marker in ['[placeholder]', '[example]', '[tbd]', 'todo:', 'fixme:']):
                    print(f"⚠️  Skipping test case '{tc_data.get('name')}' - placeholder marker found: {var_value}")
                    has_invalid_values = True
                    break
        
        if has_invalid_values:
            continue
        
        # Format input_variables for database
        input_vars_list = [
            {
                "variable_name": var_name,
                "variable_value": var_value
            }
            for var_name, var_value in input_vars_dict.items()
        ]
        
        # Create eval case
        eval_case = EvalCase(
            id=uuid4(),
            workflow_id=flow.id,  # Use the actual flow id (not version id)
            name=tc_data.get("name", f"Generated Test {len(created_cases) + 1}"),
            description=f"Auto-generated test case",
            input_variables=input_vars_list,
            expected_output=None,
            validation_rules=None,
            tags=["auto-generated"],
            is_active=True,
            created_by=created_by_user,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(eval_case)
        created_cases.append(eval_case)
    
    db.commit()
    
    # Refresh to get all fields
    for case in created_cases:
        db.refresh(case)
    
    # Convert to response models
    case_responses = [
        EvalCaseResponse(
            id=str(case.id),
            workflow_id=str(case.workflow_id),
            name=case.name,
            description=case.description,
            input_variables=case.input_variables,
            expected_output=case.expected_output,
            validation_rules=case.validation_rules,
            tags=case.tags,
            is_active=case.is_active,
            created_at=case.created_at,
            updated_at=case.updated_at
        )
        for case in created_cases
    ]
    
    return GenerateTestCasesResponse(
        workflow_id=str(version_uuid),
        test_cases=case_responses,
        count=len(case_responses)
    )


# ============================================================================
# Batch Execution
# ============================================================================

class RunBatchRequest(BaseModel):
    """Request model for running multiple test cases."""
    eval_case_ids: List[str] = Field(..., description="List of eval case IDs to run")
    

class RunBatchResponse(BaseModel):
    """Response model for batch execution."""
    batch_id: str
    eval_case_ids: List[str]
    eval_run_ids: List[str]
    count: int


@router.post("/eval-cases/run-batch", response_model=RunBatchResponse)
async def run_eval_cases_batch(
    request: RunBatchRequest,
    db: Session = Depends(get_db)
):
    """
    Run multiple eval cases in parallel.
    
    This endpoint:
    1. Creates eval runs for all specified test cases
    2. Publishes all runs to RabbitMQ for parallel execution
    3. Returns batch information
    """
    from services.eval_runner import EvalRunner
    
    batch_id = str(uuid4())
    eval_runner = EvalRunner(db, enable_debugging=False)
    eval_run_ids = []
    
    # Create and run all eval cases
    for case_id_str in request.eval_case_ids:
        try:
            case_id = UUID(case_id_str)
            
            # Run the eval case
            run_id = await eval_runner.run_eval_case(case_id)
            eval_run_ids.append(str(run_id))
            
        except Exception as e:
            # Log error but continue with other cases
            print(f"Failed to run eval case {case_id_str}: {str(e)}")
            continue
    
    return RunBatchResponse(
        batch_id=batch_id,
        eval_case_ids=request.eval_case_ids,
        eval_run_ids=eval_run_ids,
        count=len(eval_run_ids)
    )


# ============================================================================
# Artifact Files
# ============================================================================

from fastapi.responses import FileResponse

@router.get("/eval-runs/{run_id}/artifacts")
async def list_artifacts(
    run_id: str,
    db: Session = Depends(get_db)
):
    """
    List all artifact files for an eval run.
    
    Returns a list of files with their paths and sizes.
    """
    import os
    from pathlib import Path
    
    # Get flow_execution_id for this eval run
    query = text("""
        SELECT flow_execution_id
        FROM evaluation.eval_run
        WHERE id = :run_id
    """)
    
    result = db.execute(query, {"run_id": run_id}).fetchone()
    
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Eval run {run_id} not found"
        )
    
    flow_execution_id = str(result[0])
    
    # Get artifacts directory
    # Artifacts can be stored in:
    # 1. /artifacts/{flow_execution_id}/ (top level)
    # 2. /artifacts/{flow_execution_id}/{step_execution_id}/ (subdirectories)
    artifacts_base = Path("/Users/<USER>/agentic-core/artifacts")
    flow_artifacts_dir = artifacts_base / flow_execution_id
    
    if not flow_artifacts_dir.exists():
        return {"artifacts": [], "artifacts_dir": str(flow_artifacts_dir)}
    
    # List all files recursively (includes subdirectories)
    artifacts = []
    for file_path in flow_artifacts_dir.rglob("*"):
        if file_path.is_file():
            rel_path = file_path.relative_to(flow_artifacts_dir)
            artifacts.append({
                "name": file_path.name,
                "path": str(rel_path),
                "size": file_path.stat().st_size,
                "extension": file_path.suffix.lower()
            })
    
    return {
        "artifacts": artifacts,
        "artifacts_dir": str(flow_artifacts_dir)
    }


@router.get("/eval-runs/{run_id}/artifacts/download")
@router.head("/eval-runs/{run_id}/artifacts/download")
async def download_artifact(
    run_id: str,
    file_path: str,
    db: Session = Depends(get_db)
):
    """
    Download a specific artifact file.
    
    Args:
        run_id: Eval run ID
        file_path: Relative path to file within artifacts directory
    """
    import os
    from pathlib import Path
    
    # Get flow_execution_id for this eval run
    query = text("""
        SELECT flow_execution_id
        FROM evaluation.eval_run
        WHERE id = :run_id
    """)
    
    result = db.execute(query, {"run_id": run_id}).fetchone()
    
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Eval run {run_id} not found"
        )
    
    flow_execution_id = str(result[0])
    
    # Construct full file path
    artifacts_base = Path("/Users/<USER>/agentic-core/artifacts")
    flow_artifacts_dir = artifacts_base / flow_execution_id
    full_path = flow_artifacts_dir / file_path
    
    # Security check: ensure file is within artifacts directory
    try:
        full_path = full_path.resolve()
        flow_artifacts_dir = flow_artifacts_dir.resolve()
        if not str(full_path).startswith(str(flow_artifacts_dir)):
            raise ValueError("Path traversal attempt detected")
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    if not full_path.exists() or not full_path.is_file():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"File not found: {file_path}"
        )
    
    return FileResponse(
        path=str(full_path),
        filename=full_path.name,
        media_type='application/octet-stream'
    )



