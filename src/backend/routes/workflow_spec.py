"""
API routes for workflow specification generation.
"""

import threading
import async<PERSON>
import json
from pathlib import Path
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, status
from fastapi.responses import FileResponse, StreamingResponse
from pydantic import BaseModel, Field

from services.workflow_spec_generator import get_generator


router = APIRouter(prefix="/api/workflow-spec", tags=["workflow-spec"])


class GenerateRequest(BaseModel):
    """Request model for workflow specification generation."""
    workflow_request: str = Field(
        ..., 
        description="Natural language description of the desired workflow",
        min_length=10,
        max_length=2000,
        examples=["Take an IC memo and produce a structured assessment of risks"]
    )


class GenerateResponse(BaseModel):
    """Response model for generation request."""
    task_id: str
    status: str
    message: str


class TaskStatus(BaseModel):
    """Response model for task status."""
    id: str
    status: str
    workflow_request: str
    messages: list
    progress: int
    created_at: str
    completed_at: str | None
    error: str | None
    download_url: str | None


@router.post("/generate", response_model=GenerateResponse)
async def generate_specification(request: GenerateRequest) -> Dict[str, Any]:
    """
    Start generating a workflow specification.
    Returns a task ID that can be polled for status updates.
    """
    try:
        generator = get_generator()
        
        # Create task
        task_id = generator.create_task(request.workflow_request)
        
        # Start generation in background thread
        thread = threading.Thread(
            target=generator.generate_specification,
            args=(task_id, request.workflow_request),
            daemon=True
        )
        thread.start()
        
        return {
            "task_id": task_id,
            "status": "processing",
            "message": "Workflow specification generation started"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting generation: {str(e)}"
        )


@router.get("/status/{task_id}", response_model=TaskStatus)
async def get_task_status(task_id: str) -> Dict[str, Any]:
    """
    Get the current status of a workflow specification generation task.
    Poll this endpoint every second for updates.
    """
    generator = get_generator()
    task = generator.get_task_status(task_id)
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task {task_id} not found"
        )
    
    # Build response
    response = {
        "id": task["id"],
        "status": task["status"],
        "workflow_request": task["workflow_request"],
        "messages": task["messages"],
        "progress": task["progress"],
        "created_at": task["created_at"],
        "completed_at": task["completed_at"],
        "error": task["error"],
        "download_url": None
    }
    
    # Add download URL if completed
    if task["status"] == "completed" and task.get("document_path"):
        response["download_url"] = f"/api/workflow-spec/download/{task_id}"
    
    return response


@router.get("/download/{task_id}")
async def download_specification(task_id: str):
    """
    Download the generated workflow specification document.
    """
    generator = get_generator()
    task = generator.get_task_status(task_id)
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task {task_id} not found"
        )
    
    if task["status"] != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Task is not completed. Current status: {task['status']}"
        )
    
    document_path = task.get("document_path")
    if not document_path or not Path(document_path).exists():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document file not found"
        )
    
    # Get workflow request for filename
    workflow_request = task["workflow_request"][:50].replace(" ", "_")
    filename = f"workflow_spec_{workflow_request}.docx"
    
    return FileResponse(
        path=document_path,
        media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        filename=filename
    )


@router.get("/stream/{task_id}")
async def stream_task_updates(task_id: str):
    """
    Stream real-time updates for a workflow specification generation task using SSE.
    """
    generator = get_generator()
    task = generator.get_task_status(task_id)
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task {task_id} not found"
        )
    
    async def event_generator():
        """Generate SSE events with task updates."""
        last_message_count = 0
        last_progress = 0
        
        while True:
            try:
                # Get current task status
                current_task = generator.get_task_status(task_id)
                
                if not current_task:
                    # Task was deleted or doesn't exist
                    yield f"data: {json.dumps({'type': 'error', 'message': 'Task not found'})}\n\n"
                    break
                
                # Check for new messages
                messages = current_task.get("messages", [])
                if len(messages) > last_message_count:
                    # Send new messages
                    for msg in messages[last_message_count:]:
                        event_data = {
                            "type": "message",
                            "message": msg
                        }
                        yield f"data: {json.dumps(event_data)}\n\n"
                    last_message_count = len(messages)
                
                # Check for progress updates
                current_progress = current_task.get("progress", 0)
                if current_progress != last_progress:
                    event_data = {
                        "type": "progress",
                        "progress": current_progress
                    }
                    yield f"data: {json.dumps(event_data)}\n\n"
                    last_progress = current_progress
                
                # Check if task is complete or errored
                status = current_task.get("status")
                if status in ["completed", "error"]:
                    event_data = {
                        "type": "status",
                        "status": status,
                        "download_url": f"/api/workflow-spec/download/{task_id}" if status == "completed" else None,
                        "error": current_task.get("error")
                    }
                    yield f"data: {json.dumps(event_data)}\n\n"
                    break
                
                # Wait before checking again
                await asyncio.sleep(0.1)  # Check 10 times per second for smoother updates
                
            except Exception as e:
                error_data = {
                    "type": "error",
                    "message": str(e)
                }
                yield f"data: {json.dumps(error_data)}\n\n"
                break
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"
        }
    )



