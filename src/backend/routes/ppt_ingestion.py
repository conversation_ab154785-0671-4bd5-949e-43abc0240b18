"""
PPT Ingestion API Routes

FastAPI routes for processing PowerPoint and PDF presentations.
"""

import os
import tempfile
from typing import Optional, List
from pathlib import Path

from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from pydantic import BaseModel

# Import from services module (path is set up in main.py)
from services.ppt_ingestion import get_ppt_ingestion_service, SlideData


router = APIRouter(prefix="/ppt-ingestion", tags=["ppt-ingestion"])


class SlideResponse(BaseModel):
    """Response model for a single slide."""
    page_number: int
    ocr_text: str
    enhanced_text: Optional[str] = None
    has_image: bool
    image_format: str


class ProcessingResponse(BaseModel):
    """Response model for presentation processing."""
    total_slides: int
    slides: List[SlideResponse]
    filename: str
    file_type: str


@router.post("/process", response_model=ProcessingResponse)
async def process_presentation(
    file: UploadFile = File(...),
    use_vision: bool = Form(True),
    vision_prompt: Optional[str] = Form(None)
):
    """
    Process a PowerPoint (PPTX) or PDF file.
    
    Args:
        file: The uploaded file (PPTX or PDF)
        use_vision: Whether to use GPT-4 Vision for enhanced text extraction
        vision_prompt: Custom prompt for vision model (optional)
    
    Returns:
        ProcessingResponse with all slide data
    """
    # Validate file type
    if not file.filename:
        raise HTTPException(status_code=400, detail="No filename provided")
    
    file_extension = Path(file.filename).suffix.lower()
    if file_extension not in [".pdf", ".pptx"]:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type: {file_extension}. Only .pdf and .pptx are supported."
        )
    
    # Save uploaded file to temporary location
    with tempfile.NamedTemporaryFile(
        delete=False,
        suffix=file_extension
    ) as tmp_file:
        tmp_path = tmp_file.name
        contents = await file.read()
        tmp_file.write(contents)
    
    try:
        # Initialize service
        service = get_ppt_ingestion_service()
        
        # Process file
        slides = service.process_file(
            file_path=tmp_path,
            use_vision=use_vision,
            vision_prompt=vision_prompt
        )
        
        # Convert to response model
        slide_responses = [
            SlideResponse(
                page_number=slide.page_number,
                ocr_text=slide.ocr_text,
                enhanced_text=slide.enhanced_text,
                has_image=bool(slide.image_data),
                image_format=slide.image_format
            )
            for slide in slides
        ]
        
        return ProcessingResponse(
            total_slides=len(slides),
            slides=slide_responses,
            filename=file.filename,
            file_type=file_extension[1:]  # Remove the dot
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")
    
    finally:
        # Clean up temporary file
        if os.path.exists(tmp_path):
            os.unlink(tmp_path)


@router.post("/process-to-text")
async def process_presentation_to_text(
    file: UploadFile = File(...),
    use_vision: bool = Form(True),
    vision_prompt: Optional[str] = Form(None)
):
    """
    Process a presentation and return as plain text.
    
    Args:
        file: The uploaded file (PPTX or PDF)
        use_vision: Whether to use GPT-4 Vision for enhanced text extraction
        vision_prompt: Custom prompt for vision model (optional)
    
    Returns:
        Plain text representation of all slides
    """
    # Validate file type
    if not file.filename:
        raise HTTPException(status_code=400, detail="No filename provided")
    
    file_extension = Path(file.filename).suffix.lower()
    if file_extension not in [".pdf", ".pptx"]:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type: {file_extension}. Only .pdf and .pptx are supported."
        )
    
    # Save uploaded file to temporary location
    with tempfile.NamedTemporaryFile(
        delete=False,
        suffix=file_extension
    ) as tmp_file:
        tmp_path = tmp_file.name
        contents = await file.read()
        tmp_file.write(contents)
    
    try:
        # Initialize service
        service = get_ppt_ingestion_service()
        
        # Process file
        slides = service.process_file(
            file_path=tmp_path,
            use_vision=use_vision,
            vision_prompt=vision_prompt
        )
        
        # Convert to text
        text_parts = []
        for slide in slides:
            text_parts.append(f"{'=' * 80}")
            text_parts.append(f"Slide/Page {slide.page_number}")
            text_parts.append(f"{'=' * 80}")
            text_parts.append("")
            
            text = slide.enhanced_text if slide.enhanced_text else slide.ocr_text
            text_parts.append(text)
            text_parts.append("")
        
        return {
            "text": "\n".join(text_parts),
            "total_slides": len(slides),
            "filename": file.filename
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")
    
    finally:
        # Clean up temporary file
        if os.path.exists(tmp_path):
            os.unlink(tmp_path)


@router.post("/upload")
async def upload_for_processing(
    file: UploadFile = File(...)
):
    """
    Upload a file and get back a path that can be used in workflows.
    
    The file is saved to a temporary location that workflow workers can access.
    """
    import uuid
    from datetime import datetime
    
    if not file.filename:
        raise HTTPException(status_code=400, detail="No filename provided")
    
    file_extension = Path(file.filename).suffix.lower()
    if file_extension not in [".pdf", ".pptx"]:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type: {file_extension}. Only .pdf and .pptx are supported."
        )
    
    # Create uploads directory if it doesn't exist
    upload_dir = Path("/tmp/ppt_uploads")
    upload_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate unique filename
    unique_id = uuid.uuid4().hex[:8]
    safe_filename = f"{Path(file.filename).stem}_{unique_id}{file_extension}"
    file_path = upload_dir / safe_filename
    
    # Save file
    try:
        contents = await file.read()
        with open(file_path, "wb") as f:
            f.write(contents)
        
        return {
            "success": True,
            "file_path": str(file_path),
            "original_filename": file.filename,
            "file_size": len(contents),
            "message": "File uploaded successfully. Use the file_path in your workflow."
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


@router.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "ppt-ingestion"}

