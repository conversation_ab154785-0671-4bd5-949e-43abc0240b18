#!/usr/bin/env python3
"""
FastAPI server for Axon-PFC workflow management.
Provides REST API for viewing, creating, and modifying workflows.
JSON format only.
"""

import os
import sys
import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from uuid import uuid4, UUID
from datetime import datetime
from dotenv import load_dotenv

from fastapi import Fast<PERSON><PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# Add agentic-core to Python path
# Get the project root (two levels up from this file)
project_root = Path(__file__).parent.parent.parent.resolve()

# Load .env from project root
load_dotenv(project_root / ".env")

# Get AGENTIC_CORE_PATH and resolve it relative to project root if it's a relative path
agentic_core_path_str = os.getenv("AGENTIC_CORE_PATH", "../agentic-core")
agentic_core_path = Path(agentic_core_path_str)

# If it's a relative path, resolve it relative to project root
if not agentic_core_path.is_absolute():
    agentic_core_path = (project_root / agentic_core_path).resolve()

backend_path = agentic_core_path / "backend"
backend_lib_path = backend_path / "lib"

if backend_path.exists():
    sys.path.insert(0, str(backend_lib_path))
    sys.path.insert(0, str(backend_path))
else:
    print(f"Warning: agentic-core backend not found at {backend_path}")
    print(f"Looked for: {agentic_core_path}")
    print(f"Project root: {project_root}")
    sys.exit(1)

# Add src/backend to path for local imports
src_backend_path = project_root / "src" / "backend"
if src_backend_path not in [Path(p) for p in sys.path]:
    sys.path.insert(0, str(src_backend_path))

# Import JSON serializer
from workflow_json_serializer import (
    flow_to_json,
    json_to_flow,
    flow_to_json_string,
    json_string_to_flow
)

# Database configuration
DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "postgresql://postgres:postgres@localhost:5432/invisible"
)

# Import agentic-core modules
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from database.users.models import User, Tenant
from database.agentic_objects.models import (
    Namespace, Flow, FlowVersion, Delegate, DelegateVersion,
    Step, AgenticEntityType
)

# Initialize FastAPI app
app = FastAPI(
    title="Axon-PFC Workflow Manager",
    description="REST API for managing agentic-core workflows",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict this to your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import and include routers
sys.path.insert(0, str(Path(__file__).parent))
from routes.evaluation import router as evaluation_router
from routes.dashboard import router as dashboard_router
from routes.ppt_ingestion import router as ppt_ingestion_router
from routes.workflow_spec import router as workflow_spec_router
from routes.workflow_json_generation import router as workflow_json_generation_router

app.include_router(evaluation_router)
app.include_router(dashboard_router)
app.include_router(ppt_ingestion_router)
app.include_router(workflow_spec_router)
app.include_router(workflow_json_generation_router)

# Database setup
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(bind=engine)


# Pydantic models for API
class WorkflowListItem(BaseModel):
    id: str
    name: str
    description: str
    created_date: datetime
    modified_date: datetime
    is_draft: bool

    class Config:
        from_attributes = True


class WorkflowResponse(BaseModel):
    """Response model for workflow in JSON format."""
    id: str
    flow_id: str
    name: str
    workflow: Dict[str, Any] = Field(..., description="Workflow in JSON format")
    
    class Config:
        from_attributes = True


class WorkflowCreate(BaseModel):
    """Request model for creating workflow from JSON."""
    name: str = Field(..., description="Workflow name")
    workflow: Dict[str, Any] = Field(..., description="Workflow in JSON format (without metadata)")
    namespace: str = Field(default="default", description="Namespace for the workflow")
    is_draft: bool = Field(default=False, description="Whether this is a draft")


class WorkflowUpdate(BaseModel):
    """Request model for updating workflow with JSON."""
    name: Optional[str] = Field(None, description="Workflow name (optional, defaults to current name)")
    workflow: Dict[str, Any] = Field(..., description="Workflow in JSON format (without metadata)")
    namespace: Optional[str] = Field(None, description="Namespace (optional, defaults to current)")
    is_draft: Optional[bool] = Field(None, description="Draft status (optional, defaults to current)")


# Database helper functions
def get_db():
    """Dependency to get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_system_user_id(db: Session) -> UUID:
    """Get or create system user."""
    system_user = db.query(User).filter(User.email == "system@localhost").first()
    if not system_user:
        # Create system tenant first
        system_tenant = db.query(Tenant).filter(Tenant.name == "system").first()
        if not system_tenant:
            system_tenant = Tenant(id=uuid4(), name="system")
            db.add(system_tenant)
            db.flush()
        
        system_user = User(
            id=uuid4(),
            email="system@localhost",
            display_name="System User",
            system_admin=True
        )
        db.add(system_user)
        db.commit()
    
    return system_user.id


def get_default_namespace_id(db: Session, user_id: UUID) -> UUID:
    """Get or create default namespace."""
    namespace = db.query(Namespace).filter(Namespace.name == "default").first()
    if not namespace:
        namespace = Namespace(
            id=uuid4(),
            name="default",
            created_by=user_id
        )
        db.add(namespace)
        db.commit()
    
    return namespace.id




# API Endpoints
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Axon-PFC Workflow Manager API",
        "version": "1.0.0",
        "docs": "/docs"
    }


@app.get("/api/workflows", response_model=List[WorkflowListItem])
async def list_workflows():
    """List all workflows."""
    db = SessionLocal()
    try:
        # Get all flows with their latest versions
        flows = db.query(Flow).filter(Flow.is_archived.is_(False)).all()
        
        result = []
        for flow in flows:
            # Get latest flow version
            flow_version = db.query(FlowVersion).filter(
                FlowVersion.flow_id == flow.id
            ).order_by(FlowVersion.modified_date.desc()).first()
            
            if flow_version:
                result.append(WorkflowListItem(
                    id=str(flow_version.id),
                    name=flow.name,
                    description=flow_version.description or "",
                    created_date=flow.created_date,
                    modified_date=flow_version.modified_date,
                    is_draft=flow_version.is_draft
                ))
        
        return result
    finally:
        db.close()


@app.get("/api/workflows/{workflow_id}")
async def get_workflow(workflow_id: str):
    """Get a specific workflow by ID in JSON format."""
    db = SessionLocal()
    try:
        # Parse UUID
        try:
            version_uuid = UUID(workflow_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid workflow ID format")
        
        # Get flow version
        flow_version = db.query(FlowVersion).filter(
            FlowVersion.id == version_uuid
        ).first()
        
        if not flow_version:
            raise HTTPException(status_code=404, detail="Workflow not found")
        
        # Get flow
        flow = db.query(Flow).filter(Flow.id == flow_version.flow_id).first()
        if not flow:
            raise HTTPException(status_code=404, detail="Flow not found")
        
        # Get namespace
        namespace = db.query(Namespace).filter(Namespace.id == flow.namespace_id).first()
        
        # Convert to JSON
        workflow_json = flow_to_json(db, flow, flow_version)
        
        return {
            "id": str(flow_version.id),
            "flow_id": str(flow.id),
            "name": flow.name,
            "namespace": namespace.name if namespace else "default",
            "created_at": flow.created_date.isoformat() if flow.created_date else None,
            "modified_at": flow_version.modified_date.isoformat() if flow_version.modified_date else None,
            "is_draft": flow_version.is_draft,
            "workflow": workflow_json
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving workflow: {str(e)}")
    finally:
        db.close()


@app.post("/api/workflows", status_code=status.HTTP_201_CREATED)
async def create_workflow(data: WorkflowCreate):
    """Create a new workflow from JSON format."""
    db = SessionLocal()
    try:
        # Get system user
        user_id = get_system_user_id(db)
        
        # Create workflow from JSON (name and metadata passed separately)
        flow, flow_version = json_to_flow(
            db, 
            data.workflow, 
            user_id,
            name=data.name,
            namespace_name=data.namespace,
            is_draft=data.is_draft
        )
        db.commit()
        
        # Return the created workflow in JSON format
        workflow_json = flow_to_json(db, flow, flow_version)
        
        # Get namespace for response
        namespace = db.query(Namespace).filter(Namespace.id == flow.namespace_id).first()
        
        return {
            "id": str(flow_version.id),
            "flow_id": str(flow.id),
            "name": flow.name,
            "namespace": namespace.name if namespace else "default",
            "created_at": flow.created_date.isoformat() if flow.created_date else None,
            "modified_at": flow_version.modified_date.isoformat() if flow_version.modified_date else None,
            "is_draft": flow_version.is_draft,
            "workflow": workflow_json
        }
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating workflow: {str(e)}")
    finally:
        db.close()


@app.put("/api/workflows/{workflow_id}")
async def update_workflow(workflow_id: str, data: WorkflowUpdate):
    """Update a workflow using JSON format."""
    db = SessionLocal()
    try:
        # Parse UUID
        try:
            version_uuid = UUID(workflow_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid workflow ID format")
        
        # Get current workflow
        current_flow_version = db.query(FlowVersion).filter(
            FlowVersion.id == version_uuid
        ).first()
        
        if not current_flow_version:
            raise HTTPException(status_code=404, detail="Workflow not found")
        
        current_flow = db.query(Flow).filter(Flow.id == current_flow_version.flow_id).first()
        if not current_flow:
            raise HTTPException(status_code=404, detail="Flow not found")
        
        # Get system user
        user_id = get_system_user_id(db)
        
        # Get namespace for name if provided
        namespace_name = data.namespace
        if namespace_name is None:
            namespace = db.query(Namespace).filter(Namespace.id == current_flow.namespace_id).first()
            namespace_name = namespace.name if namespace else "default"
        
        # Update workflow from JSON (name and metadata passed separately)
        updated_flow, updated_version = json_to_flow(
            db, 
            data.workflow, 
            user_id,
            name=data.name if data.name else current_flow.name,
            namespace_name=namespace_name,
            flow_id=current_flow.id,
            is_draft=data.is_draft if data.is_draft is not None else current_flow_version.is_draft
        )
        db.commit()
        
        # Return updated workflow in JSON format
        workflow_json = flow_to_json(db, updated_flow, updated_version)
        
        # Get namespace for response
        namespace = db.query(Namespace).filter(Namespace.id == updated_flow.namespace_id).first()
        
        return {
            "id": str(updated_version.id),
            "flow_id": str(updated_flow.id),
            "name": updated_flow.name,
            "namespace": namespace.name if namespace else "default",
            "created_at": updated_flow.created_date.isoformat() if updated_flow.created_date else None,
            "modified_at": updated_version.modified_date.isoformat() if updated_version.modified_date else None,
            "is_draft": updated_version.is_draft,
            "workflow": workflow_json
        }
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating workflow: {str(e)}")
    finally:
        db.close()


@app.delete("/api/workflows/{workflow_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_workflow(workflow_id: str):
    """Delete a workflow (archives it)."""
    db = SessionLocal()
    try:
        # Parse UUID
        try:
            version_uuid = UUID(workflow_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid workflow ID format")
        
        # Get flow version
        flow_version = db.query(FlowVersion).filter(
            FlowVersion.id == version_uuid
        ).first()
        
        if not flow_version:
            raise HTTPException(status_code=404, detail="Workflow not found")
        
        # Get flow
        flow = db.query(Flow).filter(Flow.id == flow_version.flow_id).first()
        if not flow:
            raise HTTPException(status_code=404, detail="Flow not found")
        
        # Archive the flow
        flow.is_archived = True
        db.commit()
        
        return None
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting workflow: {str(e)}")
    finally:
        db.close()


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    db = SessionLocal()
    try:
        # Test database connection
        result = db.execute(text("SELECT 1"))
        result.fetchone()
        return {"status": "healthy", "database": "connected"}
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={"status": "unhealthy", "database": "disconnected", "error": str(e)}
        )
    finally:
        db.close()


if __name__ == "__main__":
    import uvicorn
    import os
    
    # Enable auto-reload in development
    reload = os.getenv("ENABLE_RELOAD", "true").lower() == "true"
    
    uvicorn.run(
        "main:app",  # Must be import string for reload to work
        host="0.0.0.0", 
        port=8000,
        reload=reload,
        reload_dirs=["./"] if reload else None
    )

