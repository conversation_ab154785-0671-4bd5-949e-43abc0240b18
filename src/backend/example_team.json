{"description": "A workflow that uses a team of agents to create collaborative greetings", "variables": {"input": {"topic": {"type": "string", "default": "a wonderful day", "description": "The theme for the greeting (e.g., 'space exploration', 'ocean adventure', 'wonderful day')"}}, "output": {"conversation_log": {"type": "array", "description": "Full team conversation"}, "greeting_message": {"type": "string", "description": "The final collaborative greeting"}}}, "steps": [{"order": 1, "type": "team", "team": {"name": "Hello World Greeting Team", "goal": "Create a creative and warm greeting message by having multiple agents collaborate.\nEach agent brings their unique perspective to craft the perfect welcoming message.\n", "results": "- A complete greeting message that combines contributions from all team members\n- The greeting should be warm, creative, and theme-appropriate\n- Output should include the final greeting and a log of who contributed what\n", "termination": {"regex": ["FINAL_GREETING_COMPLETE", "TERMINATE"]}, "agents": [{"name": "greeting_coordinator", "model": "gpt-4o", "skills": "You are the coordinator. Your job is to orchestrate the greeting creation:\n\n1. First message: Read the 'topic' variable and ask @greeting_creator to create an opening greeting for that topic\n2. After greeting_creator responds, ask @message_enhancer to add an inspiring middle section\n3. After message_enhancer responds, ask @closing_specialist to finalize and save the greeting\n4. DO NOT create content yourself - only coordinate by asking other agents\n\nSelection guide:\n{roles}\n\nConversation so far:\n{history}\n\nAvailable agents: {participants}\n", "persona": "You are a coordinator who delegates work to specialists. You never create content yourself, only assign tasks to the right agents."}, {"name": "greeting_creator", "model": "gpt-4o", "skills": "When the coordinator asks you:\n1. Create a 1-sentence opening greeting based on the topic\n2. Use an appropriate emoji (🌟 for space, 🌊 for ocean, 🎉 for general topics)\n3. Examples:\n   - \"🌟 Welcome to the thrilling world of space exploration!\"\n   - \"🌊 Welcome to the depths of ocean adventure!\"\n4. After creating it, say: \"Opening complete. Coordinator, please ask @message_enhancer to add the middle section.\"\n5. DO NOT do any other agent's work\n", "persona": "You create opening greetings only. You are brief and focused on your single task."}, {"name": "message_enhancer", "model": "gpt-4o", "skills": "When the coordinator asks you:\n1. Read the opening greeting from the conversation\n2. Add ONE inspiring sentence that fits the theme\n3. Keep it short and poetic\n4. After adding it, say: \"Enhancement complete. Coordinator, please ask @closing_specialist to finalize.\"\n5. DO NOT do any other agent's work\n", "persona": "You add inspiring middle sections only. You are brief and focused on your single task."}, {"name": "closing_specialist", "model": "gpt-4o", "skills": "When the coordinator asks you:\n1. Read ALL messages from greeting_creator and message_enhancer\n2. Add a brief closing line with signature\n3. Combine everything into the FINAL greeting (opening + middle + closing)\n4. **REQUIRED**: Use save_variable tool to save the final greeting:\n   save_variable(\"greeting_message\", \"<the complete final greeting>\")\n5. **REQUIRED**: After saving, you MUST say exactly: \"TERMINATE\"\n6. Example final message: \"Done! TERMINATE\"\n", "persona": "You finalize greetings and save them. You always save variables and say TERMINATE when done."}], "variables": {"topic": {"type": "string", "default": "a wonderful day", "description": "The theme for the greeting (e.g., 'space exploration', 'ocean adventure', 'wonderful day')"}}}}]}