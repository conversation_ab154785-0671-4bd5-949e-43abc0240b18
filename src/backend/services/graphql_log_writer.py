#!/usr/bin/env python3
"""
GraphQL Event Log Writer Service

This service subscribes to GraphQL flow execution events and writes them to log files
in the artifacts directory, making logs available for the evaluation system.

Features:
- Subscribes to flowExecutionStream for all active executions (regardless of debugging_enabled)
- Writes formatted logs to artifacts/{execution_id}/flow-execution.log
- Handles FlowExecutionEvent, FlowExecutionArtifactEvent, and FlowExecutionDebugEvent
- Auto-discovers new flow executions from the database
- Maintains active subscriptions until completion

Usage:
    python -m src.backend.services.graphql_log_writer
"""

import os
import sys
import asyncio
import json
import base64
import websockets
from pathlib import Path
from datetime import datetime
from typing import Dict, Set, Optional
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))


class GraphQLLogWriter:
    """Subscribes to GraphQL events and writes them to log files."""
    
    def __init__(self):
        self.ws_url = self._get_websocket_url()
        self.credentials = self._get_credentials()
        self.active_subscriptions: Set[str] = set()
        self.subscription_tasks: Dict[str, asyncio.Task] = {}
        self.log_handles: Dict[str, object] = {}
        
        # Database setup
        database_url = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/invisible")
        self.engine = create_engine(database_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        
        # Artifacts directory
        agentic_core_path = os.getenv("AGENTIC_CORE_PATH", "../agentic-core")
        if not os.path.isabs(agentic_core_path):
            agentic_core_path = os.path.abspath(os.path.join(project_root, agentic_core_path))
        
        self.artifacts_dir = Path(agentic_core_path) / "artifacts"
        self.artifacts_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"📁 Artifacts directory: {self.artifacts_dir}", flush=True)
    
    def _get_websocket_url(self) -> str:
        """Get WebSocket URL from environment."""
        # Try various environment variable names
        ws_url = os.getenv('GRAPHQL_WS_URL') or \
                 os.getenv('WS_URL') or \
                 'ws://localhost:5002/graphql/'
        return ws_url
    
    def _get_credentials(self) -> str:
        """Get base64 encoded credentials for WebSocket auth."""
        username = os.getenv('SYSTEM_USER', 'system')
        password = os.getenv('SYSTEM_PASSWORD', 'foo')  # GraphQL service uses 'foo' as default password
        return base64.b64encode(f"{username}:{password}".encode()).decode()
    
    def _get_active_flow_executions(self) -> list:
        """Get all currently active flow executions from database."""
        db = self.SessionLocal()
        try:
            query = text("""
                SELECT id, result, debugging_enabled
                FROM flow_execution.flow_execution
                WHERE result IN ('PENDING', 'RUNNING')
                ORDER BY start_time DESC
            """)
            
            results = db.execute(query).fetchall()
            return [(str(row[0]), row[1], row[2]) for row in results]
        finally:
            db.close()
    
    def _get_log_file_path(self, execution_id: str) -> Path:
        """Get log file path for an execution."""
        exec_dir = self.artifacts_dir / execution_id
        exec_dir.mkdir(parents=True, exist_ok=True)
        return exec_dir / "flow-execution.log"
    
    def _open_log_file(self, execution_id: str) -> object:
        """Open log file for writing."""
        if execution_id in self.log_handles:
            return self.log_handles[execution_id]
        
        log_path = self._get_log_file_path(execution_id)
        handle = open(log_path, 'a', encoding='utf-8', buffering=1)  # Line buffered
        self.log_handles[execution_id] = handle
        return handle
    
    def _close_log_file(self, execution_id: str):
        """Close log file for an execution."""
        if execution_id in self.log_handles:
            self.log_handles[execution_id].close()
            del self.log_handles[execution_id]
    
    def _write_log_entry(self, execution_id: str, entry: str):
        """Write an entry to the log file."""
        try:
            log_handle = self._open_log_file(execution_id)
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
            log_handle.write(f"[{timestamp}] {entry}\n")
            log_handle.flush()
        except Exception as e:
            print(f"❌ Error writing log for {execution_id}: {e}", flush=True)
    
    def _format_flow_event(self, event: Dict) -> str:
        """Format a FlowExecutionEvent for logging."""
        component_type = event.get('component_type', 'Unknown') or 'Unknown'
        component_id = event.get('component_id') or ''
        step_id = event.get('step_id') or ''
        message = event.get('message') or ''
        
        parts = [f"[{component_type}]"]
        if component_id and component_id not in ['None', 'null', 'system']:
            parts.append(f"[{component_id}]")
        if step_id and step_id not in ['None', 'null']:
            parts.append(f"(step:{step_id})")
        if message:  # Only append if message is not empty
            parts.append(message)
        
        return " ".join(parts)
    
    def _format_artifact_event(self, event: Dict) -> str:
        """Format a FlowExecutionArtifactEvent for logging."""
        file_name = event.get('file_name') or 'Unknown'
        message = event.get('message') or 'File operation'
        storage_path = event.get('storage_path') or ''
        
        return f"[ARTIFACT] {message}: {file_name} ({storage_path})"
    
    def _format_debug_event(self, event: Dict) -> str:
        """Format a FlowExecutionDebugEvent for logging."""
        event_type = event.get('event_type') or 'Unknown'
        context = event.get('context') or ''
        variables = event.get('variables') or ''
        
        parts = [f"[DEBUG:{event_type}]"]
        if context:
            parts.append(f"Context: {context}")
        if variables:
            parts.append(f"Variables: {variables}")
        
        return " ".join(parts)
    
    def _format_terminated_event(self, event: Dict) -> str:
        """Format a FlowExecutionTerminatedEvent for logging."""
        status = event.get('status') or 'Unknown'
        message = event.get('message') or 'Execution terminated'
        detail = event.get('detail') or ''
        
        parts = [f"[TERMINATED:{status}]", message]
        if detail:
            parts.append(f"\n  Detail: {detail}")
        
        return " ".join(parts)
    
    async def subscribe_to_execution(self, execution_id: str):
        """Subscribe to a single flow execution and write logs."""
        if execution_id in self.active_subscriptions:
            return
        
        self.active_subscriptions.add(execution_id)
        print(f"📡 Subscribing to execution: {execution_id}", flush=True)
        
        self._write_log_entry(execution_id, "=== Flow Execution Log Started ===")
        
        try:
            # Connect with proper subprotocol
            async with websockets.connect(
                self.ws_url,
                subprotocols=["graphql-transport-ws"],
                additional_headers={
                    "Authorization": f"Basic {self.credentials}"
                }
            ) as websocket:
                
                # Initialize connection
                await websocket.send(json.dumps({
                    "type": "connection_init",
                    "payload": {
                        "Authorization": f"Basic {self.credentials}"
                    }
                }))
                
                # Wait for ack
                ack = await websocket.recv()
                ack_data = json.loads(ack)
                if ack_data.get("type") != "connection_ack":
                    raise Exception(f"Connection failed: {ack_data}")
                
                # Subscribe to execution stream
                await websocket.send(json.dumps({
                    "id": execution_id,
                    "type": "subscribe",
                    "payload": {
                        "query": """subscription ($id: ID!) {
                            flowExecutionStream(id: $id) {
                                ... on FlowExecutionEvent {
                                    execution_id
                                    flow_id
                                    step_id
                                    component_id
                                    component_type
                                    message
                                    timestamp
                                    reason
                                }
                                ... on FlowExecutionArtifactEvent {
                                    execution_id
                                    file_name
                                    message
                                    storage_path
                                    operation
                                    timestamp
                                }
                                ... on FlowExecutionDebugEvent {
                                    flow_execution_id
                                    event_type
                                    context
                                    variables
                                    timestamp
                                }
                                ... on FlowExecutionTerminatedEvent {
                                    flow_execution_id
                                    status
                                    message
                                    detail
                                }
                            }
                        }""",
                        "variables": {"id": execution_id}
                    }
                }))
                
                # Process events
                while True:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=60.0)
                        data = json.loads(response)
                        
                        if data.get("type") == "next" and "payload" in data:
                            event = data["payload"].get("data", {}).get("flowExecutionStream")
                            if event:
                                # Format and write based on event type
                                if 'component_type' in event:
                                    # FlowExecutionEvent
                                    log_entry = self._format_flow_event(event)
                                    self._write_log_entry(execution_id, log_entry)
                                    
                                elif 'file_name' in event:
                                    # FlowExecutionArtifactEvent
                                    log_entry = self._format_artifact_event(event)
                                    self._write_log_entry(execution_id, log_entry)
                                    
                                elif 'event_type' in event:
                                    # FlowExecutionDebugEvent
                                    log_entry = self._format_debug_event(event)
                                    self._write_log_entry(execution_id, log_entry)
                                
                                elif 'status' in event:
                                    # FlowExecutionTerminatedEvent
                                    log_entry = self._format_terminated_event(event)
                                    self._write_log_entry(execution_id, log_entry)
                                    
                                    # Execution completed, exit
                                    print(f"✓ Execution completed: {execution_id}", flush=True)
                                    break
                        
                        elif data.get("type") == "complete":
                            print(f"✓ Subscription completed: {execution_id}", flush=True)
                            break
                            
                    except asyncio.TimeoutError:
                        # Check if execution is still active
                        continue
                    except websockets.exceptions.ConnectionClosed:
                        print(f"⚠️ Connection closed for: {execution_id}", flush=True)
                        break
                        
        except Exception as e:
            error_msg = f"❌ Error subscribing to {execution_id}: {e}"
            print(error_msg, flush=True)
            self._write_log_entry(execution_id, f"ERROR: {error_msg}")
        
        finally:
            self._write_log_entry(execution_id, "=== Flow Execution Log Ended ===")
            self._close_log_file(execution_id)
            self.active_subscriptions.discard(execution_id)
            if execution_id in self.subscription_tasks:
                del self.subscription_tasks[execution_id]
    
    async def monitor_new_executions(self):
        """Continuously monitor for new flow executions and subscribe to them."""
        print("👀 Monitoring for new flow executions...", flush=True)
        
        while True:
            try:
                # Get active executions
                executions = self._get_active_flow_executions()
                
                # Start subscriptions for new executions
                for execution_id, result, debugging in executions:
                    if execution_id not in self.active_subscriptions:
                        task = asyncio.create_task(
                            self.subscribe_to_execution(execution_id)
                        )
                        self.subscription_tasks[execution_id] = task
                
                # Wait before checking again
                await asyncio.sleep(0.3)  # Check every 300ms to catch fast executions
                
            except Exception as e:
                print(f"❌ Error in monitor loop: {e}", flush=True)
                await asyncio.sleep(5)
    
    async def run(self):
        """Run the log writer service."""
        print("🚀 GraphQL Event Log Writer Service Started", flush=True)
        print(f"📡 WebSocket URL: {self.ws_url}", flush=True)
        print("=" * 70, flush=True)
        
        try:
            await self.monitor_new_executions()
        except KeyboardInterrupt:
            print("\n⚠️ Shutting down...", flush=True)
        finally:
            # Clean up
            for execution_id in list(self.log_handles.keys()):
                self._close_log_file(execution_id)
            
            print("✓ GraphQL Event Log Writer Service Stopped", flush=True)


async def main():
    """Main entry point."""
    writer = GraphQLLogWriter()
    await writer.run()


if __name__ == "__main__":
    asyncio.run(main())

