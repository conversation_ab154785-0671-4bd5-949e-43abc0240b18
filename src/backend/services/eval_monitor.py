"""
EvalMonitor background daemon for validation.

Monitors completed flow executions and validates them against expected outputs.
Runs as a background process.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime
from dotenv import load_dotenv

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session

# Load environment variables from .env file
# __file__ is src/backend/services/eval_monitor.py, so parent.parent.parent.parent is project root
project_root = Path(__file__).parent.parent.parent.parent.resolve()
env_file = project_root / ".env"
print(f"Loading .env from: {env_file}", flush=True)
print(f".env exists: {env_file.exists()}", flush=True)
load_dotenv(env_file)
print(f"DATABASE_URL from env: {os.getenv('DATABASE_URL')}", flush=True)

# Add agentic-core to path
agentic_core_path_str = os.getenv("AGENTIC_CORE_PATH", "../agentic-core")
agentic_core_path = Path(agentic_core_path_str)
if not agentic_core_path.is_absolute():
    agentic_core_path = (project_root / agentic_core_path).resolve()
backend_path = agentic_core_path / "backend"
sys.path.insert(0, str(backend_path / "lib"))
sys.path.insert(0, str(backend_path))

# Import models
sys.path.insert(0, str(project_root / "src" / "backend"))
from models.evaluation import EvalCase, EvalRun, EvalBatchRun
from database.flow_execution.models import FlowExecution
from data_types.database.flow_executions import FlowResult


logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EvalMonitor:
    """
    Background monitor for eval run validation.
    
    Monitors flow executions linked to eval runs and validates results
    when executions complete.
    """
    
    def __init__(self, database_url: Optional[str] = None, check_interval: float = 2.0):
        """
        Initialize the eval monitor.
        
        Args:
            database_url: Database connection string (defaults to env var)
            check_interval: How often to check for pending validations (seconds)
        """
        self.database_url = database_url or os.getenv(
            "DATABASE_URL",
            "postgresql://postgres:postgres@localhost:5432/invisible"
        )
        self.check_interval = check_interval
        self.running = False
        
        # Database setup
        self.engine = create_engine(self.database_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        
    async def start(self):
        """Start the monitor loop."""
        self.running = True
        logger.info(f"EvalMonitor started (check interval: {self.check_interval}s)")
        
        while self.running:
            try:
                await self.check_pending_validations()
            except Exception as e:
                logger.error(f"Error in validation check: {e}", exc_info=True)
            
            await asyncio.sleep(self.check_interval)
        
        logger.info("EvalMonitor stopped")
    
    def stop(self):
        """Stop the monitor loop."""
        self.running = False
    
    async def check_pending_validations(self):
        """Check for completed executions that need validation."""
        db = self.SessionLocal()
        try:
            # Find eval runs with completed executions that haven't been validated
            pending = (
                db.query(EvalRun, FlowExecution)
                .join(FlowExecution, EvalRun.flow_execution_id == FlowExecution.id)
                .filter(
                    FlowExecution.result.in_([FlowResult.COMPLETE.value, FlowResult.FAILED.value]),
                    EvalRun.evaluated_at.is_(None)
                )
                .all()
            )
            
            if pending:
                logger.info(f"Found {len(pending)} eval run(s) pending validation")
            
            for eval_run, flow_execution in pending:
                try:
                    await self.validate_eval_run(db, eval_run, flow_execution)
                except Exception as e:
                    logger.error(f"Error validating eval run {eval_run.id}: {e}", exc_info=True)
        
        finally:
            db.close()
    
    async def validate_eval_run(self, db: Session, eval_run: EvalRun, flow_execution: FlowExecution):
        """
        Validate an eval run against its expected outputs.
        
        Args:
            db: Database session
            eval_run: EvalRun instance
            flow_execution: FlowExecution instance
        """
        logger.info(f"Validating eval run {eval_run.id} (execution {flow_execution.id})")
        
        # Get eval case
        eval_case = db.query(EvalCase).filter(EvalCase.id == eval_run.eval_case_id).first()
        if not eval_case:
            logger.error(f"Eval case {eval_run.eval_case_id} not found")
            return
        
        # If execution failed, mark as failed validation
        if flow_execution.result == FlowResult.FAILED.value:
            eval_run.passed = False
            eval_run.validation_notes = f"Execution failed: {flow_execution.failure_reason or 'Unknown error'}"
            eval_run.validation_output = {
                "error": flow_execution.failure_reason,
                "error_detail": flow_execution.failure_detail
            }
            eval_run.evaluated_at = datetime.utcnow()
            db.commit()
            logger.info(f"Eval run {eval_run.id} marked as failed (execution failed)")
            return
        
        # Get actual outputs
        actual_outputs = flow_execution.output_variables or []
        
        # If no expected output, mark as passed (execution completed successfully)
        if not eval_case.expected_output:
            eval_run.passed = True
            eval_run.validation_notes = "No expected output defined - execution completed successfully"
            eval_run.validation_output = {"actual": actual_outputs}
            eval_run.evaluated_at = datetime.utcnow()
            db.commit()
            logger.info(f"Eval run {eval_run.id} marked as passed (no expected output)")
            return
        
        # Validate outputs
        passed, validation_output = self._validate_outputs(
            expected=eval_case.expected_output,
            actual=actual_outputs,
            validation_rules=eval_case.validation_rules
        )
        
        # Update eval run
        eval_run.passed = passed
        eval_run.validation_output = validation_output
        eval_run.validation_notes = validation_output.get('notes', 'Validated' if passed else 'Validation failed')
        eval_run.evaluated_at = datetime.utcnow()
        db.commit()
        
        logger.info(f"Eval run {eval_run.id} validated: {'PASSED' if passed else 'FAILED'}")
    
    def _validate_outputs(
        self,
        expected: Dict[str, Any],
        actual: List[Dict[str, Any]],
        validation_rules: Optional[Dict[str, Any]] = None
    ) -> tuple[bool, Dict[str, Any]]:
        """
        Validate actual outputs against expected outputs.
        
        Args:
            expected: Expected output dictionary
            actual: Actual output variables (array of dicts with variable_name, variable_value)
            validation_rules: Optional custom validation rules
            
        Returns:
            Tuple of (passed: bool, validation_output: dict)
        """
        # Convert actual outputs array to dict for easier comparison
        actual_dict = {}
        if isinstance(actual, list):
            for var in actual:
                if isinstance(var, dict) and 'variable_name' in var:
                    actual_dict[var['variable_name']] = var.get('variable_value')
        
        # Simple comparison for now - can be extended with more sophisticated rules
        validation_output = {
            "expected": expected,
            "actual": actual_dict,
            "differences": []
        }
        
        passed = True
        
        # Check each expected key/value
        for key, expected_value in expected.items():
            if key not in actual_dict:
                passed = False
                validation_output["differences"].append({
                    "field": key,
                    "expected": expected_value,
                    "actual": None,
                    "error": "Missing in output"
                })
            elif actual_dict[key] != expected_value:
                # Try loose comparison (str vs int, etc.)
                if str(actual_dict[key]) != str(expected_value):
                    passed = False
                    validation_output["differences"].append({
                        "field": key,
                        "expected": expected_value,
                        "actual": actual_dict[key],
                        "error": "Value mismatch"
                    })
        
        # Add notes
        if passed:
            validation_output["notes"] = "All expected outputs match"
        else:
            validation_output["notes"] = f"{len(validation_output['differences'])} field(s) did not match"
        
        return passed, validation_output


async def main():
    """Main entry point for running the monitor as a standalone process."""
    from dotenv import load_dotenv
    load_dotenv(project_root / ".env")
    
    logger.info("=" * 80)
    logger.info("EvalMonitor - Background Validation Daemon")
    logger.info("=" * 80)
    
    monitor = EvalMonitor()
    
    try:
        await monitor.start()
    except KeyboardInterrupt:
        logger.info("Received interrupt signal, shutting down...")
        monitor.stop()
    except Exception as e:
        logger.error(f"Error in monitor: {e}", exc_info=True)
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))

