"""
EvalRunner service for executing test cases.

Handles:
- Creating flow executions from eval cases
- Publishing execution messages to RabbitMQ
- Tracking eval runs
"""

import os
import sys
import asyncio
import logging
import json
import base64
from pathlib import Path
from typing import List, Dict, Any, Optional
from uuid import UUID, uuid4
from datetime import datetime

from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy import create_engine

# Add agentic-core to path
project_root = Path(__file__).parent.parent.parent.parent.resolve()
agentic_core_path_str = os.getenv("AGENTIC_CORE_PATH", "../agentic-core")
agentic_core_path = Path(agentic_core_path_str)
if not agentic_core_path.is_absolute():
    agentic_core_path = (project_root / agentic_core_path).resolve()
backend_path = agentic_core_path / "backend"
sys.path.insert(0, str(backend_path / "lib"))
sys.path.insert(0, str(backend_path))

# Import agentic-core models and messaging
from models.evaluation import <PERSON><PERSON><PERSON><PERSON>, <PERSON>l<PERSON>un, Eval<PERSON>atchRun
from database.users.models import User, Tenant
from database.agentic_objects.models import Flow, FlowVersion
from database.flow_execution.models import FlowExecution
from data_types.database.flow_executions import FlowResult

import aio_pika
from async_engine.core.models.messages import MessageHeader, StartFlowExecutionMessage
from async_engine.messaging.factory import MessagingBackendFactory
from async_engine.messaging.message_factory import MessageFactory
from async_engine.core.models.messaging_config import MessagingConfig
from async_engine.pools.pool_registry import PoolRegistry


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EvalRunner:
    """
    Service for running evaluation test cases.
    
    Self-contained service that creates flow executions and publishes to RabbitMQ.
    """
    
    def __init__(self, db: Session, enable_debugging: bool = False):
        """
        Initialize the eval runner with a database session.
        
        Args:
            db: Database session
            enable_debugging: Whether to enable debugging for flow executions (pauses execution for manual debugging).
                            Note: Logs are captured regardless via GraphQL Log Writer service.
        """
        self.db = db
        self.enable_debugging = enable_debugging
        self.messaging_backend = None
        self.rabbitmq_url = os.getenv("RABBITMQ_URL", "amqp://guest:guest@localhost:5672/")
        
        # Cache for system user and tenant
        self._system_user: Optional[User] = None
        self._system_tenant: Optional[Tenant] = None
        
    def _get_system_user(self) -> User:
        """Get or create system user."""
        if self._system_user is None:
            self._system_user = self.db.query(User).filter(User.email == "system@localhost").first()
            if not self._system_user:
                # Create system user if it doesn't exist
                tenant = self._get_system_tenant()
                self._system_user = User(
                    email="system@localhost",
                    name="System User",
                    tenant_id=tenant.id
                )
                self.db.add(self._system_user)
                self.db.commit()
                self.db.refresh(self._system_user)
        return self._system_user
    
    def _get_system_tenant(self) -> Tenant:
        """Get or create system tenant."""
        if self._system_tenant is None:
            self._system_tenant = self.db.query(Tenant).filter(Tenant.name == "system").first()
            if not self._system_tenant:
                self._system_tenant = Tenant(name="system")
                self.db.add(self._system_tenant)
                self.db.commit()
                self.db.refresh(self._system_tenant)
        return self._system_tenant
    
    async def _ensure_messaging(self):
        """Ensure messaging backend is initialized."""
        if self.messaging_backend is None:
            # Use Celery directly with proper queue configuration
            from celery import Celery
            from kombu import Queue, Exchange
            
            self.messaging_backend = Celery(
                'agentic',
                broker=self.rabbitmq_url,
                backend=None
            )
            
            # Configure queue with dead letter exchange (matching async workers)
            self.messaging_backend.conf.update(
                task_queues=[
                    Queue('agentic.async',
                          Exchange('agentic.queues', type='direct'),
                          routing_key='agentic.async',
                          queue_arguments={
                              'x-dead-letter-exchange': 'agentic.dlx',
                              'x-dead-letter-routing-key': 'agentic.async.dead_letter',
                              'x-consumer-timeout': *********
                          })
                ],
                task_default_exchange='agentic.queues',
                task_default_exchange_type='direct',
                task_default_routing_key='agentic.async',
            )
            logger.info("Messaging backend initialized (Celery with DLX)")
    
    def _create_flow_execution(
        self, 
        flow_version_id: UUID,
        input_variables: List[Dict[str, Any]]
    ) -> UUID:
        """
        Create a FlowExecution record in the database.
        
        Args:
            flow_version_id: ID of the flow version to execute
            input_variables: List of input variables (eval case format)
            
        Returns:
            UUID of the created flow execution
        """
        # Format variables for agentic-core
        formatted_variables = []
        for var in input_variables:
            if isinstance(var, dict):
                formatted_variables.append({
                    'variable_name': var.get('variable_name'),
                    'variable_value': var.get('variable_value'),
                    'variable_type': var.get('variable_type', 'String'),
                    'direction': 'Input',  # Input variables for flow execution
                    'step_id': None
                })
        
        # Get system user and tenant
        system_user = self._get_system_user()
        system_tenant = self._get_system_tenant()
        
        # Create flow execution
        flow_execution = FlowExecution(
            id=uuid4(),
            correlation_id=uuid4(),
            flow_version_id=flow_version_id,
            tenant_id=system_tenant.id,
            user_id=system_user.id,
            result=FlowResult.PENDING,
            variables=formatted_variables,
            debugging_enabled=self.enable_debugging  # False by default - logs still captured by GraphQL Log Writer
        )
        self.db.add(flow_execution)
        self.db.commit()
        self.db.refresh(flow_execution)
        
        logger.info(f"Created flow execution {flow_execution.id} (debugging: {self.enable_debugging}, logs via GraphQL)")
        return flow_execution.id
    
    async def _publish_execution_message(self, flow_execution_id: UUID):
        """
        Publish StartFlowExecutionMessage to RabbitMQ.
        
        Args:
            flow_execution_id: UUID of the flow execution to publish
        """
        await self._ensure_messaging()
        
        if not self.messaging_backend:
            raise RuntimeError("Messaging backend not initialized")
        
        # Get flow execution details
        flow_execution = self.db.query(FlowExecution).filter(
            FlowExecution.id == flow_execution_id
        ).first()
        
        if not flow_execution:
            raise ValueError(f"Flow execution {flow_execution_id} not found")
        
        # Create message
        from datetime import datetime, timezone
        message = StartFlowExecutionMessage(
            header=MessageHeader(
                correlation_id=str(flow_execution.correlation_id),
                flow_execution_id=str(flow_execution_id),
                tenant_id=str(flow_execution.tenant_id),
                user_id=str(flow_execution.user_id)
            ),
            message_type="start_flow_execution",
            timestamp=datetime.now(timezone.utc).isoformat(),
            flow_version_id=str(flow_execution.flow_version_id),
            enable_debugging=flow_execution.debugging_enabled
        )
        
        # Publish message using Celery's send_task
        # Use agentic.async queue for async worker
        # Pass message as a single positional argument, not unpacked kwargs
        self.messaging_backend.send_task(
            'async_engine.start_flow_execution',
            args=[message.to_dict()],
            queue='agentic.async',
            routing_key='agentic.async'
        )
        
        logger.info(f"Published execution message for {flow_execution_id}")
    
    async def run_eval_case(self, eval_case_id: UUID) -> UUID:
        """
        Run a single eval case.
        
        Steps:
        1. Get the eval case and workflow
        2. Create a flow execution with the eval case's input variables
        3. Create an eval_run record linking the case to the execution
        4. Publish the execution message to RabbitMQ
        
        Args:
            eval_case_id: UUID of the eval case to run
            
        Returns:
            UUID of the created eval_run
        """
        # Get eval case
        eval_case = self.db.query(EvalCase).filter(EvalCase.id == eval_case_id).first()
        if not eval_case:
            raise ValueError(f"Eval case {eval_case_id} not found")
        
        # Get workflow and latest version
        workflow = self.db.query(Flow).filter(Flow.id == eval_case.workflow_id).first()
        if not workflow:
            raise ValueError(f"Workflow {eval_case.workflow_id} not found")
        
        # Get latest flow version (most recently modified)
        flow_version = self.db.query(FlowVersion).filter(
            FlowVersion.flow_id == workflow.id
        ).order_by(FlowVersion.modified_date.desc()).first()
        
        if not flow_version:
            raise ValueError(f"No version found for workflow {workflow.id}")
        
        logger.info(f"Running eval case '{eval_case.name}' for workflow '{workflow.name}'")
        
        # Create flow execution
        flow_execution_id = self._create_flow_execution(
            flow_version_id=flow_version.id,
            input_variables=eval_case.input_variables
        )
        
        # Create eval_run record
        eval_run = EvalRun(
            eval_case_id=eval_case_id,
            flow_execution_id=flow_execution_id,
            batch_run_id=None  # Can be set later if part of a batch
        )
        self.db.add(eval_run)
        self.db.commit()
        self.db.refresh(eval_run)
        
        logger.info(f"Created eval run {eval_run.id}")
        
        # Publish execution message to RabbitMQ
        await self._publish_execution_message(flow_execution_id)
        logger.info(f"Published run message for execution {flow_execution_id}")
        
        return eval_run.id
    
    async def run_eval_cases(
        self, 
        eval_case_ids: List[UUID], 
        batch_name: Optional[str] = None
    ) -> List[UUID]:
        """
        Run multiple eval cases, optionally as a batch.
        
        Args:
            eval_case_ids: List of eval case IDs to run
            batch_name: Optional name for the batch
            
        Returns:
            List of created eval_run IDs
        """
        if not eval_case_ids:
            return []
        
        # Create batch run if batch_name is provided
        batch_run_id = None
        if batch_name:
            # Get workflow ID from first case
            first_case = self.db.query(EvalCase).filter(EvalCase.id == eval_case_ids[0]).first()
            if first_case:
                # Get system user
                system_user = self._get_system_user()
                batch_run = EvalBatchRun(
                    workflow_id=first_case.workflow_id,
                    name=batch_name,
                    started_by=system_user.id,
                    status='RUNNING'
                )
                self.db.add(batch_run)
                self.db.commit()
                self.db.refresh(batch_run)
                batch_run_id = batch_run.id
                logger.info(f"Created batch run {batch_run_id} with name '{batch_name}'")
        
        # Run each case
        eval_run_ids = []
        for case_id in eval_case_ids:
            try:
                eval_run_id = await self.run_eval_case(case_id)
                
                # Update batch_run_id if this is a batch
                if batch_run_id:
                    eval_run = self.db.query(EvalRun).filter(EvalRun.id == eval_run_id).first()
                    if eval_run:
                        eval_run.batch_run_id = batch_run_id
                        self.db.commit()
                
                eval_run_ids.append(eval_run_id)
            except Exception as e:
                logger.error(f"Error running eval case {case_id}: {e}", exc_info=True)
                # Continue with other cases
        
        # Update batch run status if all cases completed
        if batch_run_id and len(eval_run_ids) == len(eval_case_ids):
            batch_run = self.db.query(EvalBatchRun).filter(EvalBatchRun.id == batch_run_id).first()
            if batch_run:
                # Keep status as RUNNING - it will be updated by the monitor when all executions complete
                pass
        
        return eval_run_ids
    
    async def cleanup(self):
        """Clean up resources."""
        if self.messaging_backend:
            try:
                await self.messaging_backend.close()
                logger.info("Messaging backend closed")
            except Exception as e:
                logger.warning(f"Error closing messaging backend: {e}")
            self.messaging_backend = None


# Async helper function
async def run_eval_case_async(db: Session, eval_case_id: UUID) -> UUID:
    """
    Async helper to run a single eval case.
    
    Usage:
        import asyncio
        eval_run_id = asyncio.run(run_eval_case_async(db, eval_case_id))
    """
    runner = EvalRunner(db)
    try:
        return await runner.run_eval_case(eval_case_id)
    finally:
        await runner.cleanup()

