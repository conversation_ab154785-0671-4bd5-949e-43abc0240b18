"""
Workflow Specification Generator Service using OpenAI Agents SDK.
Uses a specialized 4-agent team to generate detailed specifications
for asset management report generation workflows.

The 4-agent team:
1. Requirements & Context Agent - Analyzes report goals, audience, and key questions
2. Data Strategy Agent - Designs file search queries, web research strategy, and data validation
3. Report Structure Agent - Plans markdown report structure with appropriate formatting
4. Specification Writer Agent - Compiles everything into a complete, actionable specification

This system is specifically designed for reports that:
- Read from IC memo files and weekly update files
- Use file search tools to extract information
- Optionally supplement with web research
- Generate markdown-formatted reports for asset managers
"""

import os
import json
import uuid
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, List
from threading import Lock
import asyncio
from pydantic import BaseModel

# Import OpenAI Agents SDK
from agents import Agent, Runner, ModelSettings, AgentOutputSchema, RunHooks
from agents.run_context import RunContextWrapper

from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import Pt
import re


# Task storage (in-memory for now, could move to Redis/DB later)
tasks: Dict[str, Dict[str, Any]] = {}
tasks_lock = Lock()


# Output models for structured agent responses
class RequirementsOutput(BaseModel):
    """Output from requirements & context agent."""
    report_goal: str  # Clear statement of what the report aims to accomplish
    target_audience: str  # Who will read and use this report
    key_questions: List[str]  # 3-5 key questions the report must answer
    information_needed: List[str]  # Types of information required (e.g., company performance, market trends)
    report_tone: str  # Professional, technical, executive summary, etc.


class DataStrategyOutput(BaseModel):
    """Output from data strategy agent."""
    workflow_inputs: List[Dict[str, str]]  # List of inputs with 'name', 'type', 'description', 'required'
    file_search_queries: List[Dict[str, Any]]  # List with 'purpose', 'prompt', 'file_types', 'expected_info'
    web_search_strategy: Dict[str, Any]  # 'use_web_search' (bool), 'search_topics' (list), 'purpose' (str)
    data_validation: List[str]  # Key validation steps to ensure data quality


class ReportStructureOutput(BaseModel):
    """Output from report structure agent."""
    report_title: str  # Suggested report title
    sections: List[Dict[str, str]]  # List with 'title', 'purpose', 'content_guidelines', 'markdown_elements'
    length_guidance: str  # Expected length and depth for each section
    formatting_notes: str  # Key formatting considerations for markdown


class SpecificationDocumentOutput(BaseModel):
    """Output from specification writer agent."""
    specification: str  # The complete specification document in markdown format


class StreamingHooks(RunHooks):
    """Custom hooks to stream agent responses in real-time."""
    
    def __init__(self, task_id: str, agent_name: str, add_message_callback):
        self.task_id = task_id
        self.agent_name = agent_name
        self.add_message_callback = add_message_callback
        self.streaming_message_id: Optional[str] = None
        
    async def on_llm_start(self, context: RunContextWrapper, agent: Agent, system_prompt: Optional[str], input_items: list) -> None:
        """Called when LLM starts processing."""
        # Create a streaming message placeholder
        self.streaming_message_id = f"stream_{self.task_id}_{id(agent)}"
        self.add_message_callback(self.task_id, "agent_stream_start", {
            "agent": self.agent_name,
            "stream_id": self.streaming_message_id
        })
        
    async def on_llm_end(self, context: RunContextWrapper, agent: Agent, response) -> None:
        """Called when LLM finishes - stream the response word by word."""
        # Extract the text content from the response
        content = ""
        
        try:
            # The response is a ModelResponse object with output array
            if hasattr(response, 'output') and len(response.output) > 0:
                output_message = response.output[0]
                # Get content from the output message
                if hasattr(output_message, 'content') and len(output_message.content) > 0:
                    # Content is a list of content items
                    for content_item in output_message.content:
                        if hasattr(content_item, 'text'):
                            content = content_item.text
                            break
        except Exception as e:
            print(f"Error extracting response content: {e}")
            print(f"Response type: {type(response)}")
            print(f"Response: {response}")
            content = ""
        
        if content:
            # Stream the response word by word
            words = content.split()
            for i, word in enumerate(words):
                # Send each word (or small chunks of words) incrementally
                partial_text = " ".join(words[:i+1])
                self.add_message_callback(self.task_id, "agent_stream", {
                    "agent": self.agent_name,
                    "stream_id": self.streaming_message_id,
                    "content": partial_text,
                    "is_complete": i == len(words) - 1
                })
                # Small delay to simulate streaming (adjust for desired speed)
                await asyncio.sleep(0.02)  # 50 words per second
        
        # Mark stream as complete
        self.add_message_callback(self.task_id, "agent_stream_end", {
            "agent": self.agent_name,
            "stream_id": self.streaming_message_id,
            "final_content": content
        })


class WorkflowSpecGenerator:
    """Generates workflow specifications for asset management report generation using OpenAI Agents SDK."""
    
    def __init__(self):
        self.model = "gpt-5"
        self.model_settings = ModelSettings(
            model="gpt-5",
            reasoning_effort="minimal",
            extra_body={
                "service_tier": "priority"
            }
        )
        
        # Log configuration
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("WARNING: OPENAI_API_KEY not set!")
        else:
            print(f"OpenAI Agents SDK initialized with model: {self.model}")
        
        # Initialize specialized agents
        self._init_agents()
        
    def _init_agents(self):
        """Initialize the 4-agent team for report workflow specification generation."""
        
        # Agent 1: Requirements & Context Agent
        self.requirements_agent = Agent(
            name="Requirements & Context Agent",
            handoff_description="Analyzes the report requirement and defines goals and context",
            instructions="""You are an expert at understanding asset management reporting needs.

Your task is to analyze the report requirement and define:

1. REPORT GOAL: What is the primary purpose of this report? What decision or insight should it enable?
   Be specific and focused. Example: "Provide investment committee with quarterly performance analysis 
   of portfolio company X to inform hold/sell decision."

2. TARGET AUDIENCE: Who will read this report? What is their level of expertise and what do they care about?
   Be specific about roles (e.g., "Portfolio managers", "Investment committee members", "LPs").

3. KEY QUESTIONS: What are the 3-5 critical questions this report must answer?
   These should be specific, actionable questions. Examples:
   - "How has the company performed vs. initial investment thesis?"
   - "What are the key risk factors that could impact returns?"
   - "What market trends are affecting the company's prospects?"

4. INFORMATION NEEDED: What types of information will be required to answer these questions?
   Think broadly: financial metrics, operational KPIs, market analysis, competitive positioning, etc.

5. REPORT TONE: What tone and style should the report use?
   Consider: executive summary style, detailed analytical, forward-looking strategic, etc.

Your output should be crisp and focused on what really matters for this specific report.""",
            output_type=AgentOutputSchema(RequirementsOutput, strict_json_schema=False),
            model_settings=self.model_settings
        )
        
        # Agent 2: Data Strategy Agent
        self.data_strategy_agent = Agent(
            name="Data Strategy Agent",
            handoff_description="Defines how to gather information from files and web sources",
            instructions="""You are an expert at designing data gathering strategies for asset management reports.

CONTEXT: The workflow has access to:
- ~10 IC (Investment Committee) memo files containing investment theses, deal details, and initial analysis
- ~40 weekly update files containing ongoing performance updates, metrics, and developments
- A file search tool that takes a prompt and list of filenames, returns relevant extracted information
- Web search capability for supplementary market research and current information

Your task is to define a complete data strategy:

1. WORKFLOW INPUTS: What inputs should the workflow accept? Each input should have:
   - name: descriptive name (e.g., "company_name", "memo_filename", "date_range")
   - type: data type (e.g., "string", "list", "date_range")
   - description: what this input is for and how it will be used
   - required: "yes" or "no"
   
   Common patterns:
   - For company-specific reports: company name or memo filename
   - For time-based reports: date ranges for weekly updates
   - For portfolio reports: list of companies or "all"

2. FILE SEARCH QUERIES: Design 3-6 specific file search queries. For each query, specify:
   - purpose: what information you're trying to extract
   - prompt: the actual prompt to use with the file search tool (be specific and directive)
   - file_types: which files to search ("IC_memos", "weekly_updates", or "both")
   - expected_info: what kind of information you expect to get back
   
   Make prompts SPECIFIC and ACTIONABLE. Examples:
   - "Extract all financial metrics including revenue, EBITDA, and growth rates with dates"
   - "Identify key risks and challenges mentioned across all updates"
   - "Summarize product development milestones and launch timelines"

3. WEB SEARCH STRATEGY: Define if and how web search should be used:
   - use_web_search: true/false - should web search be used?
   - search_topics: list of specific topics to research (if applicable)
   - purpose: why web search is needed and what it adds to the report
   
   Use web search for: current market data, recent news, industry trends, competitor analysis
   Don't use web search for: internal company data (use files instead)

4. DATA VALIDATION: List 3-5 key validation steps to ensure data quality:
   - Check for missing required information
   - Validate date ranges and time periods
   - Ensure numerical data is reasonable
   - Verify file search returned substantive results

Be strategic and practical. Design a data gathering approach that will provide complete, 
high-quality information for the report.""",
            output_type=AgentOutputSchema(DataStrategyOutput, strict_json_schema=False),
            model_settings=self.model_settings
        )
        
        # Agent 3: Report Structure Agent
        self.structure_agent = Agent(
            name="Report Structure Agent",
            handoff_description="Designs the markdown report structure and formatting",
            instructions="""You are an expert at designing report structures for asset management reports.

The report will be written in MARKDOWN format. Your task is to recommend an optimal structure
for this specific report based on its goals, audience, and the questions it needs to answer.

CONTEXT:
You've been given information about the report's purpose, target audience, key questions to answer,
and the tone it should have. Use this to design a structure that best serves these needs.

AVAILABLE MARKDOWN ELEMENTS:
- Headers (# H1, ## H2, ### H3, etc.) for hierarchical organization
- Bold (**text**) and italic (*text*) for emphasis
- Bullet lists and numbered lists for organized information
- Tables (for metrics, comparisons, structured data)
- Blockquotes (> text) for highlights, key takeaways, or important callouts
- Horizontal rules (---) for visual section breaks
- Code blocks (```) for data, formulas, or technical content

YOUR RECOMMENDATIONS:

1. REPORT TITLE: 
   Suggest a clear, compelling title that accurately reflects the report's purpose.

2. REPORT SECTIONS:
   Design the optimal section structure for THIS specific report. Think about:
   - What sequence of information makes the most sense?
   - How should insights build upon each other?
   - What does the audience need to see first vs. later?
   
   For each section you recommend, specify:
   - title: clear section heading
   - purpose: what this section accomplishes and why it's needed
   - content_guidelines: what to include, level of detail, specific points to address
   - markdown_elements: which formatting to use (tables for X, lists for Y, blockquotes for Z, etc.)
   
   Common patterns for asset management reports (use as inspiration, not requirements):
   - Executive summaries for senior audiences
   - Performance/metrics sections with tables and trends
   - Deep-dive analysis sections
   - Risk assessments
   - Market/competitive context
   - Forward-looking recommendations or outlook
   
   But adapt to what THIS report needs - you might need fewer sections, different sections,
   or a completely different organization.

3. LENGTH & DEPTH GUIDANCE:
   For each section, provide practical guidance on scope and depth.
   Be specific: "2-3 paragraphs plus a metrics table" or "500-750 words covering X, Y, and Z"
   Consider the audience - executives need concise summaries, analysts might need more detail.

4. FORMATTING STRATEGY:
   Provide overall formatting guidance for the report:
   - How to effectively use tables (what data deserves tables vs. inline mentions?)
   - When to use blockquotes (key findings? Risk alerts? Recommendations?)
   - How to balance narrative text vs. structured data
   - Visual hierarchy considerations (when to use H2 vs H3, etc.)
   - Any other formatting patterns that would enhance readability

Think strategically about what structure will best serve the report's goals and audience.
Be thoughtful and specific in your recommendations.""",
            output_type=AgentOutputSchema(ReportStructureOutput, strict_json_schema=False),
            model_settings=self.model_settings
        )
        
        # Agent 4: Specification Writer Agent
        self.specification_writer_agent = Agent(
            name="Specification Writer Agent",
            handoff_description="Compiles all information into a complete workflow specification",
            instructions="""You are a technical writer specializing in workflow specifications for asset management reports.

Your task is to write a complete, actionable workflow specification document that describes
how to generate the asset management report. This specification will be used by an execution
team (human or AI) to actually create the report.

You have been provided with:
- Report requirements and context (goal, audience, key questions)
- Data gathering strategy (inputs, file searches, web research)
- Report structure recommendations (sections, formatting)

Your job is to synthesize ALL of this information into a coherent, well-organized specification
document that tells someone EXACTLY how to generate this report.

CRITICAL - OUTCOME-ORIENTED LANGUAGE:
Write the specification in terms of OUTCOMES and GOALS, not implementation details or tool calls.
Focus on WHAT needs to be accomplished, not HOW to call specific tools.

GOOD (outcome-oriented):
- "Search through all weekly update memos to find revenue, EBITDA, and growth rate metrics with dates"
- "Gather information about key risks and challenges from company documentation"
- "Research current market trends affecting the industry"

BAD (tool-implementation details):
- "Call the file search tool with prompt 'Extract all financial metrics...'"
- "Use the web search API to query for..."
- "Execute a file search against IC_memos with parameters..."

The specification should read like instructions to a knowledgeable analyst, describing
what information to gather and what to write, without exposing the underlying technical
implementation or specific tools being used.

IMPORTANT - STRUCTURE FLEXIBILITY:
You have COMPLETE FREEDOM to organize the specification however you think is best for this
specific report. Consider what structure would be clearest and most useful. Think about:
- What information does someone need first?
- What's the logical flow of information?
- How can you make this as easy to follow as possible?
- What level of detail is appropriate for each aspect?

You might organize it by workflow phases, by information types, by chronological steps,
or any other structure that makes sense. Be creative and thoughtful.

CONTENT REQUIREMENTS:
Your specification must be comprehensive and actionable. Make sure to cover:
- WHAT the workflow produces and WHY (purpose, audience, goals)
- INPUTS the workflow needs (what parameters, files, or data to start with)
- WHAT information to gather (describe data needs in outcome-oriented terms)
- WHAT the report should contain (structure, sections, content for each part)
- HOW to write it (tone, style, formatting, markdown usage)
- WHEN it's done well (success criteria, quality standards)

Be SPECIFIC and PRACTICAL:
- Describe information needs clearly but in business/analyst terms
- Describe report sections with enough detail that someone knows what to write
- Be explicit about markdown formatting (when to use tables, lists, blockquotes, etc.)
- Provide concrete examples where helpful
- Think about edge cases and potential challenges

FORMAT:
Write in MARKDOWN format. Use:
- Headers (#, ##, ###) to organize sections
- Tables for structured information (like workflow inputs)
- Bullet lists for items and steps
- **Bold** for emphasis on key points
- `Code formatting` for technical terms only when appropriate
- Blockquotes (>) for important callouts or examples

The specification should be thorough but readable - aim for clarity and completeness.
Trust your judgment on the best way to organize and present this information.""",
            output_type=AgentOutputSchema(SpecificationDocumentOutput, strict_json_schema=False),
            model_settings=self.model_settings
        )
        
    def create_task(self, workflow_request: str) -> str:
        """Create a new task and return its ID."""
        task_id = str(uuid.uuid4())
        
        with tasks_lock:
            tasks[task_id] = {
                "id": task_id,
                "status": "processing",
                "workflow_request": workflow_request,
                "messages": [],
                "progress": 0,
                "created_at": datetime.utcnow().isoformat(),
                "completed_at": None,
                "error": None,
                "document_path": None
            }
        
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get the current status of a task."""
        with tasks_lock:
            return tasks.get(task_id)
    
    def add_message(self, task_id: str, message_type: str, content):
        """Add a message to the task's message log."""
        with tasks_lock:
            if task_id in tasks:
                # Handle both string content and dict content (for streaming)
                if isinstance(content, str):
                    tasks[task_id]["messages"].append({
                        "type": message_type,
                        "content": content,
                        "timestamp": datetime.utcnow().isoformat()
                    })
                else:
                    # For streaming messages, content is a dict with agent, stream_id, etc.
                    tasks[task_id]["messages"].append({
                        "type": message_type,
                        **content,
                        "timestamp": datetime.utcnow().isoformat()
                    })
    
    def update_progress(self, task_id: str, progress: int):
        """Update task progress (0-100)."""
        with tasks_lock:
            if task_id in tasks:
                tasks[task_id]["progress"] = progress
    
    def set_error(self, task_id: str, error: str):
        """Set task error status."""
        with tasks_lock:
            if task_id in tasks:
                tasks[task_id]["status"] = "error"
                tasks[task_id]["error"] = error
                tasks[task_id]["completed_at"] = datetime.utcnow().isoformat()
    
    def set_complete(self, task_id: str, document_path: str):
        """Mark task as complete."""
        with tasks_lock:
            if task_id in tasks:
                tasks[task_id]["status"] = "completed"
                tasks[task_id]["document_path"] = document_path
                tasks[task_id]["progress"] = 100
                tasks[task_id]["completed_at"] = datetime.utcnow().isoformat()
    
    async def _run_agent_with_tracking(
        self, 
        task_id: str, 
        agent: Agent, 
        input_text: str,
        agent_name: str,
        progress_value: int
    ) -> Any:
        """Run an agent and track progress with messages to frontend."""
        self.add_message(task_id, "thinking", f"🤖 Handoff to {agent_name}...")
        self.update_progress(task_id, progress_value)
        
        try:
            # Add message that agent is processing
            self.add_message(task_id, "info", f"💭 {agent_name} is analyzing the request...")
            
            # Create streaming hooks to capture agent output
            streaming_hooks = StreamingHooks(
                task_id=task_id,
                agent_name=agent_name,
                add_message_callback=self.add_message
            )
            
            # Run the agent with streaming hooks
            result = await asyncio.wait_for(
                Runner.run(agent, input_text, hooks=streaming_hooks),
                timeout=120.0
            )
            
            self.add_message(
                task_id, 
                "success", 
                f"✅ {agent_name} completed analysis successfully"
            )
            
            return result
            
        except asyncio.TimeoutError:
            error_msg = f"⏰ {agent_name} timed out after 120 seconds"
            self.add_message(task_id, "error", error_msg)
            raise
        except Exception as e:
            error_msg = f"❌ {agent_name} error: {str(e)}"
            self.add_message(task_id, "error", error_msg)
            import traceback
            # Log full traceback for debugging
            print(f"Full error traceback for {agent_name}:")
            traceback.print_exc()
            raise
    
    async def _generate_specification_async(self, task_id: str, workflow_request: str):
        """Generate workflow specification using 4-agent workflow (async)."""
        try:
            self.add_message(task_id, "info", "🚀 Starting specification generation")
            self.add_message(task_id, "info", f"📝 Request: {workflow_request[:100]}{'...' if len(workflow_request) > 100 else ''}")
            self.update_progress(task_id, 10)
            
            # Step 1: Requirements & Context Analysis
            self.add_message(task_id, "thinking", "📊 Step 1/4: Analyzing report requirements and context...")
            requirements_result = await self._run_agent_with_tracking(
                task_id,
                self.requirements_agent,
                f"""Analyze this report requirement for an asset management company:

{workflow_request}

Define the report goal, target audience, key questions to answer, information needed, and appropriate tone.""",
                "Requirements & Context Agent",
                25
            )
            requirements_output = requirements_result.final_output_as(RequirementsOutput)
            
            # Show what was extracted
            self.add_message(task_id, "success", f"🎯 Identified report goal: {requirements_output.report_goal[:100]}...")
            self.add_message(task_id, "success", f"👥 Target audience: {requirements_output.target_audience}")
            self.add_message(task_id, "success", f"❓ Identified {len(requirements_output.key_questions)} key questions to answer")
            
            # Step 2: Data Strategy - How to gather information
            self.add_message(task_id, "thinking", "🗂️ Step 2/4: Designing data gathering strategy...")
            data_strategy_context = f"""
Report Requirement: {workflow_request}

CONTEXT:
- Report Goal: {requirements_output.report_goal}
- Target Audience: {requirements_output.target_audience}
- Key Questions to Answer:
{chr(10).join(f"  - {q}" for q in requirements_output.key_questions)}
- Information Needed:
{chr(10).join(f"  - {i}" for i in requirements_output.information_needed)}

AVAILABLE DATA SOURCES:
- ~10 IC memo files (investment theses, deal details, initial analysis)
- ~40 weekly update files (ongoing performance, metrics, developments)
- File search tool (takes prompt + file list, returns extracted info)
- Web search capability

Design a complete data gathering strategy:
1. What inputs should the workflow accept?
2. What specific file search queries should be executed?
3. Should web search be used, and for what?
4. What data validation steps are needed?
"""
            data_strategy_result = await self._run_agent_with_tracking(
                task_id,
                self.data_strategy_agent,
                data_strategy_context,
                "Data Strategy Agent",
                50
            )
            data_strategy_output = data_strategy_result.final_output_as(DataStrategyOutput)
            
            # Show what was designed
            self.add_message(task_id, "success", f"📥 Defined {len(data_strategy_output.workflow_inputs)} workflow inputs")
            self.add_message(task_id, "success", f"🔍 Designed {len(data_strategy_output.file_search_queries)} file search queries")
            if data_strategy_output.web_search_strategy.get('use_web_search'):
                self.add_message(task_id, "success", "🌐 Web search enabled for market/trend research")
            
            # Step 3: Report Structure - Design markdown report structure
            self.add_message(task_id, "thinking", "📐 Step 3/4: Designing report structure and formatting...")
            structure_context = f"""
Report Requirement: {workflow_request}

CONTEXT:
- Report Goal: {requirements_output.report_goal}
- Target Audience: {requirements_output.target_audience}
- Report Tone: {requirements_output.report_tone}
- Key Questions:
{chr(10).join(f"  - {q}" for q in requirements_output.key_questions)}

Design a markdown report structure with 4-7 sections.
For each section, specify: title, purpose, content guidelines, markdown elements to use, and length guidance.
Consider appropriate markdown formatting: headers, tables, lists, blockquotes, etc.
"""
            structure_result = await self._run_agent_with_tracking(
                task_id,
                self.structure_agent,
                structure_context,
                "Report Structure Agent",
                75
            )
            structure_output = structure_result.final_output_as(ReportStructureOutput)
            
            # Show what was designed
            self.add_message(task_id, "success", f"📋 Report title: {structure_output.report_title}")
            self.add_message(task_id, "success", f"📑 Designed {len(structure_output.sections)} report sections")
            self.add_message(task_id, "success", "✏️ Defined formatting and style guidelines")
            
            # Step 4: Specification Writer - Compile everything into final spec
            self.add_message(task_id, "thinking", "📝 Step 4/4: Compiling complete specification document...")
            spec_context = f"""
Report Requirement: {workflow_request}

REQUIREMENTS & CONTEXT:
Report Goal: {requirements_output.report_goal}
Target Audience: {requirements_output.target_audience}
Key Questions: {json.dumps(requirements_output.key_questions, indent=2)}
Information Needed: {json.dumps(requirements_output.information_needed, indent=2)}
Report Tone: {requirements_output.report_tone}

DATA STRATEGY:
Workflow Inputs: {json.dumps(data_strategy_output.workflow_inputs, indent=2)}
File Search Queries: {json.dumps(data_strategy_output.file_search_queries, indent=2)}
Web Search Strategy: {json.dumps(data_strategy_output.web_search_strategy, indent=2)}
Data Validation: {json.dumps(data_strategy_output.data_validation, indent=2)}

REPORT STRUCTURE:
Report Title: {structure_output.report_title}
Sections: {json.dumps(structure_output.sections, indent=2)}
Length Guidance: {structure_output.length_guidance}
Formatting Notes: {structure_output.formatting_notes}

Write a complete workflow specification document in markdown format that describes how to generate this report.
Include: Overview, Workflow Inputs, Information Gathering (file search, web research, validation), 
Report Structure (section by section), Writing Guidelines, and Success Criteria.

Be specific and actionable - this will guide the actual report generation.
"""
            spec_result = await self._run_agent_with_tracking(
                task_id,
                self.specification_writer_agent,
                spec_context,
                "Specification Writer Agent",
                95
            )
            
            spec_output = spec_result.final_output_as(SpecificationDocumentOutput)
            
            self.add_message(task_id, "success", "📄 Specification document compiled successfully")
            self.add_message(task_id, "info", "📦 Generating Word document...")
            
            # Generate Word document from the specification
            spec_data = {
                "workflow_request": workflow_request,
                "report_goal": requirements_output.report_goal,
                "target_audience": requirements_output.target_audience,
                "key_questions": requirements_output.key_questions,
                "information_needed": requirements_output.information_needed,
                "report_tone": requirements_output.report_tone,
                "workflow_inputs": data_strategy_output.workflow_inputs,
                "file_search_queries": data_strategy_output.file_search_queries,
                "web_search_strategy": data_strategy_output.web_search_strategy,
                "data_validation": data_strategy_output.data_validation,
                "report_title": structure_output.report_title,
                "sections": structure_output.sections,
                "length_guidance": structure_output.length_guidance,
                "formatting_notes": structure_output.formatting_notes,
                "specification_document": spec_output.specification
            }
            
            document_path = self._generate_word_document(task_id, workflow_request, spec_data)
            
            self.add_message(task_id, "success", "✅ Word document generated successfully!")
            self.add_message(task_id, "success", "🎉 Workflow specification complete and ready for download!")
            self.set_complete(task_id, document_path)
            
        except Exception as e:
            error_msg = f"Error generating specification: {str(e)}"
            self.add_message(task_id, "error", error_msg)
            self.set_error(task_id, error_msg)
            import traceback
            traceback.print_exc()
    
    def generate_specification(self, task_id: str, workflow_request: str):
        """Generate workflow specification using agent orchestration (sync wrapper)."""
        # Run the async function - create a new event loop for this thread
        try:
            # Check if there's a running event loop in the current thread
            try:
                asyncio.get_running_loop()
                # If we're already in a running loop, we can't use asyncio.run()
                # This shouldn't happen when called from a background thread
                error_msg = "Cannot run in existing event loop context"
                self.add_message(task_id, "error", error_msg)
                self.set_error(task_id, error_msg)
                return
            except RuntimeError:
                # No running loop - this is the expected case for background threads
                # Create a new event loop and run the async function
                asyncio.run(self._generate_specification_async(task_id, workflow_request))
        except Exception as e:
            error_msg = f"Error in agent orchestration: {str(e)}"
            self.add_message(task_id, "error", error_msg)
            self.set_error(task_id, error_msg)
    
    def _parse_markdown_to_docx(self, doc: Any, markdown_text: str):
        """Parse markdown text and add it to a Word document with proper formatting."""
        lines = markdown_text.split('\n')
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # Check for tables (lines starting with |)
            if line.strip().startswith('|'):
                # Collect all table lines
                table_lines = []
                while i < len(lines) and lines[i].strip().startswith('|'):
                    table_lines.append(lines[i].strip())
                    i += 1
                
                # Parse and create table
                self._create_table_from_markdown(doc, table_lines)
                continue
            
            # Check for headers
            if line.startswith('#### '):
                doc.add_heading(line[5:].strip(), level=4)
            elif line.startswith('### '):
                doc.add_heading(line[4:].strip(), level=3)
            elif line.startswith('## '):
                doc.add_heading(line[3:].strip(), level=2)
            elif line.startswith('# '):
                doc.add_heading(line[2:].strip(), level=1)
            
            # Check for horizontal rule
            elif line.strip() in ['---', '***', '___']:
                # Add a horizontal line (using a paragraph with bottom border)
                p = doc.add_paragraph()
                p_format = p.paragraph_format
                p_format.space_before = Pt(6)
                p_format.space_after = Pt(6)
            
            # Check for blockquotes
            elif line.strip().startswith('>'):
                quote_lines = []
                while i < len(lines) and lines[i].strip().startswith('>'):
                    quote_lines.append(lines[i].strip()[1:].strip())
                    i += 1
                
                # Create blockquote paragraph with special formatting
                quote_text = ' '.join(quote_lines)
                p = doc.add_paragraph()
                self._add_formatted_text(p, quote_text)
                p.style = 'Intense Quote'
                continue
            
            # Check for code blocks
            elif line.strip().startswith('```'):
                code_lines = []
                i += 1  # Skip opening ```
                while i < len(lines) and not lines[i].strip().startswith('```'):
                    code_lines.append(lines[i])
                    i += 1
                if i < len(lines):
                    i += 1  # Skip closing ```
                
                # Add code block with monospace font
                for code_line in code_lines:
                    p = doc.add_paragraph(code_line)
                    run = p.runs[0] if p.runs else p.add_run()
                    run.font.name = 'Courier New'
                    run.font.size = Pt(9)
                continue
            
            # Check for numbered lists
            elif re.match(r'^\d+\.\s', line.strip()):
                list_text = re.sub(r'^\d+\.\s+', '', line.strip())
                p = doc.add_paragraph(style='List Number')
                self._add_formatted_text(p, list_text)
            
            # Check for bullet lists
            elif line.strip().startswith('- ') or line.strip().startswith('* ') or line.strip().startswith('+ '):
                list_text = line.strip()[2:].strip()
                p = doc.add_paragraph(style='List Bullet')
                self._add_formatted_text(p, list_text)
            
            # Empty line
            elif not line.strip():
                doc.add_paragraph()
            
            # Regular paragraph with inline formatting
            else:
                p = doc.add_paragraph()
                self._add_formatted_text(p, line)
            
            i += 1
    
    def _add_formatted_text(self, paragraph, text: str):
        """Add text to a paragraph with inline markdown formatting (bold, italic, code)."""
        # Pattern to match markdown formatting: ***text***, **text**, *text*, `code`
        # We'll process them in order of precedence
        
        pos = 0
        while pos < len(text):
            # Look for next formatting marker
            next_bold_italic = text.find('***', pos)
            next_bold = text.find('**', pos)
            next_italic = text.find('*', pos)
            next_code = text.find('`', pos)
            
            # Find the earliest marker
            markers = []
            if next_bold_italic != -1:
                markers.append((next_bold_italic, '***'))
            if next_bold != -1 and next_bold != next_bold_italic:
                markers.append((next_bold, '**'))
            if next_italic != -1 and next_italic not in [next_bold_italic, next_bold]:
                markers.append((next_italic, '*'))
            if next_code != -1:
                markers.append((next_code, '`'))
            
            if not markers:
                # No more formatting, add rest as plain text
                if pos < len(text):
                    paragraph.add_run(text[pos:])
                break
            
            # Get earliest marker
            markers.sort()
            marker_pos, marker = markers[0]
            
            # Add text before marker as plain text
            if marker_pos > pos:
                paragraph.add_run(text[pos:marker_pos])
            
            # Find closing marker
            close_pos = text.find(marker, marker_pos + len(marker))
            if close_pos == -1:
                # No closing marker, treat as plain text
                paragraph.add_run(text[marker_pos:])
                break
            
            # Extract formatted text
            formatted_text = text[marker_pos + len(marker):close_pos]
            
            # Add formatted run
            run = paragraph.add_run(formatted_text)
            if marker == '***':
                run.bold = True
                run.italic = True
            elif marker == '**':
                run.bold = True
            elif marker == '*':
                run.italic = True
            elif marker == '`':
                run.font.name = 'Courier New'
                run.font.size = Pt(9)
            
            pos = close_pos + len(marker)
    
    def _create_table_from_markdown(self, doc: Any, table_lines: List[str]):
        """Create a Word table from markdown table lines."""
        if not table_lines:
            return
        
        # Parse table structure
        rows = []
        for line in table_lines:
            # Skip separator lines (e.g., |---|---|)
            if re.match(r'^\|[\s\-:]+\|$', line):
                continue
            
            # Split by | and clean up
            cells = [cell.strip() for cell in line.split('|')]
            # Remove empty first/last cells (from leading/trailing |)
            cells = [c for c in cells if c]
            if cells:
                rows.append(cells)
        
        if not rows:
            return
        
        # Create table
        num_rows = len(rows)
        num_cols = max(len(row) for row in rows)
        
        table = doc.add_table(rows=num_rows, cols=num_cols)
        table.style = 'Light Grid Accent 1'
        
        # Fill table cells
        for i, row_data in enumerate(rows):
            row_cells = table.rows[i].cells
            for j, cell_data in enumerate(row_data):
                if j < len(row_cells):
                    # Clear the cell and add formatted text
                    cell = row_cells[j]
                    cell.text = ''
                    p = cell.paragraphs[0] if cell.paragraphs else cell.add_paragraph()
                    self._add_formatted_text(p, cell_data)
                    
                    # Make header row bold
                    if i == 0:
                        for run in p.runs:
                            run.bold = True
        
        # Add spacing after table
        doc.add_paragraph()
    
    def _generate_word_document(self, task_id: str, workflow_request: str, spec_data: Dict[str, Any]) -> str:
        """Generate a Word document from the workflow specification data."""
        # Create temp directory if it doesn't exist
        temp_dir = Path(__file__).parent.parent.parent.parent / "temp" / "workflow_specs"
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        # Create document
        doc = Document()
        
        # Add title
        report_title = spec_data.get("report_title", "Workflow Specification")
        title = doc.add_heading(f'Workflow Specification: {report_title}', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add metadata
        doc.add_paragraph(f"Generated: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}")
        doc.add_paragraph(f"Requirement: {workflow_request}")
        doc.add_paragraph()
        
        # Helper function to add list items
        def add_list_items(items, style='List Bullet'):
            """Add list items handling various data types."""
            if not items:
                return
            for item in items:
                if isinstance(item, dict):
                    # Handle dict items - try common keys
                    if 'name' in item and 'description' in item:
                        text = f"{item['name']}: {item['description']}"
                    elif 'title' in item and 'purpose' in item:
                        text = f"{item['title']} - {item['purpose']}"
                    else:
                        text = str(item)
                    doc.add_paragraph(str(text), style=style)
                else:
                    doc.add_paragraph(str(item), style=style)
        
        # If we have the complete specification document (from agent 4), use it
        if "specification_document" in spec_data:
            # Parse the markdown specification and convert to Word format
            spec_text = spec_data["specification_document"]
            
            # Enhanced markdown-to-word conversion
            self._parse_markdown_to_docx(doc, spec_text)
        else:
            # Fallback: construct document from individual components
            doc.add_heading('1. Overview', 1)
            if "report_goal" in spec_data:
                p = doc.add_paragraph()
                p.add_run("Report Goal: ").bold = True
                doc.add_paragraph(spec_data["report_goal"])
            
            if "target_audience" in spec_data:
                p = doc.add_paragraph()
                p.add_run("Target Audience: ").bold = True
                doc.add_paragraph(spec_data["target_audience"])
            
            if "key_questions" in spec_data:
                p = doc.add_paragraph()
                p.add_run("Key Questions: ").bold = True
                add_list_items(spec_data["key_questions"])
            
            doc.add_paragraph()
            
            # Workflow Inputs
            doc.add_heading('2. Workflow Inputs', 1)
            if "workflow_inputs" in spec_data:
                add_list_items(spec_data["workflow_inputs"])
            
            doc.add_paragraph()
            
            # Information Gathering
            doc.add_heading('3. Information Gathering', 1)
            
            if "file_search_queries" in spec_data:
                doc.add_heading('File Search Queries', 2)
                add_list_items(spec_data["file_search_queries"])
            
            if "web_search_strategy" in spec_data:
                doc.add_heading('Web Search Strategy', 2)
                ws = spec_data["web_search_strategy"]
                if isinstance(ws, dict):
                    doc.add_paragraph(f"Use Web Search: {ws.get('use_web_search', False)}")
                    if ws.get('search_topics'):
                        doc.add_paragraph("Topics:")
                        add_list_items(ws.get('search_topics', []))
                    if ws.get('purpose'):
                        doc.add_paragraph(f"Purpose: {ws['purpose']}")
            
            if "data_validation" in spec_data:
                doc.add_heading('Data Validation', 2)
                add_list_items(spec_data["data_validation"])
            
            doc.add_paragraph()
            
            # Report Structure
            doc.add_heading('4. Report Structure', 1)
            if "sections" in spec_data:
                for section in spec_data["sections"]:
                    if isinstance(section, dict):
                        title = section.get('title', 'Section')
                        doc.add_heading(title, 2)
                        if section.get('purpose'):
                            p = doc.add_paragraph()
                            p.add_run("Purpose: ").bold = True
                            p.add_run(section['purpose'])
                        if section.get('content_guidelines'):
                            p = doc.add_paragraph()
                            p.add_run("Content: ").bold = True
                            p.add_run(section['content_guidelines'])
            
            if "length_guidance" in spec_data:
                p = doc.add_paragraph()
                p.add_run("Length Guidance: ").bold = True
                doc.add_paragraph(spec_data["length_guidance"])
            
            doc.add_paragraph()
            
            # Writing Guidelines
            doc.add_heading('5. Writing Guidelines', 1)
            if "report_tone" in spec_data:
                p = doc.add_paragraph()
                p.add_run("Tone: ").bold = True
                doc.add_paragraph(spec_data["report_tone"])
            
            if "formatting_notes" in spec_data:
                p = doc.add_paragraph()
                p.add_run("Formatting Notes: ").bold = True
                doc.add_paragraph(spec_data["formatting_notes"])
        
        # Save document
        filename = f"workflow_spec_{task_id}.docx"
        filepath = temp_dir / filename
        doc.save(str(filepath))
        
        return str(filepath)


# Global generator instance
_generator = None

def get_generator() -> WorkflowSpecGenerator:
    """Get or create the global generator instance."""
    global _generator
    if _generator is None:
        _generator = WorkflowSpecGenerator()
    return _generator
