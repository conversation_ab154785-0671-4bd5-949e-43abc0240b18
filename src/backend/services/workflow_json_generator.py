"""
Workflow JSON Generator Service using OpenAI Agents SDK.

This service takes a specification document (Word doc) and generates a complete
workflow JSON following <PERSON><PERSON>'s new team-based guidelines.

The generator uses OpenAI Agents SDK with 4 sequential agents:
1. Specification Analyzer - Analyzes the spec and identifies requirements
2. Workflow Architect - Designs workflow structure with team-based steps
3. Team Designer - Designs teams following coordinator+specialist pattern
4. JSON Compiler - Compiles final JSON following Axon guidelines

The generated AXON workflows will have:
- All steps are "team" type (no delegates)
- First agent in each team is a coordinator/leader
- Coordinators use gpt-5-mini (routing is simple)
- Specialists use gpt-5 or gpt-5-mini based on task complexity
- Coordinator uses {roles}, {history}, {participants} for routing
- Specialists with focused, procedural skills
- Clear termination patterns (TERMINATE)
- Proper use of save_variable for outputs
"""

import os
import json
import uuid
import asyncio
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, List
from threading import Lock
from docx import Document
from pydantic import BaseModel

# Import OpenAI Agents SDK
from agents import Agent, Runner, ModelSettings, AgentOutputSchema


# Task storage (in-memory for now, could move to Redis/DB later)
generation_tasks: Dict[str, Dict[str, Any]] = {}
tasks_lock = Lock()


# Output models for structured agent responses
class SpecificationAnalysisOutput(BaseModel):
    """Output from specification analyzer agent."""
    primary_goal: str  # Main goal of the workflow
    complexity_level: str  # Simple, moderate, or complex
    key_capabilities_needed: List[str]  # List of capabilities needed (e.g., "file search", "data analysis")
    data_inputs: List[Dict[str, str]]  # Expected inputs with name, type, description
    data_outputs: List[Dict[str, str]]  # Expected outputs with name, type, description
    critical_requirements: List[str]  # Must-have requirements


class WorkflowArchitectureOutput(BaseModel):
    """Output from workflow architect agent."""
    workflow_description: str  # Clear description of what the workflow does
    workflow_steps: List[Dict[str, Any]]  # List with 'order', 'name', 'purpose', 'expected_output'
    data_flow: str  # How data flows between steps
    success_criteria: str  # What makes this workflow successful


class TeamDesignOutput(BaseModel):
    """Output from team designer agent."""
    teams: List[Dict[str, Any]]  # List of team designs with agents, skills, termination


class WorkflowJSONOutput(BaseModel):
    """Output from JSON compiler agent."""
    workflow_json: Dict[str, Any]  # The complete workflow JSON


class WorkflowJSONGenerator:
    """Generates workflow JSON from specification documents using OpenAI Agents SDK."""
    
    def __init__(self):
        self.model = "gpt-5"
        self.model_settings = ModelSettings(
            model="gpt-5",
            reasoning_effort="minimal",
            extra_body={
                "service_tier": "priority"
            }
        )
        
        # Log configuration
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("WARNING: OPENAI_API_KEY not set!")
        else:
            print(f"Workflow JSON Generator initialized with model: {self.model}")
        
        # Initialize the 4-agent sequential workflow
        self._init_agents()
        
    def _init_agents(self):
        """Initialize the 4-agent team for workflow JSON generation (Agents SDK structure)."""
        
        # Agent 1: Specification Analyzer
        self.spec_analyzer_agent = Agent(
            name="Specification Analyzer",
            handoff_description="Analyzes the specification document and identifies key requirements",
            instructions="""You are an expert at analyzing workflow specifications.

Your task is to analyze a specification document and extract key information needed for workflow design.

WHAT TO IDENTIFY:

1. PRIMARY GOAL: What is the main purpose of this workflow? What should it accomplish?
   Be clear and specific. Example: "Generate quarterly performance reports for portfolio companies"

2. COMPLEXITY LEVEL: Rate the workflow complexity as "simple", "moderate", or "complex"
   - Simple: 1-2 main tasks, straightforward data flow
   - Moderate: 3-4 tasks with some data dependencies
   - Complex: 5+ tasks with complex dependencies and branching

3. KEY CAPABILITIES NEEDED: List the key capabilities this workflow requires
   Examples: "file_search", "web_search", "data_analysis", "report_generation", 
   "comparison", "summarization", "validation", etc.

4. DATA INPUTS: What inputs does the workflow need? For each input:
   - name: variable name (e.g., "company_name", "date_range")
   - type: data type (string, integer, float, boolean, list, date)
   - description: what this input represents

5. DATA OUTPUTS: What outputs should the workflow produce? For each output:
   - name: variable name (e.g., "report", "analysis_results")
   - type: data type
   - description: what this output contains

6. CRITICAL REQUIREMENTS: List 3-5 must-have requirements that the workflow MUST fulfill
   Focus on the non-negotiable aspects of the specification.

Your analysis will guide the design of the workflow structure and teams.""",
            output_type=AgentOutputSchema(SpecificationAnalysisOutput, strict_json_schema=False),
            model_settings=self.model_settings
        )
        
        # Agent 2: Workflow Architect
        self.workflow_architect_agent = Agent(
            name="Workflow Architect",
            handoff_description="Designs the high-level workflow structure and data flow",
            instructions="""You are an expert workflow architect for Axon agentic systems.

Your task is to design the high-level structure of a workflow based on the analyzed requirements.

WORKFLOW DESIGN PRINCIPLES FOR AXON:
1. All steps must be "team" type (no delegates)
2. Break complex work into 2-5 logical, sequential team steps
3. Each team step should have a clear, focused purpose
4. Steps should build on previous outputs
5. Keep data flow simple and clear

YOUR DESIGN SHOULD INCLUDE:

1. WORKFLOW DESCRIPTION: A clear 1-2 sentence description of what this workflow does
   Focus on the value it provides and what it accomplishes.

2. WORKFLOW STEPS: Design 2-5 logical team steps. For each step:
   - order: step number (1, 2, 3, etc.)
   - name: clear, descriptive name for the team/step
   - purpose: what this team accomplishes and why it's needed
   - expected_output: what this team should produce
   
   Think about logical progression:
   - Data gathering teams (file search, web research)
   - Processing teams (analysis, comparison, validation)
   - Generation teams (report writing, summarization)
   
   Example steps:
   - "Information Gathering Team" → gather all required data
   - "Analysis Team" → analyze data and extract insights
   - "Report Generation Team" → create final deliverable

3. DATA FLOW: Describe how data flows through the workflow
   What information passes from team to team? How does each team use previous outputs?

4. SUCCESS CRITERIA: What indicates this workflow has succeeded?
   Be specific about the quality and completeness of outputs.

Design a clear, logical workflow structure that can be executed by Axon teams of AI agents.""",
            output_type=AgentOutputSchema(WorkflowArchitectureOutput, strict_json_schema=False),
            model_settings=self.model_settings
        )
        
        # Agent 3: Team Designer
        self.team_designer_agent = Agent(
            name="Team Designer",
            handoff_description="Designs specific teams with agents for each workflow step",
            instructions="""You are an expert at designing collaborative AI agent teams for Axon workflows.

Your task is to design specific teams of AI agents for each step of a workflow.

CRITICAL - AXON TEAM DESIGN PATTERN (from new guidelines):

Each team MUST follow this structure:
1. FIRST agent is the COORDINATOR/LEADER
   - Coordinates and routes work to specialists
   - Uses {roles}, {history}, {participants} for speaker selection
   - NEVER creates content - only delegates
   
2. REMAINING agents (2-3) are SPECIALISTS
   - Each has focused, specific skills
   - Skills written as "When coordinator asks: 1) Do X, 2) Do Y, 3) Hand back"
   - Use save_variable when they need to persist outputs

FOR EACH STEP, DESIGN A TEAM:

TEAM STRUCTURE:
- name: descriptive team name matching the step
- goal: clear statement of what the team should accomplish (may use {{variable_name}} syntax)
- results: bullet list of what the team produces
- termination: {"regex": ["TERMINATE"]} - use simple, unambiguous patterns
- agents: list of agents starting with coordinator
- variables: {} (empty for now)

COORDINATOR AGENT PATTERN:
{
  "name": "<role>_coordinator",
  "model": "gpt-5-mini",
  "persona": "You are a coordinator who delegates work to specialists. You never create content yourself, only assign tasks to the right agents.",
  "skills": "You are the coordinator. Your job is to orchestrate the work:\n\n1. First message: Read variables and ask @specialist1 to do their task\n2. After specialist1 responds, ask @specialist2 to do their task\n3. Continue until all work is done\n4. When complete, say: TERMINATE\n\nSelection guide:\n{roles}\n\nConversation so far:\n{history}\n\nAvailable agents: {participants}"
}

SPECIALIST AGENT PATTERN:
{
  "name": "descriptive_role_name",
  "model": "gpt-5" or "gpt-5-mini",
  "persona": "You are [description]. You [mindset/approach].",
  "skills": "When the coordinator asks you:\n1. [Specific task step]\n2. [Specific task step]\n3. Use save_variable(\"name\", \"value\") if you need to save outputs\n4. After completing, say: \"[Task] complete. Coordinator, please ask @next_agent...\"\n5. DO NOT do any other agent's work"
}

MODEL SELECTION GUIDELINES:
- Coordinators: Use "gpt-5-mini" (routing/coordination is simple)
- Specialists doing complex work: Use "gpt-5" (analysis, writing, synthesis, complex reasoning)
- Specialists doing simple work: Use "gpt-5-mini" (extraction, validation, simple formatting)

TERMINATION PATTERNS:
- Keep it simple: "TERMINATE" is best
- Can also use: "TASK_COMPLETE", "FINAL_OUTPUT_READY"
- Usually the final specialist or coordinator says it
- Make it VERY clear in agent skills when to emit the termination keyword

AGENT ROLE PATTERNS (choose appropriate specialists for each team):
- Researcher/Gatherer: Finds and collects information
- Analyzer: Processes and interprets data  
- Validator: Checks quality and accuracy
- Writer/Synthesizer: Creates outputs and summaries
- Strategist: Plans approach

Create teams that follow this Axon pattern exactly - coordinator + focused specialists.""",
            output_type=AgentOutputSchema(TeamDesignOutput, strict_json_schema=False),
            model_settings=self.model_settings
        )
        
        # Agent 4: JSON Compiler
        self.json_compiler_agent = Agent(
            name="JSON Compiler",
            handoff_description="Compiles all designs into the final workflow JSON format",
            instructions="""You are a technical writer specializing in Axon workflow JSON generation.

Your task is to compile all the design information into a complete workflow JSON that follows
the exact format required by Axon's workflow execution system.

You have been provided with:
- Specification analysis (goal, inputs, outputs, requirements)
- Workflow architecture (description, steps, data flow)
- Team designs (teams with agents for each step following Axon's coordinator/specialist pattern)

REQUIRED AXON WORKFLOW JSON STRUCTURE:

You must return your output in this EXACT format with a "workflow_json" wrapper:

{
  "workflow_json": {
    "description": "Clear description of what the workflow does",
    "variables": {
      "input": {
        "variable_name": {
          "type": "string/integer/float/boolean",
          "description": "What this input is for"
        }
      },
      "output": {
        "output_name": {
          "type": "string/integer/float/boolean",
          "description": "What this output contains"
        }
      }
    },
    "steps": [
      {
        "order": 1,
        "type": "team",
        "team": {
          "name": "Team Name",
          "goal": "What the team should accomplish (can use {{variables}})",
          "results": "- Bullet list of what the team produces\n- Another result\n",
          "termination": {
            "regex": ["TERMINATE"]
          },
          "agents": [
            {
              "name": "coordinator_name",
              "model": "gpt-5-mini",
              "persona": "Coordinator who delegates. Never creates content.",
              "skills": "Coordination instructions including:\n\n{roles}\n{history}\n{participants}"
            },
            {
              "name": "specialist_name",
              "model": "gpt-5",
              "persona": "Specialist description",
              "skills": "When coordinator asks:\n1. Do specific task\n2. Use save_variable if needed\n3. Hand back to coordinator"
            }
          ],
          "variables": {}
        }
      }
    ]
  }
}

CRITICAL AXON REQUIREMENTS:
1. Wrap entire output in "workflow_json" field as shown
2. All steps must have "type": "team" (no delegates)
3. Agent models: Use "gpt-5" or "gpt-5-mini" appropriately
   - Coordinators: "gpt-5-mini" (routing is simple)
   - Complex specialists: "gpt-5" (analysis, writing, reasoning)
   - Simple specialists: "gpt-5-mini" (extraction, validation)
4. First agent in EVERY team must be the coordinator
5. Coordinator skills MUST include {roles}, {history}, {participants} placeholders
6. Specialist skills MUST be procedural ("When coordinator asks: 1) X, 2) Y, 3) Z")
7. Termination regex should be simple patterns like ["TERMINATE"]
8. Variables must have valid types: string, integer, float, boolean
9. Steps numbered sequentially starting from 1
10. Each team must have at least 2 agents (coordinator + 1 specialist minimum)

Create a complete, valid Axon workflow JSON that can be executed by the Axon system.""",
            output_type=AgentOutputSchema(WorkflowJSONOutput, strict_json_schema=False),
            model_settings=self.model_settings
        )
    
    def extract_text_from_docx(self, file_path: str) -> str:
        """Extract text from a Word document."""
        doc = Document(file_path)
        text_parts = []
        
        for para in doc.paragraphs:
            if para.text.strip():
                text_parts.append(para.text.strip())
        
        return "\n\n".join(text_parts)
    
    def add_message(self, task_id: str, message_type: str, content: str):
        """Add a message to the task's message log."""
        with tasks_lock:
            if task_id in generation_tasks:
                if "messages" not in generation_tasks[task_id]:
                    generation_tasks[task_id]["messages"] = []
                generation_tasks[task_id]["messages"].append({
                    "type": message_type,
                    "content": content,
                    "timestamp": datetime.utcnow().isoformat()
                })
                # Also update the 'message' field for backward compatibility with API
                generation_tasks[task_id]["message"] = content
    
    def update_progress(self, task_id: str, progress: int):
        """Update task progress (0-100)."""
        with tasks_lock:
            if task_id in generation_tasks:
                generation_tasks[task_id]["progress"] = progress
    
    def set_error(self, task_id: str, error: str):
        """Set task error status."""
        with tasks_lock:
            if task_id in generation_tasks:
                generation_tasks[task_id]["status"] = "failed"
                generation_tasks[task_id]["error"] = error
                generation_tasks[task_id]["message"] = f"Error: {error}"
                generation_tasks[task_id]["completed_at"] = datetime.utcnow().isoformat()
    
    def set_complete(self, task_id: str, workflow_json: Dict[str, Any]):
        """Mark task as complete."""
        with tasks_lock:
            if task_id in generation_tasks:
                generation_tasks[task_id]["status"] = "completed"
                generation_tasks[task_id]["workflow_json"] = workflow_json
                generation_tasks[task_id]["message"] = "Workflow JSON generated successfully"
                generation_tasks[task_id]["progress"] = 100
                generation_tasks[task_id]["completed_at"] = datetime.utcnow().isoformat()
    
    async def _run_agent_with_tracking(
        self, 
        task_id: str, 
        agent: Agent, 
        input_text: str,
        agent_name: str,
        progress_value: int
    ) -> Any:
        """Run an agent and track progress with messages to frontend."""
        self.add_message(task_id, "thinking", f"Handoff to {agent_name}...")
        self.update_progress(task_id, progress_value)
        
        try:
            # Add timeout of 120 seconds for agent execution
            result = await asyncio.wait_for(
                Runner.run(agent, input_text),
                timeout=120.0
            )
            
            self.add_message(
                task_id, 
                "success", 
                f"{agent_name} completed analysis"
            )
            
            return result
            
        except asyncio.TimeoutError:
            error_msg = f"{agent_name} timed out after 120 seconds"
            self.add_message(task_id, "error", error_msg)
            raise
        except Exception as e:
            error_msg = f"{agent_name} error: {str(e)}"
            self.add_message(task_id, "error", error_msg)
            import traceback
            # Log full traceback for debugging
            print(f"Full error traceback for {agent_name}:")
            traceback.print_exc()
            raise
    
    async def generate_workflow_json_async(
        self,
        task_id: str,
        spec_text: str,
        workflow_name: str
    ):
        """
        Generate workflow JSON asynchronously using 4-agent sequential workflow.
        
        Uses OpenAI Agents SDK with sequential handoffs between agents.
        Each agent produces structured output that feeds into the next agent.
        
        Args:
            task_id: Task ID for tracking progress
            spec_text: The specification text
            workflow_name: Name for the workflow
        """
        try:
            self.add_message(task_id, "info", f"Starting workflow JSON generation: {workflow_name}")
            self.update_progress(task_id, 10)
            
            # Step 1: Specification Analysis
            self.add_message(task_id, "thinking", "Analyzing specification document...")
            spec_analysis_result = await self._run_agent_with_tracking(
                task_id,
                self.spec_analyzer_agent,
                f"""Analyze this workflow specification document:

{spec_text}

Extract the primary goal, complexity level, key capabilities needed, data inputs/outputs, and critical requirements.""",
                "Specification Analyzer",
                25
            )
            spec_analysis = spec_analysis_result.final_output_as(SpecificationAnalysisOutput)
            
            # Step 2: Workflow Architecture
            self.add_message(task_id, "thinking", "Designing workflow architecture...")
            architecture_context = f"""
Specification: {workflow_name}

ANALYSIS RESULTS:
- Primary Goal: {spec_analysis.primary_goal}
- Complexity Level: {spec_analysis.complexity_level}
- Key Capabilities Needed: {', '.join(spec_analysis.key_capabilities_needed)}
- Critical Requirements:
{chr(10).join(f"  - {req}" for req in spec_analysis.critical_requirements)}

Design a workflow structure with 2-5 logical team steps following Axon's new guidelines.
Each step should represent a major phase of work that will be executed by a team of agents
(with a coordinator and specialists).
"""
            architecture_result = await self._run_agent_with_tracking(
                task_id,
                self.workflow_architect_agent,
                architecture_context,
                "Workflow Architect",
                50
            )
            architecture = architecture_result.final_output_as(WorkflowArchitectureOutput)
            
            # Step 3: Team Design
            self.add_message(task_id, "thinking", "Designing agent teams for each step...")
            team_design_context = f"""
Specification: {workflow_name}
Goal: {spec_analysis.primary_goal}

WORKFLOW STEPS:
{chr(10).join(f"{step['order']}. {step['name']} - {step['purpose']}" for step in architecture.workflow_steps)}

For each of these {len(architecture.workflow_steps)} steps, design a team following Axon's new pattern:
- First agent is coordinator/leader who delegates work
- Remaining agents (2-3) are specialists with focused skills
- Coordinator uses {{roles}}, {{history}}, {{participants}} for routing
- Specialists have procedural skills starting with "When coordinator asks:"
- Clear termination patterns (TERMINATE)
- Specialists use save_variable for outputs

Design practical teams that will collaborate effectively.
"""
            team_design_result = await self._run_agent_with_tracking(
                task_id,
                self.team_designer_agent,
                team_design_context,
                "Team Designer",
                75
            )
            team_design = team_design_result.final_output_as(TeamDesignOutput)
            
            # Step 4: JSON Compilation
            self.add_message(task_id, "thinking", "Compiling final workflow JSON...")
            json_context = f"""
Workflow Name: {workflow_name}

SPECIFICATION ANALYSIS:
Primary Goal: {spec_analysis.primary_goal}
Complexity: {spec_analysis.complexity_level}
Capabilities: {', '.join(spec_analysis.key_capabilities_needed)}

Data Inputs: {json.dumps(spec_analysis.data_inputs, indent=2)}
Data Outputs: {json.dumps(spec_analysis.data_outputs, indent=2)}

Critical Requirements:
{chr(10).join(f"- {req}" for req in spec_analysis.critical_requirements)}

WORKFLOW ARCHITECTURE:
Description: {architecture.workflow_description}
Steps: {json.dumps(architecture.workflow_steps, indent=2)}
Data Flow: {architecture.data_flow}
Success Criteria: {architecture.success_criteria}

TEAM DESIGNS (following Axon coordinator/specialist pattern):
{json.dumps(team_design.teams, indent=2)}

Compile all of this into a complete Axon workflow JSON that follows the required format.
Ensure the JSON is valid and follows all Axon requirements (coordinator + specialists in each team).
"""
            json_result = await self._run_agent_with_tracking(
                task_id,
                self.json_compiler_agent,
                json_context,
                "JSON Compiler",
                95
            )
            
            workflow_json_output = json_result.final_output_as(WorkflowJSONOutput)
            workflow_json = workflow_json_output.workflow_json
            
            # Validate the JSON structure
            if not isinstance(workflow_json, dict):
                raise ValueError("Generated output is not a valid JSON object")
            
            if "steps" not in workflow_json:
                raise ValueError("Generated JSON missing 'steps' field")
            
            if "description" not in workflow_json:
                # Add description if missing
                workflow_json["description"] = architecture.workflow_description
            
            self.add_message(task_id, "success", "Workflow JSON generated successfully!")
            self.set_complete(task_id, workflow_json)
            
            print(f"[WorkflowJSONGenerator] Successfully generated workflow for task {task_id}")
            
        except Exception as e:
            error_msg = f"Error generating workflow JSON: {str(e)}"
            print(f"[WorkflowJSONGenerator] Error in task {task_id}: {error_msg}")
            self.add_message(task_id, "error", error_msg)
            self.set_error(task_id, error_msg)
            import traceback
            traceback.print_exc()
    
    def generate_workflow_json(
        self,
        spec_file_path: str,
        workflow_name: Optional[str] = None
    ) -> str:
        """
        Generate workflow JSON from a specification document (async task).
        
        Args:
            spec_file_path: Path to the specification document (.docx)
            workflow_name: Optional name for the workflow
        
        Returns:
            Task ID for checking progress
        """
        # Generate task ID
        task_id = str(uuid.uuid4())
        
        # Extract text from document
        spec_text = self.extract_text_from_docx(spec_file_path)
        
        # Use filename as workflow name if not provided
        if not workflow_name:
            workflow_name = Path(spec_file_path).stem.replace("_", " ").title()
        
        # Create task record with messages and progress tracking
        with tasks_lock:
            generation_tasks[task_id] = {
                "id": task_id,
                "status": "processing",
                "message": "Task queued for processing",  # For backward compatibility with API
                "workflow_name": workflow_name,
                "spec_file": spec_file_path,
                "messages": [],
                "progress": 0,
                "created_at": datetime.utcnow().isoformat(),
                "completed_at": None,
                "error": None,
                "workflow_json": None
            }
        
        # Start async generation in background
        import threading
        
        def run_async_generation():
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(
                    self.generate_workflow_json_async(task_id, spec_text, workflow_name)
                )
            finally:
                loop.close()
        
        thread = threading.Thread(target=run_async_generation, daemon=True)
        thread.start()
        
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get the status of a generation task."""
        with tasks_lock:
            return generation_tasks.get(task_id)
    
    def list_tasks(self) -> list:
        """List all generation tasks."""
        with tasks_lock:
            return list(generation_tasks.values())


# Singleton instance
_workflow_json_generator = None


def get_workflow_json_generator() -> WorkflowJSONGenerator:
    """Get or create the workflow JSON generator singleton."""
    global _workflow_json_generator
    if _workflow_json_generator is None:
        _workflow_json_generator = WorkflowJSONGenerator()
    return _workflow_json_generator

