"""
RabbitMQ monitoring service.

Provides functions to query RabbitMQ queue statistics for the dashboard.
"""

import os
import logging
from typing import Dict, Any, Optional

import aio_pika


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def get_rabbitmq_queue_stats(rabbitmq_url: Optional[str] = None) -> Dict[str, Any]:
    """
    Get RabbitMQ queue statistics.
    
    Args:
        rabbitmq_url: RabbitMQ connection URL (defaults to env var)
        
    Returns:
        Dictionary with queue statistics:
        {
            "agentic.flyweight": {"pending": 5, "consumers": 2},
            "agentic.async": {"pending": 10, "consumers": 1}
        }
    """
    rabbitmq_url = rabbitmq_url or os.getenv(
        "RABBITMQ_URL",
        "amqp://guest:guest@localhost:5672/"
    )
    
    try:
        # Connect to RabbitMQ
        connection = await aio_pika.connect_robust(rabbitmq_url)
        
        async with connection:
            channel = await connection.channel()
            
            stats = {}
            
            # Query standard queues
            queue_names = ["agentic.flyweight", "agentic.async"]
            
            for queue_name in queue_names:
                try:
                    # Get queue info using declare (passive mode)
                    queue = await channel.declare_queue(queue_name, passive=True)
                    
                    stats[queue_name] = {
                        "pending": queue.declaration_result.message_count,
                        "consumers": queue.declaration_result.consumer_count
                    }
                    
                except Exception as e:
                    logger.warning(f"Could not get stats for queue {queue_name}: {e}")
                    stats[queue_name] = {
                        "pending": 0,
                        "consumers": 0,
                        "error": str(e)
                    }
            
            return stats
    
    except Exception as e:
        logger.error(f"Error connecting to RabbitMQ: {e}", exc_info=True)
        return {
            "error": str(e),
            "agentic.flyweight": {"pending": 0, "consumers": 0, "error": "Connection failed"},
            "agentic.async": {"pending": 0, "consumers": 0, "error": "Connection failed"}
        }


async def check_rabbitmq_health(rabbitmq_url: Optional[str] = None) -> Dict[str, Any]:
    """
    Check RabbitMQ health.
    
    Args:
        rabbitmq_url: RabbitMQ connection URL (defaults to env var)
        
    Returns:
        Dictionary with health status:
        {
            "healthy": True/False,
            "connected": True/False,
            "error": "Error message" (if unhealthy)
        }
    """
    rabbitmq_url = rabbitmq_url or os.getenv(
        "RABBITMQ_URL",
        "amqp://guest:guest@localhost:5672/"
    )
    
    try:
        # Try to connect
        connection = await aio_pika.connect_robust(rabbitmq_url, timeout=5.0)
        
        async with connection:
            # If we got here, connection is successful
            return {
                "healthy": True,
                "connected": True
            }
    
    except Exception as e:
        logger.error(f"RabbitMQ health check failed: {e}")
        return {
            "healthy": False,
            "connected": False,
            "error": str(e)
        }

