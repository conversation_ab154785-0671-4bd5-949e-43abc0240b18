"""
Tool Name Resolver Service

Resolves tool names to UUIDs by querying the agentic-core database.
This allows workflows to use human-readable tool names instead of UUIDs.
"""

import os
from typing import Dict, List, Set
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker


class ToolResolver:
    """Resolves tool names to their corresponding UUIDs from the database."""
    
    def __init__(self, database_url: str = None):
        """
        Initialize the ToolResolver with a database connection.
        
        Args:
            database_url: PostgreSQL connection string. If None, uses DATABASE_URL env var.
        """
        self.database_url = database_url or os.getenv(
            "DATABASE_URL", 
            "postgresql://postgres:postgres@localhost:5432/invisible"
        )
        self.engine = create_engine(self.database_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        
        # Cache for resolved tools (name -> uuid)
        self._tool_cache: Dict[str, str] = {}
    
    def resolve_tool_names(self, tool_names: List[str]) -> Dict[str, str]:
        """
        Resolve a list of tool names to their UUIDs.
        
        Args:
            tool_names: List of tool names to resolve
            
        Returns:
            Dictionary mapping tool name to UUID (as string)
            
        Raises:
            ValueError: If any tool name cannot be resolved
        """
        if not tool_names:
            return {}
        
        # Check cache first
        uncached_names = [name for name in tool_names if name not in self._tool_cache]
        
        if uncached_names:
            # Query database for uncached tools
            with self.SessionLocal() as session:
                query = text("""
                    SELECT 
                        t.name,
                        tv.id as version_id
                    FROM agentic_objects.tool t
                    JOIN agentic_objects.tool_version tv ON t.id = tv.tool_id
                    WHERE t.name = ANY(:tool_names)
                    AND tv.is_draft = false
                    ORDER BY tv.modified_date DESC
                """)
                
                result = session.execute(query, {"tool_names": uncached_names})
                rows = result.fetchall()
                
                # Update cache with results
                seen_names = set()
                for row in rows:
                    tool_name = row[0]
                    tool_uuid = str(row[1])
                    
                    # Only cache the first (most recent) version of each tool
                    if tool_name not in seen_names:
                        self._tool_cache[tool_name] = tool_uuid
                        seen_names.add(tool_name)
        
        # Build result dictionary
        result = {}
        missing_tools = []
        
        for name in tool_names:
            if name in self._tool_cache:
                result[name] = self._tool_cache[name]
            else:
                missing_tools.append(name)
        
        if missing_tools:
            raise ValueError(f"Could not resolve tool names: {', '.join(missing_tools)}")
        
        return result
    
    def resolve_workflow_json(self, workflow_json: dict) -> dict:
        """
        Transform a workflow JSON by resolving all tool names to UUIDs.
        
        This function walks through the workflow JSON structure and replaces
        any tool name arrays with UUID arrays.
        
        Args:
            workflow_json: The workflow definition with tool names
            
        Returns:
            Transformed workflow JSON with tool UUIDs
        """
        import copy
        workflow = copy.deepcopy(workflow_json)
        
        # Collect all tool names from the workflow
        all_tool_names = self._collect_tool_names(workflow)
        
        if not all_tool_names:
            return workflow
        
        # Resolve all tool names to UUIDs
        tool_mapping = self.resolve_tool_names(list(all_tool_names))
        
        # Replace tool names with UUIDs in the workflow
        self._replace_tool_names(workflow, tool_mapping)
        
        return workflow
    
    def _collect_tool_names(self, obj) -> Set[str]:
        """
        Recursively collect all tool names from a workflow JSON structure.
        
        Args:
            obj: The object to search (dict, list, or primitive)
            
        Returns:
            Set of tool names found
        """
        tool_names = set()
        
        if isinstance(obj, dict):
            # Check if this is a tools array
            if "tools" in obj and isinstance(obj["tools"], list):
                for tool in obj["tools"]:
                    if isinstance(tool, str):
                        tool_names.add(tool)
            
            # Recursively search nested objects
            for value in obj.values():
                tool_names.update(self._collect_tool_names(value))
        
        elif isinstance(obj, list):
            for item in obj:
                tool_names.update(self._collect_tool_names(item))
        
        return tool_names
    
    def _replace_tool_names(self, obj, tool_mapping: Dict[str, str]):
        """
        Recursively replace tool names with UUIDs in a workflow JSON structure.
        
        Args:
            obj: The object to modify (dict or list)
            tool_mapping: Dictionary mapping tool names to UUIDs
        """
        if isinstance(obj, dict):
            # Replace tools array if found
            if "tools" in obj and isinstance(obj["tools"], list):
                obj["tools"] = [
                    tool_mapping.get(tool, tool) if isinstance(tool, str) else tool
                    for tool in obj["tools"]
                ]
            
            # Recursively process nested objects
            for value in obj.values():
                self._replace_tool_names(value, tool_mapping)
        
        elif isinstance(obj, list):
            for item in obj:
                self._replace_tool_names(item, tool_mapping)
    
    def clear_cache(self):
        """Clear the tool name cache. Useful if tools are updated."""
        self._tool_cache.clear()


# Global instance for reuse
_resolver_instance = None


def get_tool_resolver() -> ToolResolver:
    """
    Get or create a global ToolResolver instance.
    
    Returns:
        Singleton ToolResolver instance
    """
    global _resolver_instance
    if _resolver_instance is None:
        _resolver_instance = ToolResolver()
    return _resolver_instance

