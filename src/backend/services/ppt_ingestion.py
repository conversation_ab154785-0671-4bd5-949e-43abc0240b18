"""
PPT/PDF Ingestion Service

Processes PowerPoint (PPTX) and PDF files by:
1. Splitting into individual slides/pages
2. Extracting text via OCR (using PyMuPDF)
3. Converting slides to images
4. Using GPT-4 Vision to get detailed text representations
"""

import os
import io
import base64
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import tempfile

# Document processing
import fitz  # PyMuPDF
from pptx import Presentation
from PIL import Image

# OpenAI for vision processing
from openai import OpenAI


class SlideData:
    """Represents a single slide/page with text and image data."""
    
    def __init__(
        self,
        page_number: int,
        ocr_text: str,
        image_data: bytes,
        image_format: str = "png"
    ):
        self.page_number = page_number
        self.ocr_text = ocr_text
        self.image_data = image_data
        self.image_format = image_format
        self.enhanced_text: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert slide data to dictionary format."""
        return {
            "page_number": self.page_number,
            "ocr_text": self.ocr_text,
            "enhanced_text": self.enhanced_text,
            "has_image": bool(self.image_data),
            "image_format": self.image_format
        }


class PPTIngestionService:
    """Service for ingesting and processing PPT/PDF files."""
    
    def __init__(self, openai_api_key: Optional[str] = None):
        """
        Initialize the PPT ingestion service.
        
        Args:
            openai_api_key: OpenAI API key. If None, uses OPENAI_API_KEY env var.
        """
        api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OpenAI API key is required")
        
        self.client = OpenAI(api_key=api_key)
        # Use gpt-4o which has vision capabilities
        # Note: Update to gpt-4o or gpt-4-turbo for better performance
        # gpt-4-vision-preview is being deprecated
        self.vision_model = os.getenv("VISION_MODEL", "gpt-4o")
    
    def process_file(
        self,
        file_path: Union[str, Path],
        use_vision: bool = True,
        vision_prompt: Optional[str] = None
    ) -> List[SlideData]:
        """
        Process a PPT or PDF file and extract slide data.
        
        Args:
            file_path: Path to the PPTX or PDF file
            use_vision: Whether to use GPT-4 Vision for enhanced text extraction
            vision_prompt: Custom prompt for vision model (optional)
            
        Returns:
            List of SlideData objects, one per slide/page
            
        Raises:
            ValueError: If file format is not supported
            FileNotFoundError: If file does not exist
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_extension = file_path.suffix.lower()
        
        if file_extension == ".pdf":
            slides = self._process_pdf(file_path)
        elif file_extension == ".pptx":
            slides = self._process_pptx(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_extension}. Only .pdf and .pptx are supported.")
        
        # Enhance with GPT-4 Vision if requested
        if use_vision:
            self._enhance_with_vision(slides, vision_prompt)
        
        return slides
    
    def _process_pdf(self, file_path: Path) -> List[SlideData]:
        """
        Process a PDF file using PyMuPDF.
        
        Args:
            file_path: Path to the PDF file
            
        Returns:
            List of SlideData objects
        """
        slides = []
        
        # Open PDF with PyMuPDF
        doc = fitz.open(file_path)
        
        try:
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Extract text using OCR
                ocr_text = page.get_text()
                
                # Convert page to image
                # Higher resolution for better quality (300 DPI)
                zoom = 2.0  # Zoom factor for better quality
                mat = fitz.Matrix(zoom, zoom)
                pix = page.get_pixmap(matrix=mat)
                
                # Convert to PNG bytes
                image_data = pix.tobytes("png")
                
                slide = SlideData(
                    page_number=page_num + 1,
                    ocr_text=ocr_text,
                    image_data=image_data,
                    image_format="png"
                )
                slides.append(slide)
        
        finally:
            doc.close()
        
        return slides
    
    def _process_pptx(self, file_path: Path) -> List[SlideData]:
        """
        Process a PPTX file using python-pptx.
        
        Args:
            file_path: Path to the PPTX file
            
        Returns:
            List of SlideData objects
        """
        slides = []
        
        # First convert PPTX to PDF using PyMuPDF (which can handle PPTX)
        # Actually, python-pptx doesn't do rendering, so we need to convert to PDF first
        # For now, we'll extract text from PPTX and convert to images using a different approach
        
        prs = Presentation(file_path)
        
        # Convert PPTX to PDF for image extraction
        # We'll use a temporary PDF file
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_pdf:
            tmp_pdf_path = tmp_pdf.name
        
        try:
            # Convert PPTX to PDF using PyMuPDF
            # Note: This requires the file to be opened as a document
            doc = fitz.open(file_path)
            pdf_bytes = doc.convert_to_pdf()
            doc.close()
            
            # Write PDF to temp file
            with open(tmp_pdf_path, "wb") as f:
                f.write(pdf_bytes)
            
            # Now process as PDF for images
            pdf_doc = fitz.open(tmp_pdf_path)
            
            for slide_num, slide in enumerate(prs.slides):
                # Extract text from PPTX
                text_parts = []
                for shape in slide.shapes:
                    if hasattr(shape, "text"):
                        text_parts.append(shape.text)
                
                ocr_text = "\n".join(text_parts)
                
                # Get image from PDF version
                if slide_num < len(pdf_doc):
                    page = pdf_doc[slide_num]
                    zoom = 2.0
                    mat = fitz.Matrix(zoom, zoom)
                    pix = page.get_pixmap(matrix=mat)
                    image_data = pix.tobytes("png")
                else:
                    image_data = b""
                
                slide_data = SlideData(
                    page_number=slide_num + 1,
                    ocr_text=ocr_text,
                    image_data=image_data,
                    image_format="png"
                )
                slides.append(slide_data)
            
            pdf_doc.close()
        
        finally:
            # Clean up temp PDF
            if os.path.exists(tmp_pdf_path):
                os.unlink(tmp_pdf_path)
        
        return slides
    
    def _enhance_with_vision(
        self,
        slides: List[SlideData],
        custom_prompt: Optional[str] = None
    ) -> None:
        """
        Enhance slide text using GPT-4 Vision.
        
        This sends both the OCR text and the slide image to GPT-4 Vision
        to get a more complete and accurate text representation.
        
        Args:
            slides: List of SlideData objects to enhance
            custom_prompt: Custom prompt for vision model
        """
        default_prompt = """
You are analyzing a presentation slide/page. I have provided you with:
1. OCR-extracted text from the slide
2. An image of the slide

Please provide a comprehensive text representation of this slide that includes:
- All text content (correcting any OCR errors)
- Descriptions of any tables, charts, graphs, or diagrams
- The structure and layout of the content
- Any important visual elements that convey information

OCR Text:
{ocr_text}

Please provide a pure text representation that captures all the information from this slide.
"""
        
        prompt_template = custom_prompt or default_prompt
        
        for slide in slides:
            if not slide.image_data:
                slide.enhanced_text = slide.ocr_text
                continue
            
            try:
                # Encode image to base64
                image_base64 = base64.b64encode(slide.image_data).decode('utf-8')
                
                # Prepare the prompt
                prompt = prompt_template.format(ocr_text=slide.ocr_text)
                
                # Call GPT-4 Vision
                response = self.client.chat.completions.create(
                    model=self.vision_model,
                    messages=[
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": prompt
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/png;base64,{image_base64}",
                                        "detail": "high"
                                    }
                                }
                            ]
                        }
                    ],
                    max_tokens=2000
                )
                
                slide.enhanced_text = response.choices[0].message.content
            
            except Exception as e:
                print(f"Warning: Failed to enhance slide {slide.page_number} with vision: {e}")
                slide.enhanced_text = slide.ocr_text
    
    def export_to_text(
        self,
        slides: List[SlideData],
        output_path: Union[str, Path],
        use_enhanced: bool = True
    ) -> None:
        """
        Export slides to a text file.
        
        Args:
            slides: List of SlideData objects
            output_path: Path to output text file
            use_enhanced: Whether to use enhanced text (if available) or OCR text
        """
        output_path = Path(output_path)
        
        with open(output_path, "w", encoding="utf-8") as f:
            for slide in slides:
                f.write(f"{'=' * 80}\n")
                f.write(f"Slide/Page {slide.page_number}\n")
                f.write(f"{'=' * 80}\n\n")
                
                text = slide.enhanced_text if (use_enhanced and slide.enhanced_text) else slide.ocr_text
                f.write(text)
                f.write("\n\n")
    
    def export_to_json(
        self,
        slides: List[SlideData],
        output_path: Union[str, Path]
    ) -> None:
        """
        Export slides to a JSON file.
        
        Args:
            slides: List of SlideData objects
            output_path: Path to output JSON file
        """
        import json
        
        output_path = Path(output_path)
        
        data = {
            "total_slides": len(slides),
            "slides": [slide.to_dict() for slide in slides]
        }
        
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=2, ensure_ascii=False)


def get_ppt_ingestion_service(openai_api_key: Optional[str] = None) -> PPTIngestionService:
    """
    Get a PPTIngestionService instance.
    
    Args:
        openai_api_key: OpenAI API key. If None, uses OPENAI_API_KEY env var.
        
    Returns:
        PPTIngestionService instance
    """
    return PPTIngestionService(openai_api_key=openai_api_key)

