"""
SQLAlchemy models for the evaluation schema.

These models provide access to evaluation test cases, runs, suites, and batch runs.
They integrate with the existing flow_execution schema for execution tracking.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from sqlalchemy import (
    Column, String, Text, Boolean, TIMESTAMP, ForeignKey, 
    Integer, CheckConstraint, UniqueConstraint, Index, ARRAY
)
from sqlalchemy.dialects.postgresql import UUID as PGUUID, JSONB
from sqlalchemy.orm import relationship, declarative_base
from sqlalchemy.sql import func

# Use declarative base
Base = declarative_base()


class EvalCase(Base):
    """
    Test case definitions - what inputs to use and what outputs to expect.
    
    Each eval case defines:
    - Input variables for workflow execution
    - Expected outputs for validation
    - Optional validation rules (JSON schema, regex, etc.)
    - Tags for categorization
    """
    __tablename__ = "eval_case"
    __table_args__ = (
        UniqueConstraint('workflow_id', 'name', name='unique_eval_case_name_per_workflow'),
        {'schema': 'evaluation'}
    )
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    workflow_id = Column(PGUUID(as_uuid=True), nullable=False)  # FK defined in DB
    name = Column(String(255), nullable=False)
    description = Column(Text)
    input_variables = Column(JSONB, nullable=False)  # Same format as FlowExecution.variables
    expected_output = Column(JSONB)  # Expected outputs for validation
    validation_rules = Column(JSONB)  # Custom validation rules (JSON schema, regex, etc.)
    tags = Column(ARRAY(Text))  # For categorization
    is_active = Column(Boolean, default=True)
    created_by = Column(PGUUID(as_uuid=True), nullable=False)  # FK defined in DB
    created_at = Column(TIMESTAMP, default=func.now())
    updated_at = Column(TIMESTAMP, default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<EvalCase(id={self.id}, name='{self.name}', workflow_id={self.workflow_id})>"


class EvalSuite(Base):
    """
    Groups of test cases that can be run together.
    
    Suites allow organizing related test cases for batch execution.
    """
    __tablename__ = "eval_suite"
    __table_args__ = (
        UniqueConstraint('workflow_id', 'name', name='unique_suite_name_per_workflow'),
        {'schema': 'evaluation'}
    )
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    workflow_id = Column(PGUUID(as_uuid=True), nullable=False)  # FK defined in DB
    name = Column(String(255), nullable=False)
    description = Column(Text)
    created_by = Column(PGUUID(as_uuid=True), nullable=False)  # FK defined in DB
    created_at = Column(TIMESTAMP, default=func.now())
    updated_at = Column(TIMESTAMP, default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<EvalSuite(id={self.id}, name='{self.name}', workflow_id={self.workflow_id})>"


class EvalSuiteCases(Base):
    """
    Junction table linking test suites to test cases with ordering.
    """
    __tablename__ = "eval_suite_cases"
    __table_args__ = {'schema': 'evaluation'}
    
    suite_id = Column(PGUUID(as_uuid=True), primary_key=True)  # FK defined in DB
    eval_case_id = Column(PGUUID(as_uuid=True), primary_key=True)  # FK defined in DB
    order_index = Column(Integer, nullable=False)
    
    def __repr__(self):
        return f"<EvalSuiteCases(suite_id={self.suite_id}, eval_case_id={self.eval_case_id}, order={self.order_index})>"


class EvalBatchRun(Base):
    """
    Tracks bulk execution of multiple test cases.
    
    Batch runs group multiple eval runs together for tracking and reporting.
    """
    __tablename__ = "eval_batch_run"
    __table_args__ = (
        CheckConstraint("status IN ('RUNNING', 'COMPLETED', 'CANCELLED')", name='valid_batch_status'),
        {'schema': 'evaluation'}
    )
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    workflow_id = Column(PGUUID(as_uuid=True), nullable=False)  # FK defined in DB
    suite_id = Column(PGUUID(as_uuid=True))  # FK defined in DB
    name = Column(String(255))
    started_by = Column(PGUUID(as_uuid=True), nullable=False)  # FK defined in DB
    started_at = Column(TIMESTAMP, default=func.now())
    status = Column(String(50), nullable=False, default='RUNNING')
    
    def __repr__(self):
        return f"<EvalBatchRun(id={self.id}, name='{self.name}', status={self.status})>"


class EvalRun(Base):
    """
    Links eval cases to flow executions and stores validation results.
    
    This is the minimal evaluation-specific table that extends flow_execution.
    All execution state (status, timing, I/O) comes from flow_execution.
    This table only stores:
    - Link to eval case and flow execution
    - Validation results (passed/failed)
    - Validation details
    """
    __tablename__ = "eval_run"
    __table_args__ = {'schema': 'evaluation'}
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    eval_case_id = Column(PGUUID(as_uuid=True), nullable=False)  # FK defined in DB
    flow_execution_id = Column(PGUUID(as_uuid=True), nullable=False, unique=True)  # FK defined in DB
    batch_run_id = Column(PGUUID(as_uuid=True))  # FK defined in DB
    
    # ONLY evaluation-specific fields - everything else comes from flow_execution
    passed = Column(Boolean)  # Validation result (NULL = not yet validated)
    validation_output = Column(JSONB)  # Detailed validation results
    validation_notes = Column(Text)  # Human-readable explanation
    evaluated_at = Column(TIMESTAMP)  # When validation was performed
    
    def __repr__(self):
        return f"<EvalRun(id={self.id}, eval_case_id={self.eval_case_id}, flow_execution_id={self.flow_execution_id}, passed={self.passed})>"
    
    @property
    def status(self):
        """Get execution status from flow_execution (requires join query)."""
        # This would need to be populated via query with join
        # Example: session.query(EvalRun, FlowExecution).join(...).filter(...)
        return None
    
    @property
    def duration_ms(self):
        """Get execution duration from flow_execution (requires join query)."""
        # This would need to be calculated from flow_execution.start_time and end_time
        return None


# Create indexes
Index('idx_eval_case_workflow', EvalCase.workflow_id)
Index('idx_eval_case_active', EvalCase.is_active)
Index('idx_eval_case_tags', EvalCase.tags, postgresql_using='gin')
Index('idx_eval_case_created', EvalCase.created_at.desc())

Index('idx_eval_run_case', EvalRun.eval_case_id)
Index('idx_eval_run_execution', EvalRun.flow_execution_id)
Index('idx_eval_run_batch', EvalRun.batch_run_id)
Index('idx_eval_run_evaluated', EvalRun.evaluated_at.desc())

Index('idx_eval_suite_workflow', EvalSuite.workflow_id)

Index('idx_eval_batch_workflow', EvalBatchRun.workflow_id)
Index('idx_eval_batch_suite', EvalBatchRun.suite_id)
Index('idx_eval_batch_started', EvalBatchRun.started_at.desc())

