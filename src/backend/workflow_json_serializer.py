#!/usr/bin/env python3
"""
JSON Serializer for Axon-PFC Workflows

This module provides functions to serialize and deserialize workflows between
the database and a clean, intuitive JSON format.

Design principles:
- JSON instead of YAML for better structure and validation
- Delegates and tools referenced by name/ID (not fully defined inline)
- Support for both Team-based and Delegate-based workflows
- Clear separation between metadata and content
- Easy to read, write, and version control
"""

from __future__ import annotations

import json
from typing import Dict, List, Optional, Any, Tuple, TYPE_CHECKING
from uuid import UUID, uuid4
from sqlalchemy.orm import Session

# Database imports (these will be available when running in the agentic-core context)
if TYPE_CHECKING:
    from database.agentic_objects.models import (
        Flow, FlowVersion, Step, Delegate, DelegateVersion,
        Team, TeamVersion, AgenticEntityType, Namespace
    )
else:
    try:
        from database.agentic_objects.models import (
            Flow, FlowVersion, Step, Delegate, DelegateVersion,
            Team, TeamVersion, AgenticEntityType, Namespace
        )
    except ImportError:
        # Types will be available at runtime in agentic-core environment
        pass  # type: ignore


# ============================================================================
# JSON Schema Definitions (for documentation)
# ============================================================================

"""
Workflow JSON Structure (metadata like name, created_at, modified_at are handled separately):
{
    "description": "A workflow with multiple steps",
    "variables": {
        "input": {
            "data": {
                "type": "string",
                "description": "Input data",
                "default": "test"
            }
        },
        "output": {
            "result": {
                "type": "string",
                "description": "Output result"
            }
        }
    },
    "steps": [
        {
            "order": 1,
            "type": "delegate",
            "delegate_id": "uuid-here",
            "delegate_name": "DataValidator",
            "description": "Validate input data"
        },
        {
            "order": 2,
            "type": "team",
            "team": {
                "name": "Processing Team",
                "goal": "Process the validated data",
                "results": "Processed data",
                "termination": {
                    "regex": ["DONE", "COMPLETE"]
                },
                "agents": [
                    {
                        "name": "processor",
                        "model": "gpt-4o",
                        "persona": "You are a data processor",
                        "skills": "Process and transform data"
                    }
                ],
                "variables": {}
            }
        },
        {
            "order": 3,
            "type": "flow",
            "flow_id": "sub-workflow-uuid-here",
            "flow_name": "SubWorkflowName",
            "description": "Run sub-workflow"
        }
    ]
}
"""


# ============================================================================
# Database to JSON (Read from DB)
# ============================================================================

def flow_to_json(db: Session, flow: Flow, flow_version: FlowVersion) -> Dict[str, Any]:
    """
    Convert a Flow and FlowVersion from the database to JSON format.
    
    A workflow consists of steps, where each step can be:
    - A delegate (referenced by ID/name)
    - A team (fully defined inline)
    - A sub-workflow (referenced by ID/name)
    
    Note: Metadata like name, created_at, modified_at are NOT included in the JSON.
    They are handled separately by the API layer.
    
    Args:
        db: Database session
        flow: Flow object
        flow_version: FlowVersion object
        
    Returns:
        Dictionary representing the workflow in JSON format (without metadata)
    """
    # Helper function to transform array format back to nested dict format
    def transform_array_to_dict(vars_array: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Transform array format from database to nested dict for JSON API."""
        result = {}
        for var in vars_array:
            if isinstance(var, dict) and 'name' in var:
                var_name = var['name']
                result[var_name] = {
                    'type': var.get('type', 'string').lower(),
                    'description': var.get('description'),
                    'default': var.get('default_value')
                }
                # Remove None values
                result[var_name] = {k: v for k, v in result[var_name].items() if v is not None}
        return result
    
    # Get steps
    steps = db.query(Step).filter(
        Step.flow_version_id == flow_version.id
    ).order_by(Step.step_order).all()
    
    # Transform variables from array to nested dict format
    input_vars = flow_version.input_variables or []
    output_vars = flow_version.output_variables or []
    
    # Build workflow structure (without metadata section)
    workflow: Dict[str, Any] = {
        "description": flow_version.description or "",
        "variables": {
            "input": transform_array_to_dict(input_vars if isinstance(input_vars, list) else []),
            "output": transform_array_to_dict(output_vars if isinstance(output_vars, list) else [])
        },
        "steps": []
    }
    
    # Process each step
    for step in steps:
        step_dict = {
            "order": step.step_order,
            "type": step.entity_type.value
        }
        
        if step.entity_type == AgenticEntityType.team:
            # Teams are fully defined inline
            team_version = db.query(TeamVersion).filter(
                TeamVersion.id == step.entity_id
            ).first()
            
            if team_version:
                team = db.query(Team).filter(Team.id == team_version.team_id).first()
                # Transform team variables from array to nested dict format
                team_vars = team_version.variables or []
                team_vars_dict = {}
                if isinstance(team_vars, list):
                    for var in team_vars:
                        if isinstance(var, dict) and 'name' in var:
                            var_name = var['name']
                            team_vars_dict[var_name] = {
                                'type': var.get('type', 'string').lower(),
                                'description': var.get('description'),
                                'default': var.get('default_value')
                            }
                            # Remove None values
                            team_vars_dict[var_name] = {k: v for k, v in team_vars_dict[var_name].items() if v is not None}
                
                # Remove agent IDs from the agents list for cleaner JSON
                agents_list = team_version.agents or []
                agents_without_ids = []
                for agent in agents_list:
                    if isinstance(agent, dict):
                        # Create a copy without the 'id' field
                        agent_copy = {k: v for k, v in agent.items() if k != 'id'}
                        agents_without_ids.append(agent_copy)
                    else:
                        agents_without_ids.append(agent)
                
                step_dict["team"] = {
                    "name": team.name if team else "Unknown Team",
                    "goal": team_version.goal or "",
                    "results": team_version.results or "",
                    "termination": {
                        "regex": team_version.termination or []
                    },
                    "agents": agents_without_ids,
                    "variables": team_vars_dict
                }
        
        elif step.entity_type == AgenticEntityType.delegate:
            # Delegates are referenced by ID/name
            delegate_version = db.query(DelegateVersion).filter(
                DelegateVersion.id == step.entity_id
            ).first()
            
            if delegate_version:
                delegate = db.query(Delegate).filter(
                    Delegate.id == delegate_version.delegate_id
                ).first()
                
                if delegate:
                    step_dict["delegate_id"] = str(delegate.id)
                    step_dict["delegate_name"] = delegate.name
                    step_dict["delegate_version_id"] = str(delegate_version.id)
                    step_dict["description"] = delegate_version.description or ""
        
        elif step.entity_type == AgenticEntityType.flow:
            # Sub-workflows are referenced by ID/name
            sub_flow_version = db.query(FlowVersion).filter(
                FlowVersion.id == step.entity_id
            ).first()
            
            if sub_flow_version:
                sub_flow = db.query(Flow).filter(
                    Flow.id == sub_flow_version.flow_id
                ).first()
                
                if sub_flow:
                    step_dict["flow_id"] = str(sub_flow.id)
                    step_dict["flow_name"] = sub_flow.name
                    step_dict["flow_version_id"] = str(sub_flow_version.id)
                    step_dict["description"] = sub_flow_version.description or ""
        
        workflow["steps"].append(step_dict)
    
    return workflow




# ============================================================================
# JSON to Database (Write to DB)
# ============================================================================

def json_to_flow(
    db: Session,
    json_data: Dict[str, Any],
    user_id: UUID,
    name: str,
    namespace_name: str = "default",
    flow_id: Optional[UUID] = None,
    is_draft: bool = False
) -> Tuple[Flow, FlowVersion]:
    """
    Create or update a Flow and FlowVersion from JSON format.
    
    The workflow consists of steps, where each step can be:
    - A delegate (referenced by ID/name) - will be looked up
    - A team (fully defined inline) - will be created
    - A sub-workflow (referenced by ID/name) - will be looked up
    
    Note: Metadata like name, namespace, is_draft are passed as separate parameters,
    not embedded in the JSON. Timestamps are auto-generated.
    
    Args:
        db: Database session
        json_data: Workflow in JSON format (without metadata section)
        user_id: User creating/updating the workflow
        name: Workflow name (separate from JSON)
        namespace_name: Namespace name (defaults to "default")
        flow_id: Optional existing flow ID (for updates)
        is_draft: Whether this is a draft version
        
    Returns:
        Tuple of (Flow, FlowVersion)
    """
    # Resolve tool names to UUIDs before processing
    # This allows workflows to use human-readable tool names instead of UUIDs
    from services.tool_resolver import get_tool_resolver
    
    try:
        resolver = get_tool_resolver()
        json_data = resolver.resolve_workflow_json(json_data)
    except ValueError as e:
        # If tool resolution fails, raise a clear error
        raise ValueError(f"Tool resolution failed: {str(e)}")
    
    variables = json_data.get("variables", {})
    steps_data = json_data.get("steps", [])
    description = json_data.get("description", "")
    
    # Transform variables from nested dict format to array format
    # JSON format: {"input": {"varname": {"type": "string", "default": "value"}}}
    # DB format: [{"name": "varname", "type": "String", "default_value": "value", "direction": "Input"}]
    def transform_variables_to_array(vars_dict: Dict[str, Any], direction: str = "Input") -> List[Dict[str, Any]]:
        """Transform nested dict of variables to array format for database."""
        result = []
        for var_name, var_config in vars_dict.items():
            if isinstance(var_config, dict):
                # Normalize type: lowercase "string" -> capitalized "String" 
                var_type = var_config.get('type', 'string')
                if var_type.lower() == 'string':
                    var_type = 'String'
                elif var_type.lower() == 'int' or var_type.lower() == 'integer':
                    var_type = 'Int'
                elif var_type.lower() == 'float':
                    var_type = 'Float'
                elif var_type.lower() == 'boolean' or var_type.lower() == 'bool':
                    var_type = 'Boolean'
                elif var_type.lower() == 'secret':
                    var_type = 'Secret'
                elif var_type.lower() == 'file':
                    var_type = 'File'
                elif var_type.lower() == 'array':
                    var_type = 'String'  # Arrays stored as String for now
                elif var_type.lower() == 'object':
                    var_type = 'String'  # Objects stored as JSON-serialized String
                
                result.append({
                    'name': var_name,
                    'type': var_type,
                    'default_value': var_config.get('default'),
                    'description': var_config.get('description'),
                    'direction': direction  # Required by agentic-core
                })
        return result
    
    input_vars_array = transform_variables_to_array(variables.get("input", {}), direction="Input")
    output_vars_array = transform_variables_to_array(variables.get("output", {}), direction="Output")
    
    # Get or create namespace
    namespace = db.query(Namespace).filter(Namespace.name == namespace_name).first()
    if not namespace:
        namespace = Namespace(
            id=uuid4(),
            name=namespace_name,
            created_by=user_id
        )
        db.add(namespace)
        db.flush()
    
    # Create or get Flow
    if flow_id:
        # Update case: reuse existing Flow (don't update name - Flow is immutable)
        flow = db.query(Flow).filter(Flow.id == flow_id).first()
        if not flow:
            raise ValueError(f"Flow not found: {flow_id}")
        # Don't update flow.name - Flow should remain immutable
        # Only FlowVersion changes during updates
    else:
        # Create case: create new Flow
        flow = Flow(
            id=uuid4(),
            namespace_id=namespace.id,
            name=name,
            is_archived=False,
            created_by=user_id
        )
        db.add(flow)
        db.flush()
    
    # Create FlowVersion with properly formatted variables
    flow_version = FlowVersion(
        id=uuid4(),
        flow_id=flow.id,
        description=description,
        is_draft=is_draft,
        input_variables=input_vars_array,
        output_variables=output_vars_array,
        modified_by=user_id
    )
    db.add(flow_version)
    db.flush()
    
    # Create Steps
    for step_data in steps_data:
        step_order = step_data.get("order", 1)
        step_type = step_data.get("type", "delegate")
        entity_id = None
        
        if step_type == "team":
            # Team is fully defined inline
            team_data = step_data.get("team", {})
            team_name = team_data.get("name", f"Team for step {step_order}")
            
            # Check if Team already exists in this namespace
            team = db.query(Team).filter(
                Team.namespace_id == namespace.id,
                Team.name == team_name,
                Team.is_archived.is_(False)
            ).first()
            
            if not team:
                # Create new Team if it doesn't exist
                team = Team(
                    id=uuid4(),
                    namespace_id=namespace.id,
                    name=team_name,
                    is_archived=False,
                    created_by=user_id
                )
                db.add(team)
                db.flush()
            
            # Transform team variables to array format (same as flow variables)
            # Team variables default to Input direction
            team_vars_array = transform_variables_to_array(team_data.get("variables", {}), direction="Input")
            
            # Ensure each agent has an ID (required by agentic-core)
            agents_list = team_data.get("agents", [])
            for agent in agents_list:
                if 'id' not in agent:
                    agent['id'] = str(uuid4())
            
            # Always create a new TeamVersion (even if Team exists)
            team_version = TeamVersion(
                id=uuid4(),
                team_id=team.id,
                goal=team_data.get("goal", ""),
                results=team_data.get("results", ""),
                termination=team_data.get("termination", {}).get("regex", []),
                agents=agents_list,
                variables=team_vars_array,
                is_draft=False,
                modified_by=user_id
            )
            db.add(team_version)
            db.flush()
            
            # Update team latest version
            team.latest_version = team_version.id
            
            entity_id = team_version.id
        
        elif step_type == "delegate":
            # Delegate is referenced - look it up
            delegate_id = step_data.get("delegate_id")
            delegate_name = step_data.get("delegate_name")
            delegate_version_id = step_data.get("delegate_version_id")
            
            if delegate_version_id:
                # Use specific version
                entity_id = UUID(delegate_version_id)
            elif delegate_id:
                # Use latest version of specific delegate
                delegate = db.query(Delegate).filter(Delegate.id == UUID(delegate_id)).first()
                if delegate:
                    latest_version = db.query(DelegateVersion).filter(
                        DelegateVersion.delegate_id == delegate.id
                    ).order_by(DelegateVersion.modified_date.desc()).first()
                    if latest_version:
                        entity_id = latest_version.id
            elif delegate_name:
                # Look up by name
                delegate = db.query(Delegate).filter(
                    Delegate.namespace_id == namespace.id,
                    Delegate.name == delegate_name,
                    Delegate.is_archived.is_(False)
                ).first()
                if delegate:
                    latest_version = db.query(DelegateVersion).filter(
                        DelegateVersion.delegate_id == delegate.id
                    ).order_by(DelegateVersion.modified_date.desc()).first()
                    if latest_version:
                        entity_id = latest_version.id
        
        elif step_type == "flow":
            # Sub-workflow is referenced - look it up
            sub_flow_id = step_data.get("flow_id")
            sub_flow_name = step_data.get("flow_name")
            sub_flow_version_id = step_data.get("flow_version_id")
            
            if sub_flow_version_id:
                # Use specific version
                entity_id = UUID(sub_flow_version_id)
            elif sub_flow_id:
                # Use latest version of specific flow
                sub_flow = db.query(Flow).filter(Flow.id == UUID(sub_flow_id)).first()
                if sub_flow:
                    latest_version = db.query(FlowVersion).filter(
                        FlowVersion.flow_id == sub_flow.id
                    ).order_by(FlowVersion.modified_date.desc()).first()
                    if latest_version:
                        entity_id = latest_version.id
            elif sub_flow_name:
                # Look up by name
                sub_flow = db.query(Flow).filter(
                    Flow.namespace_id == namespace.id,
                    Flow.name == sub_flow_name,
                    Flow.is_archived.is_(False)
                ).first()
                if sub_flow:
                    latest_version = db.query(FlowVersion).filter(
                        FlowVersion.flow_id == sub_flow.id
                    ).order_by(FlowVersion.modified_date.desc()).first()
                    if latest_version:
                        entity_id = latest_version.id
        
        # Create the step if we have an entity_id
        if entity_id:
            step = Step(
                id=uuid4(),
                flow_version_id=flow_version.id,
                step_order=step_order,
                entity_type=AgenticEntityType[step_type],
                entity_id=entity_id,
                debug_config={}
            )
            db.add(step)
    
    db.flush()
    return flow, flow_version




# ============================================================================
# Comparison and Diff Functions
# ============================================================================

def compare_workflows(
    current_json: Dict[str, Any],
    new_json: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Compare two workflow JSON representations and return the differences.
    
    Args:
        current_json: Current workflow state
        new_json: New workflow state
        
    Returns:
        Dictionary describing the changes:
        {
            "has_changes": bool,
            "description_changed": bool,
            "variable_changes": {...},
            "step_changes": [...]
        }
    """
    diff: Dict[str, Any] = {
        "has_changes": False,
        "description_changed": False,
        "variable_changes": {},
        "step_changes": []
    }
    
    # Compare description
    if current_json.get("description") != new_json.get("description"):
        diff["description_changed"] = True
        diff["has_changes"] = True
    
    # Compare steps
    current_steps: List[Dict[str, Any]] = current_json.get("steps", [])  # type: ignore
    new_steps: List[Dict[str, Any]] = new_json.get("steps", [])  # type: ignore
    
    if len(current_steps) != len(new_steps):
        diff["step_changes"].append({
            "type": "count_changed",
            "old_count": len(current_steps),
            "new_count": len(new_steps)
        })
        diff["has_changes"] = True
    
    for i, (curr_step, new_step) in enumerate(zip(current_steps, new_steps)):
        step_diff = {}
        
        # Compare step type and order
        for key in ["order", "type"]:
            if curr_step.get(key) != new_step.get(key):
                step_diff[key] = {
                    "old": curr_step.get(key),
                    "new": new_step.get(key)
                }
        
        # Compare based on step type
        step_type = curr_step.get("type")
        
        if step_type == "team":
            # Compare inline team definitions
            if curr_step.get("team") != new_step.get("team"):
                step_diff["team"] = {
                    "old": curr_step.get("team"),
                    "new": new_step.get("team")
                }
        elif step_type == "delegate":
            # Compare delegate references
            for key in ["delegate_id", "delegate_name", "delegate_version_id"]:
                if curr_step.get(key) != new_step.get(key):
                    step_diff[key] = {
                        "old": curr_step.get(key),
                        "new": new_step.get(key)
                    }
        elif step_type == "flow":
            # Compare sub-workflow references
            for key in ["flow_id", "flow_name", "flow_version_id"]:
                if curr_step.get(key) != new_step.get(key):
                    step_diff[key] = {
                        "old": curr_step.get(key),
                        "new": new_step.get(key)
                    }
        
        if step_diff:
            diff["step_changes"].append({
                "step_index": i,
                "changes": step_diff
            })
            diff["has_changes"] = True
    
    # Compare variables
    current_vars = current_json.get("variables", {})
    new_vars = new_json.get("variables", {})
    
    if current_vars != new_vars:
        diff["variable_changes"] = {
            "old": current_vars,
            "new": new_vars
        }
        diff["has_changes"] = True
    
    return diff


def update_workflow_from_json(
    db: Session,
    current_flow: Flow,
    current_flow_version: FlowVersion,
    new_json: Dict[str, Any],
    user_id: UUID,
    name: Optional[str] = None,
    namespace_name: Optional[str] = None,
    is_draft: Optional[bool] = None,
    always_create_new_version: bool = False
) -> Tuple[Flow, FlowVersion, bool]:
    """
    Update a workflow from JSON, optionally creating a new version if there are changes.
    
    Args:
        db: Database session
        current_flow: Current Flow object
        current_flow_version: Current FlowVersion object
        new_json: New workflow JSON (without metadata)
        user_id: User performing the update
        name: Optional new name (defaults to current flow name)
        namespace_name: Optional namespace (defaults to current namespace)
        is_draft: Optional draft status (defaults to current status)
        always_create_new_version: If True, always create a new version; 
                                    if False, only create new version if there are changes
        
    Returns:
        Tuple of (Flow, FlowVersion, was_updated: bool)
    """
    # Get current state as JSON
    current_json = flow_to_json(db, current_flow, current_flow_version)
    
    # Compare
    diff = compare_workflows(current_json, new_json)
    
    if not diff["has_changes"] and not always_create_new_version:
        # No changes, return current version
        return current_flow, current_flow_version, False
    
    # Use current values if not provided
    if name is None:
        name = current_flow.name
    if namespace_name is None:
        namespace = db.query(Namespace).filter(Namespace.id == current_flow.namespace_id).first()
        namespace_name = namespace.name if namespace else "default"
    if is_draft is None:
        is_draft = current_flow_version.is_draft
    
    # Create new version
    new_flow, new_flow_version = json_to_flow(
        db, 
        new_json, 
        user_id,
        name=name,
        namespace_name=namespace_name,
        flow_id=current_flow.id,
        is_draft=is_draft
    )
    
    return new_flow, new_flow_version, True


# ============================================================================
# Utility Functions
# ============================================================================

def export_workflow_to_json_file(
    db: Session,
    flow_version_id: UUID,
    output_path: str,
    pretty: bool = True
) -> None:
    """
    Export a workflow to a JSON file.
    
    Args:
        db: Database session
        flow_version_id: FlowVersion ID to export
        output_path: Path to write JSON file
        pretty: If True, format JSON with indentation
    """
    # Get flow version
    flow_version = db.query(FlowVersion).filter(
        FlowVersion.id == flow_version_id
    ).first()
    
    if not flow_version:
        raise ValueError(f"FlowVersion not found: {flow_version_id}")
    
    # Get flow
    flow = db.query(Flow).filter(Flow.id == flow_version.flow_id).first()
    if not flow:
        raise ValueError(f"Flow not found: {flow_version.flow_id}")
    
    # Convert to JSON
    workflow_json = flow_to_json(db, flow, flow_version)
    
    # Write to file
    with open(output_path, 'w') as f:
        if pretty:
            json.dump(workflow_json, f, indent=2, ensure_ascii=False)
        else:
            json.dump(workflow_json, f, ensure_ascii=False)


def import_workflow_from_json_file(
    db: Session,
    input_path: str,
    user_id: UUID,
    name: str,
    namespace_name: str = "default",
    flow_id: Optional[UUID] = None,
    is_draft: bool = False
) -> Tuple[Flow, FlowVersion]:
    """
    Import a workflow from a JSON file.
    
    Args:
        db: Database session
        input_path: Path to JSON file
        user_id: User creating the workflow
        name: Workflow name
        namespace_name: Namespace name (defaults to "default")
        flow_id: Optional existing flow ID (for updates)
        is_draft: Whether this is a draft version
        
    Returns:
        Tuple of (Flow, FlowVersion)
    """
    with open(input_path, 'r') as f:
        workflow_json = json.load(f)
    
    return json_to_flow(db, workflow_json, user_id, name, namespace_name, flow_id, is_draft)


def json_string_to_flow(
    db: Session,
    json_string: str,
    user_id: UUID,
    name: str,
    namespace_name: str = "default",
    flow_id: Optional[UUID] = None,
    is_draft: bool = False
) -> Tuple[Flow, FlowVersion]:
    """
    Create or update a Flow from a JSON string.
    
    Args:
        db: Database session
        json_string: Workflow in JSON string format (without metadata)
        user_id: User creating/updating the workflow
        name: Workflow name
        namespace_name: Namespace name (defaults to "default")
        flow_id: Optional existing flow ID (for updates)
        is_draft: Whether this is a draft version
        
    Returns:
        Tuple of (Flow, FlowVersion)
    """
    workflow_json = json.loads(json_string)
    return json_to_flow(db, workflow_json, user_id, name, namespace_name, flow_id, is_draft)


def flow_to_json_string(
    db: Session,
    flow: Flow,
    flow_version: FlowVersion,
    pretty: bool = True
) -> str:
    """
    Convert a Flow and FlowVersion to a JSON string.
    
    Args:
        db: Database session
        flow: Flow object
        flow_version: FlowVersion object
        pretty: If True, format JSON with indentation
        
    Returns:
        JSON string representation
    """
    workflow_json = flow_to_json(db, flow, flow_version)
    
    if pretty:
        return json.dumps(workflow_json, indent=2, ensure_ascii=False)
    else:
        return json.dumps(workflow_json, ensure_ascii=False)

