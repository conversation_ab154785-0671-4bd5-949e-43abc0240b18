// API Configuration
const API_BASE_URL = 'http://localhost:8000';

// Utility: API client
const api = {
    async get(endpoint) {
        const response = await fetch(`${API_BASE_URL}${endpoint}`);
        if (!response.ok) {
            throw new Error(`API Error: ${response.statusText}`);
        }
        return response.json();
    },
    
    async post(endpoint, data) {
        const response = await fetch(`${API_BASE_URL}${endpoint}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || `API Error: ${response.statusText}`);
        }
        return response.json();
    },
    
    async put(endpoint, data) {
        const response = await fetch(`${API_BASE_URL}${endpoint}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || `API Error: ${response.statusText}`);
        }
        return response.json();
    },
    
    async delete(endpoint) {
        const response = await fetch(`${API_BASE_URL}${endpoint}`, {
            method: 'DELETE',
        });
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || `API Error: ${response.statusText}`);
        }
        return null;
    },
};

// Utility: Toast notifications
const Toast = ({ message, type, onClose }) => {
    React.useEffect(() => {
        const timer = setTimeout(onClose, 3000);
        return () => clearTimeout(timer);
    }, [onClose]);
    
    return (
        <div className={`toast ${type}`}>
            <span>{message}</span>
        </div>
    );
};

// Component: Workflow List
const WorkflowList = ({ onSelectWorkflow, onNewWorkflow }) => {
    const [workflows, setWorkflows] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);
    
    const loadWorkflows = async () => {
        try {
            setLoading(true);
            setError(null);
            const data = await api.get('/api/workflows');
            setWorkflows(data);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };
    
    React.useEffect(() => {
        loadWorkflows();
    }, []);
    
    const handleDelete = async (workflowId, e) => {
        e.stopPropagation();
        if (!confirm('Are you sure you want to delete this workflow?')) {
            return;
        }
        
        try {
            await api.delete(`/api/workflows/${workflowId}`);
            await loadWorkflows();
        } catch (err) {
            alert(`Failed to delete workflow: ${err.message}`);
        }
    };
    
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString();
    };
    
    if (loading) {
        return <div className="loading">Loading workflows...</div>;
    }
    
    if (error) {
        return (
            <div className="error">
                <div className="error-title">Error loading workflows</div>
                <div>{error}</div>
                <button className="btn btn-primary" onClick={loadWorkflows} style={{ marginTop: '1rem' }}>
                    Retry
                </button>
            </div>
        );
    }
    
    return (
        <div>
            <div className="workflow-header">
                <h2>Workflows</h2>
                <button className="btn btn-primary" onClick={onNewWorkflow}>
                    + New Workflow
                </button>
            </div>
            
            {workflows.length === 0 ? (
                <div className="empty-state">
                    <div className="empty-state-icon">📋</div>
                    <h3>No workflows yet</h3>
                    <p>Create your first workflow to get started</p>
                    <button className="btn btn-primary" onClick={onNewWorkflow} style={{ marginTop: '1rem' }}>
                        Create Workflow
                    </button>
                </div>
            ) : (
                <ul className="workflow-list">
                    {workflows.map(workflow => (
                        <li
                            key={workflow.id}
                            className="workflow-item"
                            onClick={() => onSelectWorkflow(workflow.id)}
                        >
                            <div className="workflow-info">
                                <h3>{workflow.name}</h3>
                                <p>{workflow.description || 'No description'}</p>
                                <div className="workflow-meta">
                                    Created: {formatDate(workflow.created_date)} | 
                                    Modified: {formatDate(workflow.modified_date)}
                                    {workflow.is_draft && <span className="ml-2 text-xs font-medium" style={{ color: 'oklch(0.7 0.15 75)' }}>• Draft</span>}
                                </div>
                            </div>
                            <div className="workflow-actions">
                                <button className="btn-view" onClick={() => onSelectWorkflow(workflow.id)}>
                                    View
                                </button>
                                <button className="btn-delete" onClick={(e) => handleDelete(workflow.id, e)}>
                                    Delete
                                </button>
                            </div>
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
};

// Component: Eval Cases Tab
const EvalCasesTab = ({ workflowId }) => {
    const [cases, setCases] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [showCreateModal, setShowCreateModal] = React.useState(false);
    
    const loadCases = async () => {
        try {
            setLoading(true);
            const data = await api.get(`/api/workflows/${workflowId}/eval-cases`);
            setCases(data);
        } catch (err) {
            console.error('Error loading eval cases:', err);
        } finally {
            setLoading(false);
        }
    };
    
    React.useEffect(() => {
        loadCases();
    }, [workflowId]);
    
    const handleRunCase = async (caseId) => {
        try {
            await api.post('/api/eval-cases/run', {
                eval_case_ids: [caseId]
            });
            alert('Test case queued for execution!');
            setTimeout(loadCases, 1000);
        } catch (err) {
            alert(`Failed to run test case: ${err.message}`);
        }
    };
    
    const handleDeleteCase = async (caseId) => {
        if (!confirm('Delete this test case?')) return;
        try {
            await api.delete(`/api/eval-cases/${caseId}`);
            loadCases();
        } catch (err) {
            alert(`Failed to delete: ${err.message}`);
        }
    };
    
    const [generating, setGenerating] = React.useState(false);
    const [runningBatch, setRunningBatch] = React.useState(false);
    
    const handleGenerateTestCases = async () => {
        try {
            setGenerating(true);
            const result = await api.post(`/api/workflows/${workflowId}/eval-cases/generate`, {
                count: 10
            });
            alert(`Generated ${result.count} test cases successfully!`);
            loadCases();
        } catch (err) {
            alert(`Failed to generate test cases: ${err.message}`);
        } finally {
            setGenerating(false);
        }
    };
    
    const handleRunAllTests = async () => {
        if (cases.length === 0) {
            alert('No test cases to run!');
            return;
        }
        
        if (!confirm(`Run all ${cases.length} test cases in parallel?`)) return;
        
        try {
            setRunningBatch(true);
            const caseIds = cases.map(c => c.id);
            const result = await api.post('/api/eval-cases/run-batch', {
                eval_case_ids: caseIds
            });
            alert(`Started ${result.count} test cases! Check the dashboard for results.`);
            setTimeout(loadCases, 1000);
        } catch (err) {
            alert(`Failed to run batch: ${err.message}`);
        } finally {
            setRunningBatch(false);
        }
    };
    
    if (loading) return <div className="loading">Loading test cases...</div>;
    
    return (
        <div className="eval-cases-tab">
            <div className="eval-cases-header">
                <h3>Test Cases</h3>
                <div className="flex gap-2">
                    <button 
                        className="btn btn-secondary" 
                        onClick={handleGenerateTestCases}
                        disabled={generating}
                    >
                        {generating ? '⏳ Generating...' : '🤖 Generate 10 Tests'}
                    </button>
                    {cases.length > 0 && (
                        <button 
                            className="btn btn-success" 
                            onClick={handleRunAllTests}
                            disabled={runningBatch}
                        >
                            {runningBatch ? '⏳ Starting...' : `▶️ Run All (${cases.length})`}
                        </button>
                    )}
                    <button className="btn btn-primary" onClick={() => setShowCreateModal(true)}>
                        + New Test Case
                    </button>
                </div>
            </div>
            
            {cases.length === 0 ? (
                <div className="empty-state">
                    <p>No test cases yet. Create one to get started!</p>
                </div>
            ) : (
                <table className="eval-cases-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Runs</th>
                            <th>Pass Rate</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {cases.map(testCase => (
                            <tr key={testCase.id}>
                                <td>
                                    <strong>{testCase.name}</strong>
                                    {testCase.description && <div className="text-xs text-muted-foreground mt-1">{testCase.description}</div>}
                                </td>
                                <td>{testCase.total_runs || 0}</td>
                                <td>
                                    {testCase.success_rate !== null && testCase.success_rate !== undefined ? 
                                        `${testCase.success_rate}%` : 'N/A'}
                                </td>
                                <td>
                                    <span className={testCase.is_active ? 'badge-active' : 'badge-inactive'}>
                                        {testCase.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                </td>
                                <td>
                                    <button className="btn-small btn-primary" onClick={() => handleRunCase(testCase.id)}>
                                        Run
                                    </button>
                                    <button className="btn-small btn-delete" onClick={() => handleDeleteCase(testCase.id)}>
                                        Delete
                                    </button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            )}
            
            {showCreateModal && (
                <CreateEvalCaseModal
                    workflowId={workflowId}
                    onClose={() => setShowCreateModal(false)}
                    onCreated={() => {
                        setShowCreateModal(false);
                        loadCases();
                    }}
                />
            )}
        </div>
    );
};

// Component: Create Eval Case Modal
const CreateEvalCaseModal = ({ workflowId, onClose, onCreated }) => {
    const [name, setName] = React.useState('');
    const [description, setDescription] = React.useState('');
    const [inputVars, setInputVars] = React.useState('[{"variable_name": "topic", "variable_value": "test", "variable_type": "String"}]');
    const [expectedOutput, setExpectedOutput] = React.useState('{}');
    const [creating, setCreating] = React.useState(false);
    
    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            setCreating(true);
            await api.post(`/api/workflows/${workflowId}/eval-cases`, {
                name,
                description,
                input_variables: JSON.parse(inputVars),
                expected_output: JSON.parse(expectedOutput),
                tags: []
            });
            onCreated();
        } catch (err) {
            alert(`Failed to create test case: ${err.message}`);
        } finally {
            setCreating(false);
        }
    };
    
    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                <h3>Create Test Case</h3>
                <form onSubmit={handleSubmit}>
                    <div className="form-group">
                        <label>Name *</label>
                        <input value={name} onChange={(e) => setName(e.target.value)} required />
                    </div>
                    <div className="form-group">
                        <label>Description</label>
                        <input value={description} onChange={(e) => setDescription(e.target.value)} />
                    </div>
                    <div className="form-group">
                        <label>Input Variables (JSON Array) *</label>
                        <textarea value={inputVars} onChange={(e) => setInputVars(e.target.value)} rows="4" required />
                    </div>
                    <div className="form-group">
                        <label>Expected Output (JSON Object)</label>
                        <textarea value={expectedOutput} onChange={(e) => setExpectedOutput(e.target.value)} rows="3" />
                    </div>
                    <div className="form-actions">
                        <button type="submit" className="btn btn-primary" disabled={creating}>
                            {creating ? 'Creating...' : 'Create'}
                        </button>
                        <button type="button" className="btn btn-secondary" onClick={onClose}>Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    );
};

// Component: Dashboard Page
const DashboardPage = ({ onViewRun }) => {
    const [status, setStatus] = React.useState(null);
    const [loading, setLoading] = React.useState(true);
    const [cancelling, setCancelling] = React.useState(new Set());
    
    const loadStatus = async () => {
        try {
            const data = await api.get('/api/dashboard/status');
            setStatus(data);
        } catch (err) {
            console.error('Error loading dashboard status:', err);
        } finally {
            setLoading(false);
        }
    };
    
    const handleCancelRun = async (evalRunId, flowExecutionId) => {
        if (!confirm('Are you sure you want to cancel this execution?')) {
            return;
        }
        
        setCancelling(prev => new Set(prev).add(evalRunId));
        try {
            await api.post(`/api/eval-runs/${evalRunId}/cancel`, { flow_execution_id: flowExecutionId });
            await loadStatus(); // Refresh immediately
        } catch (err) {
            console.error('Error cancelling execution:', err);
            alert('Failed to cancel execution: ' + err.message);
        } finally {
            setCancelling(prev => {
                const next = new Set(prev);
                next.delete(evalRunId);
                return next;
            });
        }
    };
    
    React.useEffect(() => {
        loadStatus();
        const interval = setInterval(loadStatus, 2000); // Update every 2 seconds
        return () => clearInterval(interval);
    }, []);
    
    if (loading) return <div className="loading">Loading dashboard...</div>;
    if (!status) return <div className="error">Failed to load dashboard</div>;
    
    return (
        <div className="dashboard">
            <h2>System Dashboard</h2>
            
            <div className="dashboard-grid">
                <div className="dashboard-card">
                    <h3>System Health</h3>
                    <div className="health-indicators">
                        <div className="health-item">
                            <span>Database:</span>
                            <span className={`health-${status.system_health.database}`}>
                                {status.system_health.database}
                            </span>
                        </div>
                        <div className="health-item">
                            <span>RabbitMQ:</span>
                            <span className={`health-${status.system_health.rabbitmq}`}>
                                {status.system_health.rabbitmq}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div className="dashboard-card">
                    <h3>Queue Status</h3>
                    {Object.entries(status.queues).map(([queueName, stats]) => (
                        <div key={queueName} className="queue-stat">
                            <strong>{queueName}:</strong> {stats.pending} pending, {stats.consumers} consumers
                        </div>
                    ))}
                </div>
            </div>
            
            <div className="dashboard-section">
                <h3>Active Eval Runs ({status.active_runs.length})</h3>
                {status.active_runs.length === 0 ? (
                    <p>No active runs</p>
                ) : (
                    <table className="dashboard-table">
                        <thead>
                            <tr>
                                <th>Test Case</th>
                                <th>Workflow</th>
                                <th>Status</th>
                                <th>Elapsed</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {status.active_runs.map(run => (
                                <tr key={run.eval_run_id} className="clickable-row" onClick={() => onViewRun(run.eval_run_id)}>
                                    <td>{run.test_case_name}</td>
                                    <td>{run.workflow_name}</td>
                                    <td><span className="badge-running">{run.status}</span></td>
                                    <td>{Math.round(run.elapsed_seconds)}s</td>
                                    <td>
                                        <button
                                            className="btn-cancel-small"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleCancelRun(run.eval_run_id, run.flow_execution_id);
                                            }}
                                            disabled={cancelling.has(run.eval_run_id)}
                                        >
                                            {cancelling.has(run.eval_run_id) ? 'Cancelling...' : 'Cancel'}
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                )}
            </div>
            
            <div className="dashboard-section">
                <h3>Recent Completions</h3>
                {status.recent_completions.length === 0 ? (
                    <p>No recent completions</p>
                ) : (
                    <table className="dashboard-table">
                        <thead>
                            <tr>
                                <th>Test Case</th>
                                <th>Workflow</th>
                                <th>Status</th>
                                <th>Result</th>
                                <th>Duration</th>
                            </tr>
                        </thead>
                        <tbody>
                            {status.recent_completions.map(run => (
                                <tr key={run.eval_run_id} className="clickable-row" onClick={() => onViewRun(run.eval_run_id)}>
                                    <td>{run.test_case_name}</td>
                                    <td>{run.workflow_name}</td>
                                    <td><span className={`badge-${run.execution_status.toLowerCase()}`}>{run.execution_status}</span></td>
                                    <td>
                                        {run.passed !== null && (
                                            <span className={run.passed ? 'badge-passed' : 'badge-failed'}>
                                                {run.passed ? 'PASSED' : 'FAILED'}
                                            </span>
                                        )}
                                    </td>
                                    <td>{run.duration_ms ? `${Math.round(run.duration_ms)}ms` : 'N/A'}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                )}
            </div>
        </div>
    );
};

// Component: Workflow Detail
const WorkflowDetail = ({ workflowId, onBack, onWorkflowUpdated }) => {
    const [workflow, setWorkflow] = React.useState(null);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);
    const [isEditing, setIsEditing] = React.useState(false);
    const [editedJson, setEditedJson] = React.useState('');
    const [editedName, setEditedName] = React.useState('');
    const [saving, setSaving] = React.useState(false);
    const [activeTab, setActiveTab] = React.useState('definition'); // 'definition' or 'eval-cases'
    
    const loadWorkflow = async () => {
        try {
            setLoading(true);
            setError(null);
            
            const data = await api.get(`/api/workflows/${workflowId}`);
            setWorkflow(data);
            setEditedJson(JSON.stringify(data.workflow, null, 2));
            setEditedName(data.name);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };
    
    React.useEffect(() => {
        loadWorkflow();
    }, [workflowId]);
    
    const handleSave = async () => {
        try {
            setSaving(true);
            
            // Parse JSON and send with name separately
            const jsonData = JSON.parse(editedJson);
            const response = await api.put(`/api/workflows/${workflowId}`, {
                name: editedName,
                workflow: jsonData
            });
            
            // Update with the new workflow version data directly from response
            setWorkflow(response);
            setEditedJson(JSON.stringify(response.workflow, null, 2));
            setEditedName(response.name);
            setIsEditing(false);
            if (onWorkflowUpdated) onWorkflowUpdated();
        } catch (err) {
            alert(`Failed to save workflow: ${err.message}`);
        } finally {
            setSaving(false);
        }
    };
    
    const handleCancel = () => {
        setEditedJson(workflow ? JSON.stringify(workflow.workflow, null, 2) : '');
        setEditedName(workflow ? workflow.name : '');
        setIsEditing(false);
    };
    
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString();
    };
    
    if (loading) {
        return <div className="loading">Loading workflow...</div>;
    }
    
    if (error) {
        return (
            <div className="error">
                <div className="error-title">Error loading workflow</div>
                <div>{error}</div>
                <button className="btn btn-secondary" onClick={onBack} style={{ marginTop: '1rem' }}>
                    Back to List
                </button>
            </div>
        );
    }
    
    if (!workflow) {
        return <div>Workflow not found</div>;
    }
    
    const displayContent = workflow.workflow ? JSON.stringify(workflow.workflow, null, 2) : 'Loading...';
    
    return (
        <div>
            <div className="workflow-tabs">
                <button 
                    className={`tab ${activeTab === 'definition' ? 'active' : ''}`}
                    onClick={() => setActiveTab('definition')}
                >
                    Workflow Definition
                </button>
                <button 
                    className={`tab ${activeTab === 'eval-cases' ? 'active' : ''}`}
                    onClick={() => setActiveTab('eval-cases')}
                >
                    Test Cases
                </button>
            </div>
            
            {activeTab === 'eval-cases' ? (
                <>
                    <div className="workflow-detail-actions" style={{marginTop: '1rem'}}>
                        <button className="btn btn-secondary" onClick={onBack}>
                            ← Back to List
                        </button>
                    </div>
                    <EvalCasesTab workflowId={workflowId} />
                </>
            ) : (
            <>
            <div className="workflow-detail-header">
                <div className="workflow-detail-info">
                    {isEditing ? (
                        <div className="form-group">
                            <label htmlFor="workflow-name" className="text-sm font-medium">
                                Workflow Name
                            </label>
                            <input
                                id="workflow-name"
                                type="text"
                                value={editedName}
                                onChange={(e) => setEditedName(e.target.value)}
                                className="w-full"
                            />
                        </div>
                    ) : (
                        <h2>{workflow.name}</h2>
                    )}
                    <p>{workflow.workflow?.description || 'No description'}</p>
                </div>
                <div className="workflow-detail-meta">
                    <div><strong>Namespace:</strong> {workflow.namespace || 'default'}</div>
                    <div><strong>Created:</strong> {formatDate(workflow.created_at)}</div>
                    <div><strong>Modified:</strong> {formatDate(workflow.modified_at)}</div>
                    {workflow.is_draft && <div className="font-medium" style={{ color: 'oklch(0.7 0.15 75)' }}><strong>Status:</strong> Draft</div>}
                </div>
            </div>
            
            <div className="workflow-detail-actions">
                <button className="btn btn-secondary" onClick={onBack}>
                    ← Back to List
                </button>
                
                {!isEditing ? (
                    <button className="btn btn-primary" onClick={() => setIsEditing(true)}>
                        Edit Workflow
                    </button>
                ) : (
                    <>
                        <button className="btn btn-success" onClick={handleSave} disabled={saving}>
                            {saving ? 'Saving...' : 'Save Changes'}
                        </button>
                        <button className="btn btn-secondary" onClick={handleCancel} disabled={saving}>
                            Cancel
                        </button>
                    </>
                )}
            </div>
            
            <div className="workflow-editor">
                <h3>Workflow Definition (JSON)</h3>
                <div className="workflow-editor-container">
                    <div className="workflow-editor-toolbar">
                        <span style={{ fontSize: '0.875rem', color: '#666' }}>
                            {isEditing ? 'Editing Mode' : 'View Mode'}
                        </span>
                        <span style={{ fontSize: '0.875rem', color: '#3b82f6', marginLeft: '1rem' }}>
                            ℹ️ JSON format: Metadata handled separately, Teams inline, Delegates/Sub-workflows referenced
                        </span>
                    </div>
                    {isEditing ? (
                        <textarea
                            className="workflow-content editable w-full"
                            value={editedJson}
                            onChange={(e) => setEditedJson(e.target.value)}
                            style={{
                                minHeight: '500px',
                                border: 'none',
                                resize: 'vertical'
                            }}
                        />
                    ) : (
                        <pre className="workflow-content">{displayContent}</pre>
                    )}
                </div>
            </div>
            </>
            )}
        </div>
    );
};

// Component: New Workflow Form
const NewWorkflowForm = ({ onBack, onWorkflowCreated }) => {
    const [workflowName, setWorkflowName] = React.useState('New Workflow');
    const [workflowJson, setWorkflowJson] = React.useState(JSON.stringify({
        "description": "Description of the workflow",
        "variables": {
            "input": {},
            "output": {}
        },
        "steps": []
    }, null, 2));
    const [creating, setCreating] = React.useState(false);
    const [error, setError] = React.useState(null);
    
    const handleSubmit = async (e) => {
        e.preventDefault();
        
        try {
            setCreating(true);
            setError(null);
            
            const workflow = JSON.parse(workflowJson);
            
            await api.post('/api/workflows', { 
                name: workflowName,
                workflow: workflow 
            });
            
            if (onWorkflowCreated) onWorkflowCreated();
        } catch (err) {
            setError(err.message);
        } finally {
            setCreating(false);
        }
    };
    
    return (
        <div>
            <div className="workflow-header">
                <h2>Create New Workflow</h2>
            </div>
            
            {error && (
                <div className="error">
                    <div className="error-title">Error creating workflow</div>
                    <div>{error}</div>
                </div>
            )}
            
            <form onSubmit={handleSubmit}>
                <div className="form-group">
                    <label htmlFor="name">Workflow Name *</label>
                    <input
                        id="name"
                        type="text"
                        value={workflowName}
                        onChange={(e) => setWorkflowName(e.target.value)}
                        placeholder="Enter workflow name"
                        required
                    />
                </div>
                
                <div className="form-group">
                    <label htmlFor="json">Workflow Definition (JSON) *</label>
                    <div style={{ fontSize: '0.875rem', color: '#666', marginBottom: '0.5rem' }}>
                        Note: Metadata like name, created_at, modified_at are handled separately. Only include description, variables, and steps.
                    </div>
                    <textarea
                        id="json"
                        value={workflowJson}
                        onChange={(e) => setWorkflowJson(e.target.value)}
                        placeholder="Enter JSON workflow definition"
                        required
                    />
                </div>
                
                <div className="form-actions">
                    <button type="submit" className="btn btn-primary" disabled={creating}>
                        {creating ? 'Creating...' : 'Create Workflow'}
                    </button>
                    <button type="button" className="btn btn-secondary" onClick={onBack} disabled={creating}>
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    );
};

// Component: Workflow Spec Generator
const WorkflowSpecGenerator = () => {
    const [workflowRequest, setWorkflowRequest] = React.useState('');
    const [taskId, setTaskId] = React.useState(null);
    const [status, setStatus] = React.useState(null);
    const [generating, setGenerating] = React.useState(false);
    const [error, setError] = React.useState(null);
    const [streamingContent, setStreamingContent] = React.useState({}); // Track streaming messages by stream_id
    const [currentStep, setCurrentStep] = React.useState(''); // Track current step
    const messagesEndRef = React.useRef(null);
    
    const handleSubmit = async (e) => {
        e.preventDefault();
        setError(null);
        setGenerating(true);
        
        try {
            const response = await api.post('/api/workflow-spec/generate', {
                workflow_request: workflowRequest
            });
            
            setTaskId(response.task_id);
            setStatus(response);
        } catch (err) {
            setError(err.message);
            setGenerating(false);
        }
    };
    
    // Auto-scroll to bottom when new messages arrive
    React.useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [status?.messages]);
    
    // Stream status updates using SSE
    React.useEffect(() => {
        if (!taskId) return;
        
        // Initialize status with messages array
        setStatus(prev => prev || { messages: [], progress: 0 });
        
        // Create EventSource for SSE
        const eventSource = new EventSource(`${API_BASE_URL}/api/workflow-spec/stream/${taskId}`);
        
        eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                
                if (data.type === 'message') {
                    const msg = data.message;
                    
                    // Track current step
                    if (msg.type === 'thinking' && msg.content && msg.content.includes('Step')) {
                        setCurrentStep(msg.content);
                    }
                    
                    // Handle streaming message types
                    if (msg.type === 'agent_stream_start') {
                        // Agent started streaming - create placeholder
                        setStreamingContent(prev => ({
                            ...prev,
                            [msg.stream_id]: {
                                agent: msg.agent,
                                content: '',
                                isComplete: false
                            }
                        }));
                    } else if (msg.type === 'agent_stream') {
                        // Update streaming content
                        setStreamingContent(prev => ({
                            ...prev,
                            [msg.stream_id]: {
                                agent: msg.agent,
                                content: msg.content,
                                isComplete: msg.is_complete
                            }
                        }));
                    } else if (msg.type === 'agent_stream_end') {
                        // Stream finished - remove from streaming and add to messages
                        setStreamingContent(prev => {
                            const newContent = { ...prev };
                            delete newContent[msg.stream_id];
                            return newContent;
                        });
                        // Add final message to history
                        setStatus(prev => ({
                            ...prev,
                            messages: [...(prev?.messages || []), {
                                type: 'agent_response',
                                agent: msg.agent,
                                content: msg.final_content,
                                timestamp: new Date().toISOString()
                            }]
                        }));
                    } else {
                        // Regular message
                        setStatus(prev => ({
                            ...prev,
                            messages: [...(prev?.messages || []), msg]
                        }));
                    }
                } else if (data.type === 'progress') {
                    // Update progress
                    setStatus(prev => ({
                        ...prev,
                        progress: data.progress
                    }));
                } else if (data.type === 'status') {
                    // Final status update
                    setStatus(prev => ({
                        ...prev,
                        status: data.status,
                        download_url: data.download_url,
                        error: data.error
                    }));
                    setGenerating(false);
                    eventSource.close();
                } else if (data.type === 'error') {
                    setError(data.message);
                    setGenerating(false);
                    eventSource.close();
                }
            } catch (err) {
                console.error('Error parsing SSE message:', err);
            }
        };
        
        eventSource.onerror = (err) => {
            console.error('SSE connection error:', err);
            eventSource.close();
        };
        
        // Cleanup
        return () => {
            eventSource.close();
        };
    }, [taskId]);
    
    const handleReset = () => {
        setWorkflowRequest('');
        setTaskId(null);
        setStatus(null);
        setGenerating(false);
        setError(null);
    };
    
    const getProgressColor = (progress) => {
        if (progress < 30) return '#ef4444'; // red
        if (progress < 70) return '#f59e0b'; // orange
        return '#10b981'; // green
    };
    
    return (
        <div className="workflow-spec-generator">
            <div className="workflow-header">
                <h2>🤖 AI Workflow Specification Generator</h2>
                <p style={{ color: '#666', marginTop: '0.5rem' }}>
                    Describe a workflow requirement and let AI generate a comprehensive specification document
                </p>
            </div>
            
            {!taskId ? (
                <form onSubmit={handleSubmit} className="spec-generator-form">
                    <div className="form-group">
                        <label htmlFor="workflow-request">Workflow Requirement *</label>
                        <textarea
                            id="workflow-request"
                            value={workflowRequest}
                            onChange={(e) => setWorkflowRequest(e.target.value)}
                            placeholder="Example: Take an IC memo and produce a structured assessment of risks"
                            required
                            rows="4"
                            maxLength="2000"
                            style={{ resize: 'vertical' }}
                        />
                        <div style={{ fontSize: '0.875rem', color: '#666', marginTop: '0.25rem' }}>
                            {workflowRequest.length}/2000 characters
                        </div>
                    </div>
                    
                    {error && (
                        <div className="error" style={{ marginBottom: '1rem' }}>
                            <div className="error-title">Error</div>
                            <div>{error}</div>
                        </div>
                    )}
                    
                    <div className="form-actions">
                        <button type="submit" className="btn btn-primary" disabled={generating || !workflowRequest.trim()}>
                            ✨ Generate Specification
                        </button>
                    </div>
                </form>
            ) : (
                <div className="spec-generation-status">
                    <div className="status-card">
                        <div className="status-header">
                            <h3>Generation Progress</h3>
                            {status && (
                                <span className={`badge-${status.status}`} style={{ marginLeft: '1rem' }}>
                                    {status.status.toUpperCase()}
                                </span>
                            )}
                        </div>
                        
                        {status && (
                            <>
                                <div className="progress-bar-container" style={{ marginTop: '1rem', marginBottom: '1.5rem' }}>
                                    <div 
                                        className="progress-bar" 
                                        style={{ 
                                            width: `${status.progress}%`,
                                            background: getProgressColor(status.progress),
                                            height: '24px',
                                            borderRadius: '8px',
                                            transition: 'width 0.3s ease, background 0.3s ease',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            color: 'white',
                                            fontWeight: '600',
                                            fontSize: '0.875rem'
                                        }}
                                    >
                                        {status.progress}%
                                    </div>
                                </div>
                                
                                <div className="request-info" style={{ 
                                    background: '#f9fafb', 
                                    padding: '1rem', 
                                    borderRadius: '8px',
                                    marginBottom: '1.5rem'
                                }}>
                                    <strong>Request:</strong>
                                    <p style={{ marginTop: '0.5rem', color: '#666' }}>{status.workflow_request}</p>
                                </div>
                                
                                <div className="messages-container">
                                    <h4>🤖 Agent Conversation</h4>
                                    <div className="messages-list" style={{ 
                                        maxHeight: '500px', 
                                        overflowY: 'auto',
                                        border: '1px solid #e5e7eb',
                                        borderRadius: '8px',
                                        padding: '1rem',
                                        background: '#f9fafb'
                                    }}>
                                        {status.messages && status.messages.length > 0 ? (
                                            <>
                                                {status.messages.filter(msg => 
                                                    msg.type === 'agent_response' || 
                                                    (msg.type === 'thinking' && msg.content.includes('Step'))
                                                ).map((msg, idx) => (
                                                    <div key={idx}>
                                                        {msg.type === 'thinking' && msg.content.includes('Step') ? (
                                                            <div style={{ 
                                                                fontSize: '0.85rem',
                                                                fontWeight: '700',
                                                                color: '#4b5563',
                                                                padding: '1rem 0 0.75rem 0',
                                                                marginTop: idx > 0 ? '1rem' : '0',
                                                                borderTop: idx > 0 ? '2px solid #e5e7eb' : 'none'
                                                            }}>
                                                                {msg.content}
                                                            </div>
                                                        ) : msg.type === 'agent_response' ? (
                                                            <div 
                                                                style={{
                                                                    padding: '1rem',
                                                                    marginBottom: '0.5rem',
                                                                    borderRadius: '8px',
                                                                    background: 'white',
                                                                    border: '2px solid #e0e7ff',
                                                                    boxShadow: '0 2px 8px rgba(102, 126, 234, 0.1)',
                                                                    animation: 'fadeIn 0.3s ease-in'
                                                                }}
                                                            >
                                                                <div style={{ 
                                                                    fontWeight: '700', 
                                                                    fontSize: '0.9rem',
                                                                    color: '#667eea',
                                                                    marginBottom: '0.75rem',
                                                                    padding: '0.5rem 0.75rem',
                                                                    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
                                                                    borderRadius: '6px',
                                                                    borderLeft: '4px solid #667eea'
                                                                }}>
                                                                    🤖 {msg.agent}
                                                                </div>
                                                                <div style={{ 
                                                                    lineHeight: '1.6',
                                                                    color: '#374151',
                                                                    padding: '0 0.5rem',
                                                                    fontSize: '0.875rem',
                                                                    whiteSpace: 'pre-wrap'
                                                                }}>
                                                                    {msg.content}
                                                                </div>
                                                            </div>
                                                        ) : null}
                                                    </div>
                                                ))}
                                                
                                                {/* Streaming agent responses */}
                                                {Object.entries(streamingContent).map(([streamId, streamData], idx) => (
                                                    <div key={streamId}>
                                                        {/* Show current step if this is the first streaming agent */}
                                                        {idx === 0 && currentStep && !status.messages?.some(m => m.type === 'thinking' && m.content === currentStep) && (
                                                            <div style={{ 
                                                                fontSize: '0.85rem',
                                                                fontWeight: '700',
                                                                color: '#4b5563',
                                                                padding: '1rem 0 0.75rem 0',
                                                                marginTop: status.messages?.length > 0 ? '1rem' : '0',
                                                                borderTop: status.messages?.length > 0 ? '2px solid #e5e7eb' : 'none'
                                                            }}>
                                                                {currentStep}
                                                            </div>
                                                        )}
                                                        <div 
                                                            style={{
                                                                padding: '1rem',
                                                                marginBottom: '0.5rem',
                                                                borderRadius: '8px',
                                                                background: 'white',
                                                                border: '2px solid #e0e7ff',
                                                                boxShadow: '0 2px 8px rgba(102, 126, 234, 0.15)',
                                                                animation: 'fadeIn 0.3s ease-in'
                                                            }}
                                                        >
                                                            <div style={{ 
                                                                fontWeight: '700', 
                                                                fontSize: '0.9rem',
                                                                color: '#667eea',
                                                                marginBottom: '0.75rem',
                                                                padding: '0.5rem 0.75rem',
                                                                background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
                                                                borderRadius: '6px',
                                                                borderLeft: '4px solid #667eea',
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                gap: '0.5rem'
                                                            }}>
                                                                <span>🤖 {streamData.agent}</span>
                                                                {!streamData.isComplete && (
                                                                    <div className="typing-indicator" style={{ scale: '0.6', marginLeft: 'auto' }}>
                                                                        <span style={{ backgroundColor: '#667eea' }}></span>
                                                                        <span style={{ backgroundColor: '#667eea' }}></span>
                                                                        <span style={{ backgroundColor: '#667eea' }}></span>
                                                                    </div>
                                                                )}
                                                            </div>
                                                            <div style={{ 
                                                                fontSize: '0.875rem',
                                                                color: '#374151',
                                                                whiteSpace: 'pre-wrap',
                                                                lineHeight: '1.6',
                                                                padding: '0 0.5rem'
                                                            }}>
                                                                {streamData.content}
                                                                {!streamData.isComplete && <span style={{ animation: 'blink 1s infinite', color: '#667eea', fontWeight: 'bold' }}>▌</span>}
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))}
                                                
                                                {generating && status.status !== 'completed' && status.status !== 'error' && Object.keys(streamingContent).length === 0 && (
                                                    <div style={{
                                                        padding: '0.75rem',
                                                        marginBottom: '0.75rem',
                                                        borderRadius: '6px',
                                                        background: '#f3f4f6',
                                                        borderLeft: '4px solid #6b7280',
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        gap: '0.5rem'
                                                    }}>
                                                        <div className="typing-indicator">
                                                            <span></span>
                                                            <span></span>
                                                            <span></span>
                                                        </div>
                                                        <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>Agent is working...</span>
                                                    </div>
                                                )}
                                                <div ref={messagesEndRef} />
                                            </>
                                        ) : (
                                            <p style={{ color: '#9ca3af', textAlign: 'center', padding: '2rem' }}>
                                                Waiting for agent to start...
                                            </p>
                                        )}
                                    </div>
                                </div>
                                
                                {status.error && (
                                    <div className="error" style={{ marginTop: '1.5rem' }}>
                                        <div className="error-title">Generation Failed</div>
                                        <div>{status.error}</div>
                                    </div>
                                )}
                                
                                {status.status === 'completed' && status.download_url && (
                                    <div className="download-section" style={{ 
                                        marginTop: '1.5rem',
                                        padding: '1.5rem',
                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                        borderRadius: '12px',
                                        color: 'white',
                                        textAlign: 'center'
                                    }}>
                                        <div style={{ fontSize: '3rem', marginBottom: '0.5rem' }}>📄</div>
                                        <h3 style={{ marginBottom: '1rem' }}>Specification Ready!</h3>
                                        <a 
                                            href={`${API_BASE_URL}${status.download_url}`}
                                            download
                                            style={{
                                                display: 'inline-block',
                                                padding: '0.75rem 2rem',
                                                background: 'white',
                                                color: '#667eea',
                                                borderRadius: '8px',
                                                textDecoration: 'none',
                                                fontWeight: '600',
                                                fontSize: '1rem',
                                                transition: 'transform 0.2s',
                                                boxShadow: '0 4px 12px rgba(0,0,0,0.2)'
                                            }}
                                            onMouseEnter={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
                                            onMouseLeave={(e) => e.currentTarget.style.transform = 'scale(1)'}
                                        >
                                            ⬇️ Download Word Document
                                        </a>
                                    </div>
                                )}
                                
                                <div className="form-actions" style={{ marginTop: '1.5rem' }}>
                                    <button 
                                        type="button" 
                                        className="btn btn-secondary" 
                                        onClick={handleReset}
                                    >
                                        ← Generate Another Specification
                                    </button>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

// Component: Run Detail Page
const RunDetailPage = ({ runId, onBack }) => {
    const [run, setRun] = React.useState(null);
    const [logs, setLogs] = React.useState(null);
    const [artifacts, setArtifacts] = React.useState({ artifacts: [], artifacts_dir: '' });
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);
    const [isRefreshing, setIsRefreshing] = React.useState(false);
    
    const loadRun = async (isAutoRefresh = false) => {
        try {
            if (!isAutoRefresh) {
                setLoading(true);
            } else {
                setIsRefreshing(true);
            }
            setError(null);
            const data = await api.get(`/api/eval-runs/${runId}/details`);
            setRun(data);
        } catch (err) {
            setError(err.message);
        } finally {
            if (!isAutoRefresh) {
                setLoading(false);
            } else {
                setIsRefreshing(false);
            }
        }
    };
    
    const loadLogs = async () => {
        try {
            const logsData = await api.get(`/api/eval-runs/${runId}/logs`);
            setLogs(logsData);
        } catch (err) {
            console.error('Error loading logs:', err);
            setLogs({ found: false, error: err.message });
        }
    };
    
    const loadArtifacts = async () => {
        try {
            const artifactsData = await api.get(`/api/eval-runs/${runId}/artifacts`);
            setArtifacts(artifactsData);
        } catch (err) {
            console.error('Error loading artifacts:', err);
            setArtifacts({ artifacts: [], artifacts_dir: '', error: err.message });
        }
    };
    
    React.useEffect(() => {
        loadRun(false);
        loadLogs();
        loadArtifacts();
        
        // Auto-refresh for active runs every second
        let interval = null;
        if (run && ['PENDING', 'RUNNING'].includes(run.execution_status)) {
            interval = setInterval(() => {
                loadRun(true);  // Pass true for auto-refresh
                loadLogs();
                loadArtifacts();
            }, 1000);
        }
        
        return () => {
            if (interval) clearInterval(interval);
        };
    }, [runId, run?.execution_status]);
    
    const formatTimestamp = (timestamp) => {
        if (!timestamp) return 'N/A';
        return new Date(timestamp).toLocaleString();
    };
    
    const formatDuration = (ms) => {
        if (!ms) return 'N/A';
        if (ms < 1000) return `${Math.round(ms)}ms`;
        return `${(ms / 1000).toFixed(2)}s`;
    };
    
    const renderVariableValue = (variable) => {
        if (typeof variable === 'object') {
            return <pre style={{ margin: 0, fontSize: '0.875rem' }}>{JSON.stringify(variable, null, 2)}</pre>;
        }
        return String(variable);
    };
    
    // Build conversation view from variables
    const buildConversationView = () => {
        if (!run) {
            return [];
        }
        
        const messages = [];
        
        // Add input variables first
        if (run.input_variables && run.input_variables.length > 0) {
            run.input_variables.forEach(varObj => {
                messages.push({
                    type: 'input',
                    name: varObj.variable_name,
                    value: varObj.variable_value,
                    stepId: varObj.step_id
                });
            });
        }
        
        // Add output variables
        if (run.output_variables && run.output_variables.length > 0) {
            run.output_variables.forEach(varObj => {
                const varName = varObj.variable_name;
                const varValue = varObj.variable_value;
                
                // Try to detect message types based on variable names
                if (varName.includes('message') || varName.includes('response') || varName.includes('output')) {
                    messages.push({
                        type: 'message',
                        name: varName,
                        value: varValue,
                        stepId: varObj.step_id
                    });
                } else if (varName.includes('tool') || varName.includes('function')) {
                    messages.push({
                        type: 'tool',
                        name: varName,
                        value: varValue,
                        stepId: varObj.step_id
                    });
                } else {
                    messages.push({
                        type: 'variable',
                        name: varName,
                        value: varValue,
                        stepId: varObj.step_id
                    });
                }
            });
        }
        
        return messages;
    };
    
    if (loading && !run) return <div className="loading">Loading run details...</div>;
    if (error && !run) return (
        <div className="error">
            <div className="error-title">Error loading run</div>
            <div>{error}</div>
            <button className="btn btn-secondary" onClick={onBack} style={{ marginTop: '1rem' }}>
                Back to Dashboard
            </button>
        </div>
    );
    if (!run) return <div>Run not found</div>;
    
    const conversationMessages = buildConversationView();
    const isActive = ['PENDING', 'RUNNING'].includes(run.execution_status);
    
    return (
        <div className="run-detail">
            <div className="run-detail-header">
                <div>
                    <h2>{run.eval_case_name}</h2>
                    <p className="text-muted-foreground">{run.workflow_name}</p>
                </div>
                <div className="flex gap-4 items-center">
                    {isActive && (
                        <span className="text-sm font-medium" style={{ color: 'oklch(0.7 0.15 75)' }}>
                            {isRefreshing ? '🔄 Refreshing...' : '● Auto-refresh enabled'}
                        </span>
                    )}
                    <button className="btn btn-secondary" onClick={onBack}>
                        ← Back to Dashboard
                    </button>
                </div>
            </div>
            
            <div className="run-detail-grid">
                <div className="run-detail-card">
                    <h3>Status</h3>
                    <div style={{ fontSize: '1.5rem', marginTop: '0.5rem' }}>
                        <span className={`badge-${run.execution_status.toLowerCase()}`}>{run.execution_status}</span>
                    </div>
                    {run.passed !== null && (
                        <div style={{ marginTop: '1rem' }}>
                            <span className={run.passed ? 'badge-passed' : 'badge-failed'}>
                                {run.passed ? 'PASSED' : 'FAILED'}
                            </span>
                        </div>
                    )}
                </div>
                
                <div className="run-detail-card">
                    <h3>Timing</h3>
                    <div className="mt-2 space-y-1 text-sm">
                        <div><strong>Started:</strong> {formatTimestamp(run.started_at)}</div>
                        <div><strong>Completed:</strong> {formatTimestamp(run.completed_at)}</div>
                        <div><strong>Duration:</strong> {formatDuration(run.duration_ms)}</div>
                    </div>
                </div>
                
                <div className="run-detail-card">
                    <h3>IDs</h3>
                    <div className="mt-2 space-y-1 text-xs font-mono break-all">
                        <div><strong>Run:</strong> {run.id}</div>
                        <div><strong>Execution:</strong> {run.flow_execution_id}</div>
                        {run.batch_run_id && <div><strong>Batch:</strong> {run.batch_run_id}</div>}
                    </div>
                </div>
            </div>
            
            {run.error_message && (
                <div className="run-detail-section error">
                    <h3>Error</h3>
                    <div className="mt-2">
                        <div><strong>Message:</strong> {run.error_message}</div>
                        {run.failure_detail && (
                            <pre className="mt-2 text-sm font-mono">
                                {JSON.stringify(run.failure_detail, null, 2)}
                            </pre>
                        )}
                    </div>
                </div>
            )}
            
            {run.validation_notes && (
                <div className="run-detail-section">
                    <h3>Validation Notes</h3>
                    <div className="mt-2">{run.validation_notes}</div>
                </div>
            )}
            
                    <div className="run-detail-section">
                        <h3>Conversation View</h3>
                        {conversationMessages.length === 0 ? (
                            <div className="mt-2">
                                <p className="text-muted-foreground mb-2">No conversation data available</p>
                                {isActive ? (
                                    <p className="text-sm" style={{ color: 'oklch(0.7 0.15 75)' }}>⏳ Execution in progress - outputs will appear here once the workflow completes steps</p>
                                ) : run.error_message ? (
                                    <p className="text-sm text-destructive">⚠️ Execution failed - check error details above</p>
                                ) : (
                                    <p className="text-sm text-muted-foreground">💡 This workflow may not have produced any output variables. Agents need to use save_variable tool to persist data.</p>
                                )}
                            </div>
                        ) : (
                            <>
                                {!run.output_variables || run.output_variables.length === 0 ? (
                                    <div className="mt-2 mb-4 p-3 rounded-lg" style={{ background: 'oklch(0.95 0.08 85)', border: '1px solid oklch(0.8 0.15 75)' }}>
                                        <p className="text-sm">
                                            ℹ️ Only showing input variables. {isActive ? 'Outputs will appear as the workflow executes.' : 'No outputs were generated during execution.'}
                                        </p>
                                    </div>
                                ) : null}
                                <div className="conversation-view">
                                    {conversationMessages.map((msg, idx) => (
                                        <div key={idx} className={`conversation-message ${msg.type}`}>
                                            <div className="conversation-message-header">
                                                <span className="conversation-message-type">{msg.type.toUpperCase()}</span>
                                                <span className="conversation-message-name">{msg.name}</span>
                                                {msg.stepId && <span className="conversation-message-step">Step: {msg.stepId}</span>}
                                            </div>
                                            <div className="conversation-message-content">
                                                {renderVariableValue(msg.value)}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </>
                        )}
                    </div>
            
            <div className="run-detail-section">
                <h3>Execution Logs</h3>
                {!logs ? (
                    <p className="text-muted-foreground mt-2">Loading logs...</p>
                ) : logs.found ? (
                    <div className="mt-4">
                        <div className="mb-2 text-sm text-muted-foreground">
                            <span>📁 Log File: {logs.log_path || logs.configured_path}</span>
                        </div>
                        <pre className="bg-primary text-primary-foreground p-4 rounded-lg overflow-auto max-h-[600px] text-sm leading-relaxed font-mono">
                            {logs.log_content}
                        </pre>
                    </div>
                ) : (
                    <div className="mt-4">
                        <p className="text-muted-foreground mb-2">No execution logs found</p>
                        {logs.configured_path && (
                            <p className="text-sm text-muted-foreground">
                                Expected at: {logs.configured_path}
                            </p>
                        )}
                        {logs.artifacts_dir && (
                            <p className="text-sm text-muted-foreground">
                                Artifacts directory: {logs.artifacts_dir}
                            </p>
                        )}
                        {isActive ? (
                            <p className="text-sm mt-2" style={{ color: 'oklch(0.7 0.15 75)' }}>
                                ⏳ Logs may appear as execution progresses
                            </p>
                        ) : (
                            <p className="text-sm text-muted-foreground mt-2">
                                💡 Log files may not be generated for all executions
                            </p>
                        )}
                    </div>
                )}
            </div>
            
            <div className="run-detail-tabs">
                <div className="run-detail-tab">
                    <h3>Input Variables</h3>
                    <div className="variables-list">
                        {run.input_variables && run.input_variables.length > 0 ? (
                            run.input_variables.map((varObj, idx) => (
                                <div key={idx} className="variable-item">
                                    <strong>{varObj.variable_name}:</strong>
                                    <div style={{ marginLeft: '1rem', marginTop: '0.25rem' }}>
                                        {renderVariableValue(varObj.variable_value)}
                                    </div>
                                </div>
                            ))
                        ) : (
                            <p style={{ color: '#666' }}>No input variables</p>
                        )}
                    </div>
                </div>
                
                <div className="run-detail-tab">
                    <h3>Output Variables</h3>
                    <div className="variables-list">
                        {run.output_variables && run.output_variables.length > 0 ? (
                            run.output_variables.map((varObj, idx) => (
                                <div key={idx} className="variable-item">
                                    <strong>{varObj.variable_name}:</strong>
                                    <div style={{ marginLeft: '1rem', marginTop: '0.25rem' }}>
                                        {renderVariableValue(varObj.variable_value)}
                                    </div>
                                </div>
                            ))
                        ) : (
                            <p style={{ color: '#666' }}>No output variables yet</p>
                        )}
                    </div>
                </div>
                
                <div className="run-detail-tab">
                    <h3>Expected Output</h3>
                    <div className="variables-list">
                        {run.expected_output ? (
                            <pre style={{ fontSize: '0.875rem' }}>{JSON.stringify(run.expected_output, null, 2)}</pre>
                        ) : (
                            <p style={{ color: '#666' }}>No expected output defined</p>
                        )}
                    </div>
                </div>
                
                {run.validation_output && (
                    <div className="run-detail-tab">
                        <h3>Validation Details</h3>
                        <div className="variables-list">
                            <pre style={{ fontSize: '0.875rem' }}>{JSON.stringify(run.validation_output, null, 2)}</pre>
                        </div>
                    </div>
                )}
                
                <div className="run-detail-tab full-width">
                    <h3>📁 Generated Files</h3>
                    <div className="variables-list">
                        {artifacts.artifacts && artifacts.artifacts.length > 0 ? (
                            <div style={{ width: '100%' }}>
                                <p style={{ fontSize: '0.875rem', color: '#666', marginBottom: '1.5rem' }}>
                                    Files created during workflow execution
                                </p>
                                <table style={{ 
                                    width: '100%', 
                                    borderCollapse: 'separate',
                                    borderSpacing: 0,
                                    border: '1px solid #e5e7eb',
                                    borderRadius: '8px',
                                    overflow: 'hidden'
                                }}>
                                    <thead>
                                        <tr style={{ 
                                            background: 'linear-gradient(to bottom, #f9fafb, #f3f4f6)',
                                            borderBottom: '2px solid #e5e7eb'
                                        }}>
                                            <th style={{ 
                                                padding: '1rem 1.5rem', 
                                                fontSize: '0.875rem', 
                                                fontWeight: '600', 
                                                color: '#374151',
                                                textAlign: 'left',
                                                letterSpacing: '0.025em'
                                            }}>FILE NAME</th>
                                            <th style={{ 
                                                padding: '1rem 1.5rem', 
                                                fontSize: '0.875rem', 
                                                fontWeight: '600', 
                                                color: '#374151',
                                                width: '100px',
                                                textAlign: 'center',
                                                letterSpacing: '0.025em'
                                            }}>TYPE</th>
                                            <th style={{ 
                                                padding: '1rem 1.5rem', 
                                                fontSize: '0.875rem', 
                                                fontWeight: '600', 
                                                color: '#374151',
                                                width: '100px',
                                                textAlign: 'right',
                                                letterSpacing: '0.025em'
                                            }}>SIZE</th>
                                            <th style={{ 
                                                padding: '1rem 1.5rem', 
                                                fontSize: '0.875rem', 
                                                fontWeight: '600', 
                                                color: '#374151',
                                                width: '140px',
                                                textAlign: 'center',
                                                letterSpacing: '0.025em'
                                            }}>ACTION</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {artifacts.artifacts.map((file, idx) => (
                                            <tr key={idx} style={{ 
                                                borderBottom: idx < artifacts.artifacts.length - 1 ? '1px solid #f3f4f6' : 'none',
                                                background: 'white',
                                                transition: 'background 0.2s'
                                            }}
                                            onMouseEnter={(e) => e.currentTarget.style.background = '#f9fafb'}
                                            onMouseLeave={(e) => e.currentTarget.style.background = 'white'}
                                            >
                                                <td style={{ padding: '1rem 1.5rem', verticalAlign: 'middle' }}>
                                                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                                                        <span style={{ fontSize: '1.5rem' }}>
                                                            {file.extension === '.md' ? '📝' : 
                                                             file.extension === '.docx' ? '📄' :
                                                             file.extension === '.pdf' ? '📕' :
                                                             file.extension === '.txt' ? '📃' : '📎'}
                                                        </span>
                                                        <span style={{ 
                                                            fontFamily: '"SF Mono", Monaco, "Courier New", monospace', 
                                                            fontSize: '0.95rem',
                                                            color: '#1f2937',
                                                            fontWeight: '500'
                                                        }}>
                                                            {file.name}
                                                        </span>
                                                    </div>
                                                </td>
                                                <td style={{ 
                                                    padding: '1rem 1.5rem', 
                                                    fontSize: '0.875rem', 
                                                    verticalAlign: 'middle', 
                                                    textAlign: 'center'
                                                }}>
                                                    <span style={{
                                                        display: 'inline-block',
                                                        padding: '0.375rem 0.875rem',
                                                        background: '#f3f4f6',
                                                        color: '#6b7280',
                                                        borderRadius: '6px',
                                                        fontSize: '0.75rem',
                                                        fontWeight: '600',
                                                        letterSpacing: '0.05em'
                                                    }}>
                                                        {file.extension.toUpperCase().replace('.', '') || 'TXT'}
                                                    </span>
                                                </td>
                                                <td style={{ 
                                                    padding: '1rem 1.5rem', 
                                                    fontSize: '0.875rem', 
                                                    color: '#6b7280',
                                                    verticalAlign: 'middle', 
                                                    textAlign: 'right',
                                                    fontFamily: '"SF Mono", Monaco, "Courier New", monospace',
                                                    fontWeight: '500'
                                                }}>
                                                    {file.size < 1024 ? `${file.size}B` :
                                                     file.size < 1048576 ? `${(file.size / 1024).toFixed(1)}KB` :
                                                     `${(file.size / 1048576).toFixed(1)}MB`}
                                                </td>
                                                <td style={{ 
                                                    padding: '1rem 1.5rem', 
                                                    textAlign: 'center', 
                                                    verticalAlign: 'middle'
                                                }}>
                                                    <a
                                                        href={`${API_BASE_URL}/api/eval-runs/${runId}/artifacts/download?file_path=${encodeURIComponent(file.path)}`}
                                                        download
                                                        style={{ 
                                                            textDecoration: 'none',
                                                            display: 'inline-flex',
                                                            alignItems: 'center',
                                                            gap: '0.5rem',
                                                            padding: '0.625rem 1.25rem',
                                                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                                            color: 'white',
                                                            borderRadius: '8px',
                                                            fontSize: '0.875rem',
                                                            fontWeight: '600',
                                                            transition: 'all 0.2s',
                                                            boxShadow: '0 2px 4px rgba(102, 126, 234, 0.2)',
                                                            border: 'none',
                                                            cursor: 'pointer'
                                                        }}
                                                        onMouseEnter={(e) => {
                                                            e.currentTarget.style.transform = 'translateY(-2px)';
                                                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';
                                                        }}
                                                        onMouseLeave={(e) => {
                                                            e.currentTarget.style.transform = 'translateY(0)';
                                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(102, 126, 234, 0.2)';
                                                        }}
                                                    >
                                                        <span style={{ fontSize: '1rem' }}>⬇</span>
                                                        <span>Download</span>
                                                    </a>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            <div style={{ 
                                textAlign: 'center', 
                                padding: '3rem',
                                color: '#9ca3af',
                                background: '#f9fafb',
                                borderRadius: '8px',
                                border: '1px dashed #e5e7eb'
                            }}>
                                {artifacts.error ? (
                                    <>
                                        <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>⚠️</div>
                                        <p style={{ fontSize: '0.875rem' }}>
                                            Could not load files: {artifacts.error}
                                        </p>
                                    </>
                                ) : (
                                    <>
                                        <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📂</div>
                                        <p style={{ fontSize: '0.875rem' }}>
                                            No files generated during this execution
                                        </p>
                                    </>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </div>
            
            <details className="mt-8 p-4 bg-muted border border-border rounded-lg">
                <summary className="cursor-pointer font-semibold text-foreground">🔍 Debug: Raw Run Data</summary>
                <div className="mt-4">
                    <p className="text-sm text-muted-foreground mb-2">
                        This shows the actual data structure returned from the API. Useful for debugging why outputs aren't showing.
                    </p>
                    <pre className="text-xs bg-background p-4 rounded-md overflow-auto max-h-96 font-mono">
                        {JSON.stringify(run, null, 2)}
                    </pre>
                </div>
            </details>
        </div>
    );
};

// Component: Workflow JSON Generator from Spec Doc
const WorkflowJSONGenerator = ({ onWorkflowCreated }) => {
    const [file, setFile] = React.useState(null);
    const [workflowName, setWorkflowName] = React.useState('');
    const [namespace, setNamespace] = React.useState('default');
    const [uploading, setUploading] = React.useState(false);
    const [taskId, setTaskId] = React.useState(null);
    const [status, setStatus] = React.useState(null);
    const [workflowJson, setWorkflowJson] = React.useState(null);
    const [createdWorkflowId, setCreatedWorkflowId] = React.useState(null);
    const [error, setError] = React.useState(null);
    
    const handleFileChange = (e) => {
        const selectedFile = e.target.files[0];
        if (selectedFile) {
            setFile(selectedFile);
            // Auto-populate workflow name from filename
            if (!workflowName) {
                const name = selectedFile.name.replace(/\.(docx|doc)$/i, '').replace(/[_-]/g, ' ');
                setWorkflowName(name);
            }
        }
    };
    
    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!file) {
            alert('Please select a specification document');
            return;
        }
        
        setUploading(true);
        setError(null);
        
        try {
            // Upload file and start generation
            const formData = new FormData();
            formData.append('file', file);
            if (workflowName) {
                formData.append('workflow_name', workflowName);
            }
            
            const response = await fetch(`${API_BASE_URL}/workflow-generation/generate`, {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.detail || 'Upload failed');
            }
            
            const data = await response.json();
            setTaskId(data.task_id);
            
            // Start polling for status
            pollStatus(data.task_id);
        } catch (err) {
            setError(err.message);
            setUploading(false);
        }
    };
    
    const pollStatus = async (id) => {
        try {
            const response = await fetch(`${API_BASE_URL}/workflow-generation/status/${id}`);
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: response.statusText }));
                throw new Error(errorData.detail || 'Failed to get status');
            }
            
            const data = await response.json();
            setStatus(data);
            
            if (data.status === 'completed') {
                setWorkflowJson(data.workflow_json);
                setUploading(false);
            } else if (data.status === 'failed') {
                setError(data.error || 'Generation failed');
                setUploading(false);
            } else {
                // Continue polling
                setTimeout(() => pollStatus(id), 2000);
            }
        } catch (err) {
            console.error('Status poll error:', err);
            setError(`Status check failed: ${err.message}`);
            setUploading(false);
        }
    };
    
    const handleCreateWorkflow = async () => {
        if (!workflowJson) return;
        
        try {
            const response = await api.post('/api/workflows', {
                name: workflowName || status?.workflow_name || 'Generated Workflow',
                workflow: workflowJson,
                namespace: namespace,
                is_draft: false
            });
            
            setCreatedWorkflowId(response.id);
            if (onWorkflowCreated) {
                onWorkflowCreated(response.id);
            }
        } catch (err) {
            alert(`Failed to create workflow: ${err.message}`);
        }
    };
    
    const handleReset = () => {
        setFile(null);
        setWorkflowName('');
        setNamespace('default');
        setTaskId(null);
        setStatus(null);
        setWorkflowJson(null);
        setCreatedWorkflowId(null);
        setError(null);
        setUploading(false);
    };
    
    return (
        <div className="workflow-json-generator">
            <div className="workflow-header">
                <h2>🚀 Generate Workflow from Specification</h2>
                <p className="text-muted-foreground mt-2">
                    Upload a Word document (.docx) with your workflow specification, and our AI will design an agentic workflow to execute it.
                </p>
            </div>
            
            {error && (
                <div className="error mt-4">
                    <div className="error-title">Error</div>
                    <div>{error}</div>
                    <button className="btn btn-secondary mt-2" onClick={handleReset}>
                        Try Again
                    </button>
                </div>
            )}
            
            {!taskId && !createdWorkflowId && (
                <form onSubmit={handleSubmit} className="workflow-gen-form mt-6">
                    <div className="form-group">
                        <label htmlFor="spec-file">Specification Document *</label>
                        <input
                            id="spec-file"
                            type="file"
                            accept=".docx"
                            onChange={handleFileChange}
                            required
                            className="file-input"
                        />
                        <p className="text-sm text-muted-foreground mt-1">
                            Upload a .docx file describing what your workflow should do
                        </p>
                    </div>
                    
                    <div className="form-group">
                        <label htmlFor="workflow-name">Workflow Name *</label>
                        <input
                            id="workflow-name"
                            type="text"
                            value={workflowName}
                            onChange={(e) => setWorkflowName(e.target.value)}
                            placeholder="My Workflow"
                            required
                            className="text-input"
                        />
                    </div>
                    
                    <div className="form-group">
                        <label htmlFor="namespace">Namespace</label>
                        <input
                            id="namespace"
                            type="text"
                            value={namespace}
                            onChange={(e) => setNamespace(e.target.value)}
                            placeholder="default"
                            className="text-input"
                        />
                    </div>
                    
                    <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={uploading || !file}
                    >
                        {uploading ? '⏳ Generating Workflow...' : '✨ Generate Workflow'}
                    </button>
                </form>
            )}
            
            {uploading && status && (
                <div className="status-card mt-6">
                    <h3 className="font-semibold mb-2">Generation Status</h3>
                    <div className="flex items-center gap-2">
                        <div className="spinner"></div>
                        <span>{status.message}</span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-2">
                        This may take 30-60 seconds...
                    </p>
                </div>
            )}
            
            {workflowJson && !createdWorkflowId && (
                <div className="workflow-result mt-6">
                    <h3 className="font-semibold text-lg mb-4">✅ Workflow Generated Successfully!</h3>
                    
                    <div className="flex gap-3 mb-4">
                        <button
                            className="btn btn-primary"
                            onClick={handleCreateWorkflow}
                        >
                            💾 Create Workflow in Database
                        </button>
                        <button
                            className="btn btn-secondary"
                            onClick={() => {
                                navigator.clipboard.writeText(JSON.stringify(workflowJson, null, 2));
                                alert('Workflow JSON copied to clipboard!');
                            }}
                        >
                            📋 Copy JSON
                        </button>
                        <button
                            className="btn btn-secondary"
                            onClick={handleReset}
                        >
                            🔄 Generate Another
                        </button>
                    </div>
                    
                    <div className="workflow-json-display">
                        <h4 className="font-medium mb-2">Generated Workflow JSON:</h4>
                        <pre className="json-preview">
                            {JSON.stringify(workflowJson, null, 2)}
                        </pre>
                    </div>
                </div>
            )}
            
            {createdWorkflowId && (
                <div className="success-card mt-6">
                    <h3 className="font-semibold text-lg mb-2">🎉 Workflow Created!</h3>
                    <p className="mb-4">Your workflow has been saved to the database.</p>
                    <div className="flex gap-3">
                        <a
                            href={`#workflow-${createdWorkflowId}`}
                            className="btn btn-primary"
                            onClick={(e) => {
                                e.preventDefault();
                                if (onWorkflowCreated) {
                                    onWorkflowCreated(createdWorkflowId);
                                }
                            }}
                        >
                            📊 View Workflow
                        </a>
                        <button
                            className="btn btn-secondary"
                            onClick={handleReset}
                        >
                            ✨ Generate Another Workflow
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
};

// Main App Component
const App = () => {
    const [currentView, setCurrentView] = React.useState('list'); // 'list', 'detail', 'new', 'dashboard', 'run-detail', 'spec-generator', 'workflow-json-generator'
    const [selectedWorkflowId, setSelectedWorkflowId] = React.useState(null);
    const [selectedRunId, setSelectedRunId] = React.useState(null);
    const [toasts, setToasts] = React.useState([]);
    
    const showToast = (message, type = 'info') => {
        const id = Date.now();
        setToasts(prev => [...prev, { id, message, type }]);
    };
    
    const removeToast = (id) => {
        setToasts(prev => prev.filter(toast => toast.id !== id));
    };
    
    const handleSelectWorkflow = (workflowId) => {
        setSelectedWorkflowId(workflowId);
        setCurrentView('detail');
    };
    
    const handleNewWorkflow = () => {
        setCurrentView('new');
    };
    
    const handleBackToList = () => {
        setSelectedWorkflowId(null);
        setCurrentView('list');
    };
    
    const handleWorkflowCreated = () => {
        showToast('Workflow created successfully', 'success');
        handleBackToList();
    };
    
    const handleWorkflowUpdated = () => {
        showToast('Workflow updated successfully', 'success');
    };
    
    const handleViewRun = (runId) => {
        setSelectedRunId(runId);
        setCurrentView('run-detail');
    };
    
    const handleBackToDashboard = () => {
        setSelectedRunId(null);
        setCurrentView('dashboard');
    };
    
    const handleWorkflowGenerated = (workflowId) => {
        setSelectedWorkflowId(workflowId);
        setCurrentView('detail');
        showToast('Workflow created successfully!', 'success');
    };
    
    return (
        <div className="flex min-h-screen w-full flex-col bg-canvas">
            <header className="app-header">
                <div className="flex items-center justify-between">
                    <h1 className="text-base font-bold">Axon-PFC Workflow Manager</h1>
                    <nav className="app-nav">
                        <button 
                            className={`nav-btn ${currentView === 'list' || currentView === 'detail' || currentView === 'new' ? 'active' : ''}`}
                            onClick={handleBackToList}
                        >
                            Workflows
                        </button>
                        <button 
                            className={`nav-btn ${currentView === 'dashboard' || currentView === 'run-detail' ? 'active' : ''}`}
                            onClick={handleBackToDashboard}
                        >
                            Dashboard
                        </button>
                        <button 
                            className={`nav-btn ${currentView === 'spec-generator' ? 'active' : ''}`}
                            onClick={() => setCurrentView('spec-generator')}
                        >
                            🤖 AI Spec Generator
                        </button>
                        <button 
                            className={`nav-btn ${currentView === 'workflow-json-generator' ? 'active' : ''}`}
                            onClick={() => setCurrentView('workflow-json-generator')}
                        >
                            🚀 Generate from Doc
                        </button>
                    </nav>
                </div>
            </header>
            
            <div className="container flex-1">
                <div className="main-content">
                    {currentView === 'dashboard' && (
                        <DashboardPage onViewRun={handleViewRun} />
                    )}
                    
                    {currentView === 'run-detail' && selectedRunId && (
                        <RunDetailPage
                            runId={selectedRunId}
                            onBack={handleBackToDashboard}
                        />
                    )}
                    
                    {currentView === 'list' && (
                        <WorkflowList
                            onSelectWorkflow={handleSelectWorkflow}
                            onNewWorkflow={handleNewWorkflow}
                        />
                    )}
                    
                    {currentView === 'detail' && selectedWorkflowId && (
                        <WorkflowDetail
                            workflowId={selectedWorkflowId}
                            onBack={handleBackToList}
                            onWorkflowUpdated={handleWorkflowUpdated}
                        />
                    )}
                    
                    {currentView === 'new' && (
                        <NewWorkflowForm
                            onBack={handleBackToList}
                            onWorkflowCreated={handleWorkflowCreated}
                        />
                    )}
                    
                    {currentView === 'spec-generator' && (
                        <WorkflowSpecGenerator />
                    )}
                    
                    {currentView === 'workflow-json-generator' && (
                        <WorkflowJSONGenerator onWorkflowCreated={handleWorkflowGenerated} />
                    )}
                </div>
            </div>
            
            <div className="toast-container">
                {toasts.map(toast => (
                    <Toast
                        key={toast.id}
                        message={toast.message}
                        type={toast.type}
                        onClose={() => removeToast(toast.id)}
                    />
                ))}
            </div>
        </div>
    );
};

// Render the app
const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);
