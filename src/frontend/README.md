# Axon-PFC Frontend

Simple React-based web UI for managing workflows.

## Features

- List all workflows
- View workflow details in JSON format
- Create new workflows with JSON editor
- Edit existing workflows
- Delete workflows
- Real-time validation

## Components

### index.html

Single-page application with:
- React and ReactDOM (via CDN)
- Material-like styling
- No build step required

### app.js

React components:
- `WorkflowList` - Browse all workflows
- `WorkflowDetail` - View/edit individual workflow
- `NewWorkflowForm` - Create new workflow
- `App` - Main application with routing

### style.css

Clean, modern styling with:
- Card-based layout
- Responsive design
- Syntax highlighting for JSON
- Toast notifications

## Running

From project root:
```bash
./start.sh
```

Or manually:
```bash
cd src/frontend
python -m http.server 8080
```

Access at: http://localhost:8080

## Usage

### Create Workflow

1. Click "New Workflow"
2. Enter workflow name
3. Paste JSON definition (see `example_team.json` in backend)
4. Click "Create"

### Edit Workflow

1. Click on a workflow from the list
2. Click "Edit Workflow"
3. Modify name or JSON definition
4. Click "Save Changes"

## JSON Format

Workflows use this structure:

```json
{
  "description": "...",
  "variables": {
    "input": {},
    "output": {}
  },
  "steps": [
    {
      "order": 1,
      "type": "team",
      "team": { ... }
    }
  ]
}
```

Note: Metadata (name, created_at, etc.) is handled separately by the API.
