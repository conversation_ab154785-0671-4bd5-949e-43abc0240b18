/* ============================================
   Axon-PFC UI - Based on agentic-core Design System
   Style Guide: docs/agentic-core-ui-style-guide.md
   ============================================ */

/* === Design System Foundation === */
:root {
  /* Core radius - all border-radius values derive from this */
  --radius: 0.625rem; /* 10px */
  
  /* Header height constant */
  --header-height: 64px;
  
  /* Light mode colors (using OKLCH color space) */
  --background: oklch(1 0 0);          /* Pure white */
  --foreground: oklch(0.145 0 0);      /* Near black */
  --card: oklch(1 0 0);                /* White */
  --card-foreground: oklch(0.145 0 0); /* Near black */
  --canvas: oklch(0.99 0 0);           /* Off-white background */
  --primary: oklch(0.205 0 0);         /* Dark gray/black */
  --primary-foreground: oklch(0.985 0 0); /* White */
  --muted: oklch(0.97 0 0);            /* Light gray */
  --muted-foreground: oklch(0.556 0 0); /* Medium gray */
  --border: oklch(0.922 0 0);          /* Subtle border gray */
  --ring: oklch(0.708 0 0);            /* Focus ring gray */
  --input: oklch(0.922 0 0);           /* Input border */
  --accent: oklch(0.97 0 0);           /* Accent background */
  --accent-foreground: oklch(0.145 0 0); /* Accent text */
  
  /* Destructive colors */
  --destructive: oklch(0.577 0.245 27.325); /* Red */
  --destructive-foreground: oklch(0.985 0 0); /* White */
  
  /* Brand colors */
  --electric-blue: #0c76fe;
  --electric-blue-background: oklch(97% 3% 261deg);
  --teal: #2a9d90;
  
  /* Success color */
  --success: #22c55e;
  
  /* Chart colors */
  --chart-1: oklch(0.646 0.222 41.116);   /* Orange */
  --chart-2: oklch(0.6 0.118 184.704);    /* Cyan */
  --chart-3: oklch(0.398 0.07 227.392);   /* Blue */
  --chart-4: oklch(0.828 0.189 84.429);   /* Yellow-green */
  --chart-5: oklch(0.769 0.188 70.08);    /* Yellow */
  
  /* Radius variants */
  --radius-sm: calc(var(--radius) - 4px);  /* 6px */
  --radius-md: calc(var(--radius) - 2px);  /* 8px */
  --radius-lg: var(--radius);              /* 10px */
  --radius-xl: calc(var(--radius) + 4px);  /* 14px */
  
  /* Font families */
  --font-sans: Inter, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
  --font-mono: Menlo, ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.dark {
  /* Dark mode colors */
  --background: oklch(0.145 0 0);      /* Near black */
  --foreground: oklch(0.985 0 0);      /* Near white */
  --card: oklch(0.205 0 0);            /* Dark gray */
  --canvas: oklch(0.19 0 0);           /* Slightly lighter than bg */
  --border: oklch(1 0 0 / 10%);       /* 10% white opacity */
  --input: oklch(1 0 0 / 15%);        /* 15% white opacity */
  --electric-blue-background: oklch(25% 12% 258deg);
  --muted: oklch(0.269 0 0);
  --accent: oklch(0.269 0 0);
}

/* === Base Styles === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-sans);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--canvas);
  color: var(--foreground);
  font-size: 0.875rem; /* 14px base */
  line-height: 1.5;
}

/* === Tailwind CSS Overrides === */
.bg-background { background: var(--background); }
.bg-card { background: var(--card); }
.bg-canvas { background: var(--canvas); }
.bg-primary { background: var(--primary); }
.bg-muted { background: var(--muted); }
.bg-accent { background: var(--accent); }
.bg-electric-blue { background: var(--electric-blue); }
.bg-destructive { background: var(--destructive); }

.text-foreground { color: var(--foreground); }
.text-muted-foreground { color: var(--muted-foreground); }
.text-primary-foreground { color: var(--primary-foreground); }
.text-destructive { color: var(--destructive); }

.border { border: 1px solid var(--border); }
.border-border { border-color: var(--border); }
.border-input { border-color: var(--input); }

/* === Header Bar === */
.app-header {
  background: var(--background);
  color: var(--foreground);
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 50;
}

.app-header h1 {
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: var(--foreground);
}

.app-nav {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.nav-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  color: var(--muted-foreground);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  font-family: var(--font-sans);
}

.nav-btn:hover {
  background: var(--accent);
  color: var(--foreground);
}

.nav-btn.active {
  background: var(--primary);
  color: var(--primary-foreground);
}

/* === Container & Layout === */
.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 1rem;
}

@media (min-width: 768px) {
  .container {
    padding: 2rem;
  }
}

.main-content {
  background: var(--background);
  border-radius: var(--radius-xl);
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  padding: 1.5rem;
  border: 1px solid var(--border);
}

@media (min-width: 768px) {
  .main-content {
    padding: 2rem;
  }
}

/* === Buttons === */
.btn {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  white-space: nowrap;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  font-family: var(--font-sans);
  padding: 0.5rem 1rem;
  height: 36px;
  border: none;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.btn:disabled {
  pointer-events: none;
  opacity: 0.5;
}

.btn:focus-visible {
  outline: none;
  ring: 3px solid rgba(0, 0, 0, 0.5);
}

.btn-primary {
  background: var(--primary);
  color: var(--primary-foreground);
}

.btn-primary:hover:not(:disabled) {
  background: oklch(0.25 0 0);
}

.btn-secondary {
  border: 1px solid var(--border);
  background: var(--background);
  color: var(--foreground);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--accent);
}

.btn-success {
  background: var(--success);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: oklch(0.58 0.176 145);
}

.btn-danger,
.btn-delete {
  background: transparent;
  color: var(--destructive);
  border: 1px solid var(--border);
}

.btn-danger:hover:not(:disabled),
.btn-delete:hover:not(:disabled) {
  background: oklch(0.95 0.08 27);
  border-color: var(--destructive);
}

.btn-view {
  background: var(--primary);
  color: var(--primary-foreground);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.btn-view:hover {
  background: oklch(0.25 0 0);
}

.btn-small {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  height: 32px;
}

.btn-cancel-small {
  background: var(--destructive);
  color: white;
  padding: 0.25rem 0.75rem;
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: 0.75rem;
  transition: background 0.2s;
  height: 28px;
}

.btn-cancel-small:hover:not(:disabled) {
  background: oklch(0.52 0.22 27);
}

.btn-cancel-small:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* === Cards === */
.workflow-item,
.dashboard-card,
.run-detail-card {
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  background: var(--card);
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  transition: all 0.2s;
}

.workflow-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  margin-bottom: 1rem;
}

.workflow-item:hover {
  border-color: var(--ring);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  transform: translateY(-1px);
}

.workflow-info h3 {
  color: var(--foreground);
  margin-bottom: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
}

.workflow-info p {
  color: var(--muted-foreground);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.workflow-meta {
  font-size: 0.75rem;
  color: var(--muted-foreground);
}

.workflow-actions {
  display: flex;
  gap: 0.5rem;
}

/* === Badges === */
.badge-active,
.badge-inactive,
.badge-running,
.badge-passed,
.badge-failed,
.badge-complete,
.badge-pending,
.badge-cancelled {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-block;
}

.badge-active,
.badge-passed {
  background: oklch(0.95 0.05 145);
  color: oklch(0.35 0.15 145);
}

.badge-inactive,
.badge-cancelled {
  background: var(--muted);
  color: var(--muted-foreground);
}

.badge-running,
.badge-pending {
  background: oklch(0.95 0.08 85);
  color: oklch(0.45 0.15 75);
}

.badge-failed {
  background: oklch(0.95 0.08 27);
  color: oklch(0.45 0.2 27);
}

.badge-complete {
  background: oklch(0.95 0.05 250);
  color: oklch(0.45 0.1 250);
}

/* === Tables === */
table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.eval-cases-table th,
.eval-cases-table td,
.dashboard-table th,
.dashboard-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--border);
}

.eval-cases-table th,
.dashboard-table th {
  background: var(--muted);
  font-weight: 600;
  color: var(--foreground);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.eval-cases-table tbody tr:hover,
.dashboard-table tbody tr:hover {
  background: var(--accent);
}

.clickable-row {
  cursor: pointer;
  transition: background-color 0.2s;
}

.clickable-row:hover {
  background: var(--accent) !important;
}

/* === Forms === */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--foreground);
  font-size: 0.875rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--input);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-family: var(--font-sans);
  transition: all 0.2s;
  background: var(--background);
  color: var(--foreground);
  height: 36px;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.form-group textarea {
  font-family: var(--font-mono);
  min-height: 400px;
  resize: vertical;
  height: auto;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--ring);
  ring: 3px solid rgba(0, 0, 0, 0.1);
}

.form-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 2rem;
}

/* === Headers & Sections === */
.workflow-header,
.eval-cases-header,
.workflow-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.workflow-header h2,
.dashboard h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--foreground);
}

.workflow-detail-header {
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border);
  align-items: flex-start;
}

.workflow-detail-info h2 {
  color: var(--foreground);
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.workflow-detail-info p {
  color: var(--muted-foreground);
  font-size: 0.875rem;
}

.workflow-detail-meta {
  text-align: right;
  font-size: 0.75rem;
  color: var(--muted-foreground);
}

.workflow-detail-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  align-items: center;
}

/* === Tabs === */
.workflow-tabs {
  display: flex;
  gap: 0.5rem;
  border-bottom: 1px solid var(--border);
  margin-bottom: 1.5rem;
}

.tab {
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  color: var(--muted-foreground);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  font-size: 0.875rem;
  font-weight: 500;
  font-family: var(--font-sans);
}

.tab:hover {
  color: var(--foreground);
}

.tab.active {
  color: var(--foreground);
  border-bottom-color: var(--primary);
}

/* === Workflow Editor === */
.workflow-editor {
  margin-top: 1.5rem;
}

.workflow-editor h3 {
  margin-bottom: 1rem;
  color: var(--foreground);
  font-size: 1rem;
  font-weight: 600;
}

.workflow-editor-container {
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

.workflow-editor-toolbar {
  background: var(--muted);
  padding: 0.5rem 1rem;
  border-bottom: 1px solid var(--border);
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
  font-size: 0.75rem;
  color: var(--muted-foreground);
}

.workflow-content {
  padding: 1rem;
  font-family: var(--font-mono);
  font-size: 0.875rem;
  line-height: 1.6;
  background: var(--muted);
  max-height: 600px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: var(--foreground);
}

.workflow-content.editable {
  background: var(--background);
}

/* === Dashboard === */
.dashboard {
  padding: 1rem 0;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.dashboard-card h3 {
  margin-bottom: 1rem;
  color: var(--foreground);
  font-size: 1rem;
  font-weight: 600;
}

.health-indicators {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.health-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.health-healthy {
  color: var(--success);
  font-weight: 600;
}

.health-unhealthy {
  color: var(--destructive);
  font-weight: 600;
}

.queue-stat {
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border);
  font-size: 0.875rem;
}

.queue-stat:last-child {
  border-bottom: none;
}

.dashboard-section {
  margin-bottom: 2rem;
}

.dashboard-section h3 {
  margin-bottom: 1rem;
  color: var(--foreground);
  font-size: 1.125rem;
  font-weight: 600;
}

/* === Run Detail === */
.run-detail {
  padding: 1rem 0;
}

.run-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border);
}

.run-detail-header h2 {
  margin-bottom: 0.5rem;
  color: var(--foreground);
  font-size: 1.5rem;
  font-weight: 600;
}

.run-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.run-detail-card h3 {
  margin-bottom: 0.5rem;
  color: var(--foreground);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.run-detail-section {
  background: var(--muted);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.run-detail-section h3 {
  margin-bottom: 1rem;
  color: var(--foreground);
  font-size: 1rem;
  font-weight: 600;
}

.run-detail-section.error {
  background: oklch(0.95 0.08 27);
  border-color: var(--destructive);
}

.run-detail-tabs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.run-detail-tab {
  background: var(--muted);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
}

.run-detail-tab.full-width {
  grid-column: 1 / -1;
  background: var(--card);
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

.run-detail-tab h3 {
  margin-bottom: 1rem;
  color: var(--foreground);
  font-size: 1rem;
  font-weight: 600;
}

/* === Conversation View === */
.conversation-view {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.conversation-message {
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 1rem;
  background: var(--card);
}

.conversation-message.input {
  border-left: 3px solid oklch(0.6 0.15 250);
  background: oklch(0.98 0.02 250);
}

.conversation-message.message {
  border-left: 3px solid var(--success);
  background: oklch(0.98 0.02 145);
}

.conversation-message.tool {
  border-left: 3px solid oklch(0.7 0.15 75);
  background: oklch(0.98 0.05 75);
}

.conversation-message.variable {
  border-left: 3px solid oklch(0.6 0.15 290);
  background: oklch(0.98 0.02 290);
}

.conversation-message-header {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border);
}

.conversation-message-type {
  font-size: 0.625rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  background: var(--muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.conversation-message-name {
  font-weight: 500;
  color: var(--foreground);
  font-size: 0.875rem;
}

.conversation-message-step {
  font-size: 0.75rem;
  color: var(--muted-foreground);
  margin-left: auto;
}

.conversation-message-content {
  color: var(--foreground);
  line-height: 1.6;
  font-size: 0.875rem;
}

/* === Variables === */
.variables-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.variable-item {
  padding: 0.75rem;
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
}

.variable-item strong {
  color: var(--foreground);
}

/* === Modal === */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--card);
  border-radius: var(--radius-xl);
  padding: 2rem;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
  border: 1px solid var(--border);
}

.modal-content h3 {
  margin-bottom: 1.5rem;
  color: var(--foreground);
  font-size: 1.125rem;
  font-weight: 600;
}

/* === Loading & Empty States === */
.loading {
  text-align: center;
  padding: 3rem;
  color: var(--muted-foreground);
  font-size: 0.875rem;
}

.loading::after {
  content: '...';
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60%, 100% { content: '...'; }
}

.error {
  background: oklch(0.95 0.08 27);
  border: 1px solid var(--destructive);
  border-radius: var(--radius-lg);
  padding: 1rem;
  color: var(--destructive);
  margin-bottom: 1rem;
}

.error-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: var(--muted-foreground);
}

.empty-state-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: var(--foreground);
  margin-bottom: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
}

/* === Toast Notifications === */
.toast-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

.toast {
  background: var(--card);
  border-radius: var(--radius-lg);
  padding: 1rem 1.5rem;
  margin-top: 0.5rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  animation: slideIn 0.3s ease-out;
  border: 1px solid var(--border);
  font-size: 0.875rem;
}

@keyframes slideIn {
  from {
    transform: translateX(400px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.toast.success {
  border-left: 4px solid var(--success);
}

.toast.error {
  border-left: 4px solid var(--destructive);
}

.toast.info {
  border-left: 4px solid var(--primary);
}

/* === Responsive === */
@media (max-width: 768px) {
  .workflow-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .workflow-actions {
    margin-top: 1rem;
    width: 100%;
  }

  .workflow-actions button {
    flex: 1;
  }

  .workflow-detail-header {
    flex-direction: column;
  }

  .workflow-detail-actions {
    margin-top: 1rem;
    width: 100%;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .run-detail-header {
    flex-direction: column;
  }

  .run-detail-grid,
  .run-detail-tabs {
    grid-template-columns: 1fr;
  }
}

/* === Utilities === */
.shadow-xs {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.shadow-sm {
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

/* === Workflow JSON Generator === */
.workflow-json-generator {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.workflow-gen-form {
  background: var(--card);
  padding: 2rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
}

.workflow-gen-form .form-group {
  margin-bottom: 1.5rem;
}

.workflow-gen-form .form-group:last-of-type {
  margin-bottom: 2rem;
}

.file-input,
.text-input {
  display: block;
  width: 100%;
  padding: 0.625rem 0.875rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--foreground);
  background: var(--background);
  border: 1px solid var(--input);
  border-radius: var(--radius-md);
  transition: border-color 0.15s ease-in-out;
}

.file-input:focus,
.text-input:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 3px oklch(from var(--ring) l c h / 10%);
}

.status-card {
  background: var(--accent);
  padding: 1.5rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
}

.spinner {
  width: 20px;
  height: 20px;
  border: 3px solid var(--muted);
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.workflow-result {
  background: var(--card);
  padding: 2rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
}

.workflow-json-display {
  margin-top: 1rem;
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.json-preview {
  background: var(--muted);
  padding: 1rem;
  font-family: var(--font-mono);
  font-size: 0.75rem;
  line-height: 1.5;
  overflow-x: auto;
  max-height: 500px;
  overflow-y: auto;
  margin: 0;
}

.success-card {
  background: oklch(0.95 0.05 150);
  border: 1px solid oklch(0.85 0.08 150);
  padding: 1.5rem;
  border-radius: var(--radius-lg);
}

.success-card h3 {
  color: oklch(0.35 0.12 150);
}

.success-card p {
  color: oklch(0.45 0.1 150);
}

/* Typing indicator animation */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background-color: #6b7280;
  border-radius: 50%;
  display: inline-block;
  animation: typing-pulse 1.4s infinite ease-in-out both;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing-pulse {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Fade in animation for new messages */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Blinking cursor animation for streaming text */
@keyframes blink {
  0%, 49% {
    opacity: 1;
  }
  50%, 100% {
    opacity: 0;
  }
}
