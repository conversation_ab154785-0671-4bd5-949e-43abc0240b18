#!/bin/bash
# Import Tools and Workflows from Another Environment
# This script imports tools and workflows without affecting local execution data

set -e

if [ -z "$1" ]; then
    echo "Usage: ./import_tools.sh <path_to_sql_dump>"
    echo ""
    echo "Example:"
    echo "  ./import_tools.sh ../exports/tools_and_workflows_20251028.sql"
    exit 1
fi

SQL_FILE="$1"
AGENTIC_CORE_PATH="${AGENTIC_CORE_PATH:-../agentic-core}"

if [ ! -f "$SQL_FILE" ]; then
    echo "❌ Error: File not found: $SQL_FILE"
    exit 1
fi

echo "=================================================="
echo "Importing Tools and Workflows"
echo "=================================================="
echo ""
echo "📥 Source file: $SQL_FILE"
echo "🎯 Target database: invisible"
echo ""
read -p "⚠️  This will add/update tools and workflows. Continue? (y/N): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Cancelled."
    exit 0
fi

cd "$AGENTIC_CORE_PATH"

echo "📦 Importing..."
docker compose exec -T postgres psql -U postgres invisible < "$SQL_FILE"

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Import successful!"
    echo ""
    echo "📋 Imported:"
    echo "   • Tools and tool versions"
    echo "   • Workflows (flows) and flow versions"
    echo "   • Teams and team versions"
    echo "   • Delegates"
    echo "   • Namespaces"
    echo ""
    echo "🔍 Verify in UI:"
    echo "   • Check Tools tab for new/updated tools"
    echo "   • Check Workflows tab for imported workflows"
else
    echo "❌ Import failed. Check the error messages above."
    exit 1
fi

