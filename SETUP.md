# Axon-PFC Setup Guide

Complete setup instructions for the Axon-PFC workflow execution and evaluation system.

---

## 📋 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Agentic-Core Setup](#agentic-core-setup)
3. [Axon-PFC Setup](#axon-pfc-setup)
4. [Database Migration](#database-migration)
5. [Configuration](#configuration)
6. [Starting the System](#starting-the-system)
7. [Verification](#verification)
8. [Troubleshooting](#troubleshooting)

---

## Prerequisites

### Required Software

- **Python 3.13+**
- **Docker & Docker Compose** (for agentic-core services)
- **PostgreSQL** (via Docker in agentic-core)
- **RabbitMQ** (via Docker in agentic-core)
- **Git**

### Required Accounts

- **OpenAI API Key** (for LLM-based agents)
  - Sign up at https://platform.openai.com/
  - Create an API key

---

## Agentic-Core Setup

Axon-PFC requires a running instance of **agentic-core** (the workflow execution engine).

### 1. Clone Agentic-Core

```bash
cd /Users/<USER>/projects
git clone <agentic-core-repo-url> agentic-core
cd agentic-core
```

### 2. Build the Frontend

The agentic-core UI needs to be built locally:

```bash
cd /Users/<USER>/projects/agentic-core/frontend
npm install
npm run build
```

This creates the `dist/` directory that the UI container mounts via volume.

### 3. Apply Required Changes

The agentic-core async workers need explicit Azure dependencies. Make the following change:

**File:** `backend/services/async-engine-worker/pyproject.toml`

```toml
dependencies = [
    "autogen-agentchat==0.5.6",
    "autogen-core==0.5.6",
    "autogen-ext[openai,azure,mcp]==0.5.6",
    
    # Azure dependencies (explicit for reliability)
    "azure-ai-inference>=1.0.0b7",
    "azure-core>=1.32.0",
    "azure-identity>=1.19.0",
    
    # ... rest of existing dependencies
]
```

### 4. Rebuild Worker Images

After modifying `pyproject.toml`, rebuild the Docker images:

```bash
cd /Users/<USER>/projects/agentic-core

# Regenerate lock file
cd backend/services/async-engine-worker
uv lock
cd ../../..

# Rebuild Docker images
docker compose build async-engine-flyweight async-engine-async

# Start all agentic-core services
docker compose up -d
```

### 5. Generate GraphQL Client Code

The GraphQL service requires generated client code from the GraphQL schema:

```bash
cd /Users/<USER>/projects/agentic-core

# Generate GraphQL client using ariadne-codegen
make graphql-client

# This generates:
# - backend/lib/graphql_client/ (Python client package)
# - build/index.html (GraphQL API documentation)
```

**Troubleshooting:** If `make graphql-client` fails:
```bash
# Ensure ariadne-codegen is installed
pip install ariadne-codegen

# Check for schema file
ls backend/services/graphql/schema.graphql

# Rebuild manually if needed
cd backend/lib
ariadne-codegen
```

### 6. Build GraphQL Container

The GraphQL service must be built after generating the client code:

```bash
cd /Users/<USER>/projects/agentic-core

# Build the GraphQL service container
docker compose build graphql

# Start all services (including GraphQL)
docker compose up -d
```

**Note:** The GraphQL service provides:
- Real-time execution event streaming via WebSocket subscriptions
- GraphQL API for querying execution state
- Powers the GraphQL log writer in Axon-PFC

### 7. Configure OpenAI API Key

**File:** `/Users/<USER>/projects/agentic-core/.env`

```bash
# Create .env file if it doesn't exist
cd /Users/<USER>/projects/agentic-core
touch .env

# Add your OpenAI API key
echo "OPENAI_API_KEY=sk-your-key-here" >> .env
```

**Important:** Restart workers after adding the API key:

```bash
docker compose down
docker compose up -d
```

### 8. Verify Agentic-Core

```bash
# Check all containers are running
docker compose ps

# Should see:
# - postgres (healthy)
# - rabbitmq (healthy)
# - graphql (running on port 5002)
# - async-engine-flyweight (running)
# - async-engine-async (running)

# Check worker logs (should NOT have Azure import errors)
docker compose logs async-engine-flyweight | tail -50
docker compose logs async-engine-async | tail -50

# Expected: "Worker ready", "Connected to RabbitMQ"

# Check GraphQL service
docker compose logs graphql | tail -20
# Expected: "Uvicorn running on http://0.0.0.0:8000"
```

**Note:** The axon-pfc `start.sh` script will automatically start these services, but you can verify them manually here.

---

## Axon-PFC Setup

### 1. Clone Repository

```bash
cd /Users/<USER>/projects
git clone <axon-pfc-repo-url> axon-pfc
cd axon-pfc
```

### 2. Create Virtual Environment

```bash
python3.13 -m venv venv
source venv/bin/activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

**File:** `requirements.txt` (should include):
```
fastapi==0.115.5
uvicorn[standard]==0.32.0
sqlalchemy==2.0.36
psycopg2-binary==2.9.10
pydantic==2.10.2
pydantic-settings==2.6.1
python-dotenv==1.0.1
celery==5.4.0
aio-pika==9.5.3
markdown-it-py==3.0.0
pytest==8.3.4
```

### 4. Configure Static Ports (Optional but Recommended)

Axon-PFC includes a `docker-compose.ports.yml` override file that sets static ports for agentic-core services:

**File:** `docker-compose.ports.yml`
```yaml
services:
  postgres:
    ports:
      - "5432:5432"
  rabbitmq:
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
  graphql:
    ports:
      - "5002:8000"  # GraphQL service on localhost:5002
  ui:
    ports:
      - "3001:3000"  # Agentic-core web UI on localhost:3001
```

This file ensures consistent port mappings across restarts. The `start.sh` script automatically uses this file.

### 5. Create .env File

**File:** `.env`

```bash
# Database (connects to agentic-core's PostgreSQL)
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/invisible

# RabbitMQ (connects to agentic-core's RabbitMQ)
RABBITMQ_URL=amqp://guest:guest@localhost:5672/

# GraphQL (connects to agentic-core's GraphQL service)
GRAPHQL_URL=http://localhost:5002/graphql
GRAPHQL_WS_URL=ws://localhost:5002/graphql

# System User (for GraphQL log writer and other services)
SYSTEM_USER=system
SYSTEM_PASSWORD=foo

# Backend
BACKEND_HOST=localhost
BACKEND_PORT=8000

# Axon-PFC Frontend
FRONTEND_HOST=localhost
FRONTEND_PORT=8080
```

**Important:** The ports are set by `docker-compose.ports.yml`. Verify with:

```bash
docker ps | grep postgres  # Should show 0.0.0.0:5432->5432/tcp
docker ps | grep rabbitmq  # Should show 0.0.0.0:5672->5672/tcp
docker ps | grep graphql   # Should show 0.0.0.0:5002->8000/tcp
docker ps | grep ui        # Should show 0.0.0.0:3001->3000/tcp (agentic-core UI)
```

If ports are different, update `.env` accordingly.

---

## Database Migration

### 1. Run Migration Script

The evaluation schema must be created in agentic-core's PostgreSQL database:

```bash
cd /Users/<USER>/projects/axon-pfc
source venv/bin/activate

# Execute migration
python -c "
from sqlalchemy import create_engine, text
from dotenv import load_dotenv
import os

load_dotenv()

engine = create_engine(os.getenv('DATABASE_URL'))

with open('migrations/add_evaluation_schema.sql', 'r') as f:
    migration_sql = f.read()

with engine.begin() as conn:
    conn.execute(text(migration_sql))
    print('✅ Migration completed successfully')
"
```

### 2. Verify Schema

```bash
# Connect to database
docker exec agentic-core-postgres-1 psql -U postgres -d invisible

# List schemas (should include 'evaluation')
\dn

# List evaluation tables
\dt evaluation.*

# Expected tables:
# - evaluation.eval_case
# - evaluation.eval_run
# - evaluation.eval_suite
# - evaluation.eval_suite_cases
# - evaluation.eval_batch_run

# List views
\dv evaluation.*

# Exit
\q
```

### 3. Setup System User Entitlements

**⚠️ CRITICAL STEP:** The system user (used by GraphQL log writer) needs tenant entitlements to subscribe to execution logs. Without this, the log writer will fail with "Forbidden" errors.

```bash
cd /Users/<USER>/projects/axon-pfc

# Run the entitlement setup script
./scripts/setup_system_user_entitlements.sh
```

**What this does:**
- Looks up the system user (`system@internal`) in the database
- Grants `admin` and `audit` entitlements for all existing tenants
- Enables the log writer to subscribe to flow execution streams

**Expected output:**
```
🔧 Setting up system user entitlements...
📋 Looking up system user...
✓ Found system user: <uuid>
📋 Found 1 tenant(s)
🔐 Granting entitlements to system user for all tenants...
✓ System user entitlements configured for 1 tenant(s)
✅ System user entitlement setup complete!
```

**Note:** If you create new tenants in the future, run this script again to grant the system user access to them.

**Manual verification:**
```bash
# Check system user entitlements
docker exec agentic-core-postgres-1 psql -U postgres -d invisible -c "
  SELECT te.tenant_id, t.name, te.claims
  FROM users.tenant_entitlements te
  JOIN users.tenant t ON t.id = te.tenant_id
  JOIN users.user u ON u.id = te.user_id
  WHERE u.email = 'system@internal';
"
```

---

## Configuration

### Backend Configuration

The backend connects to agentic-core's services. No additional configuration needed if you followed the `.env` setup above.

### Frontend Configuration

The frontend is served as static files and connects to the backend API via JavaScript.

**File:** `src/frontend/index.html` (already configured)
- API calls use relative paths (`/api/...`)
- No separate frontend server needed

---

## Starting the System

### Automated Start (Recommended)

```bash
cd /Users/<USER>/projects/axon-pfc
./start.sh
```

**What start.sh does:**
1. Starts agentic-core Docker services:
   - PostgreSQL (database on port 5432)
   - RabbitMQ (message broker on port 5672)
   - GraphQL (event streaming service on port 5002)
   - UI (agentic-core web UI on port 3001)
   - async-engine-async (workflow worker)
   - async-engine-flyweight (fast worker)
2. Activates Python virtual environment
3. Starts GraphQL log writer daemon (subscribes to execution events)
4. Starts FastAPI backend (port 8000)
5. Starts static file server for axon-pfc frontend (port 8080)
6. Starts eval_monitor daemon (background service)
7. Displays status and logs

**Services Overview:**
- **GraphQL Service:** Streams real-time execution events via WebSocket
- **GraphQL Log Writer:** Subscribes to events and writes them to `artifacts/{execution_id}/flow-execution.log`
- **Eval Monitor:** Monitors completed executions and validates results
- **Backend API:** Serves workflow management and evaluation endpoints
- **Frontend:** Single-page React app for workflow management and run monitoring

### Manual Start (Alternative)

```bash
cd /Users/<USER>/projects/axon-pfc
source venv/bin/activate

# Start GraphQL log writer (IMPORTANT: must be unbuffered for real-time logs)
PYTHONUNBUFFERED=1 PYTHONPATH=. python -u -m src.backend.services.graphql_log_writer > graphql_log_writer.log 2>&1 &
echo $! > .graphql_log_writer.pid

# Start backend
cd src/backend
PYTHONPATH=. python main.py > ../../backend.log 2>&1 &
echo $! > ../../backend.pid

# Start frontend
cd ../frontend
python -m http.server 3000 > ../../frontend.log 2>&1 &
echo $! > ../../frontend.pid

# Start eval monitor
cd ../backend
DATABASE_URL="postgresql://postgres:postgres@localhost:54321/invisible" \
RABBITMQ_URL="amqp://guest:guest@localhost:56721/" \
PYTHONPATH=. python services/eval_monitor.py > ../../eval_monitor.log 2>&1 &
echo $! > ../../.eval_monitor.pid
```

**Important Notes:**
- The GraphQL log writer MUST be started with `PYTHONUNBUFFERED=1` and `python -u` for real-time log output
- The eval_monitor requires explicit `DATABASE_URL` and `RABBITMQ_URL` environment variables

---

## Verification

### 1. Check Services

```bash
# Check all services are running
./start.sh  # Will show status

# Or manually:
ps aux | grep "graphql_log_writer"   # GraphQL Log Writer
ps aux | grep "python.*main.py"      # Backend
ps aux | grep "http.server"          # Frontend  
ps aux | grep "eval_monitor"         # Eval Monitor

# Check Docker services
cd /Users/<USER>/projects/agentic-core
docker compose ps
```

### 2. Access Frontends

Open in browser:
- **Axon-PFC UI:** http://localhost:8080 (workflow management and dashboard)
- **Agentic-Core UI:** http://localhost:3001 (agentic-core web interface)

Axon-PFC UI should show:
- Workflow list
- Navigation to Dashboard
- Ability to create workflows

Agentic-Core UI provides:
- Direct access to agentic-core features
- Flow execution interface
- Tool management

### 3. Test API

```bash
# Health check
curl http://localhost:8000/api/workflows

# Dashboard status
curl http://localhost:8000/api/dashboard/status
```

### 4. Test Workflow Execution

1. **Go to:** http://localhost:8080
2. **Import example workflow:**
   - Click "New Workflow"
   - Upload `src/backend/example_team.json`
   - Save workflow
3. **Create test case:**
   - Click on workflow
   - Go to "Test Cases" tab
   - Click "Create Test Case"
   - Add input: `{"topic": "space exploration"}`
   - Add expected output: `{"greeting_message": "*"}`
   - Save
4. **Run test:**
   - Click "Run Test" button
   - Navigate to Dashboard
   - Watch execution in "Active Runs"
5. **Verify completion:**
   - Execution should appear in "Recent Completions"
   - Check pass/fail status
6. **View execution logs:**
   - Click on any run in the Dashboard
   - See real-time conversation logs
   - View execution details and variables
   
**Expected Behavior:**
- Active runs refresh every second
- Conversation logs appear in real-time
- Execution logs are written to `agentic-core/artifacts/{execution_id}/flow-execution.log`

---

## Troubleshooting

### Issue: "Failed to fetch" when creating test cases

**Cause:** Backend can't connect to database or is crashing

**Solution:**
```bash
# Check backend logs
tail -f backend.log

# Verify database connection
docker exec agentic-core-postgres-1 psql -U postgres -d invisible -c "SELECT 1"

# Restart backend
pkill -f "python.*main.py"
./start.sh
```

### Issue: Workflows fail immediately with "AuthenticationError"

**Cause:** OpenAI API key not loaded by workers

**Solution:**
```bash
# Verify API key in agentic-core
cd /Users/<USER>/projects/agentic-core
cat .env | grep OPENAI_API_KEY

# Restart workers to reload environment
docker compose down
docker compose up -d

# Verify workers loaded the key (check logs)
docker compose logs async-engine-async | grep -i "key\|auth"
```

### Issue: Workflows get stuck, never complete

**Cause:** Agent definitions missing termination instructions

**Solution:**
- Ensure agents use `save_variable` tool
- Ensure coordinator says "TERMINATE" when done
- See `src/backend/example_team.json` for reference

### Issue: No execution logs showing in dashboard

**Cause:** GraphQL log writer not running or GraphQL service not available

**Solution:**
```bash
# Check if GraphQL log writer is running
ps aux | grep graphql_log_writer

# Check logs
tail -f graphql_log_writer.log

# Verify GraphQL service is accessible
curl http://localhost:5002/graphql

# Check for WebSocket connection errors in log writer
grep "ERROR\|Failed\|connection" graphql_log_writer.log

# Restart log writer
pkill -f graphql_log_writer
PYTHONUNBUFFERED=1 python -u -m src.backend.services.graphql_log_writer > graphql_log_writer.log 2>&1 &
```

### Issue: GraphQL service fails to start with "ModuleNotFoundError: No module named 'graphql_client'"

**Cause:** GraphQL client code not generated

**Solution:**
```bash
cd /Users/<USER>/projects/agentic-core

# Generate GraphQL client code
make graphql-client

# Verify generated files exist
ls backend/lib/graphql_client/

# Rebuild GraphQL container
docker compose build graphql
docker compose up -d graphql
```

### Issue: GraphQL log writer shows "Forbidden" or "4403" errors

**Cause:** System user lacks tenant entitlements for execution subscriptions

**Solution:**
```bash
# Run the entitlement setup script
cd /Users/<USER>/projects/axon-pfc
./scripts/setup_system_user_entitlements.sh

# This grants the system user access to all tenants
# If you still see errors, verify manually:
docker exec agentic-core-postgres-1 psql -U postgres -d invisible -c "
  SELECT te.tenant_id, t.name, te.claims
  FROM users.tenant_entitlements te
  JOIN users.tenant t ON t.id = te.tenant_id
  JOIN users.user u ON u.id = te.user_id
  WHERE u.email = 'system@internal';
"

# Restart log writer
pkill -f graphql_log_writer
PYTHONUNBUFFERED=1 python -u -m src.backend.services.graphql_log_writer > graphql_log_writer.log 2>&1 &
```

### Issue: "2 pending" messages in dashboard after cancelling

**Cause:** Orphaned RabbitMQ messages from cancelled executions

**Solution:**
```bash
# Purge the queue
docker exec agentic-core-rabbitmq-1 rabbitmqadmin purge queue name=agentic.async
```

### Issue: Backend can't import evaluation models

**Cause:** Migration not run or schema doesn't exist

**Solution:**
```bash
# Verify schema exists
docker exec agentic-core-postgres-1 psql -U postgres -d invisible -c "\dn"

# If 'evaluation' schema missing, run migration
cd /Users/<USER>/projects/axon-pfc
# (See Database Migration section above)
```

### Issue: Port conflicts (address already in use)

**Cause:** Services already running on ports 3000 or 8000

**Solution:**
```bash
# Find processes using the ports
lsof -ti:8000
lsof -ti:3000

# Kill them
kill $(lsof -ti:8000)
kill $(lsof -ti:3000)

# Or use stop.sh
./stop.sh
./start.sh
```

---

## Stopping the System

```bash
cd /Users/<USER>/projects/axon-pfc
./stop.sh
```

**What stop.sh does:**
1. Stops GraphQL log writer daemon
2. Stops eval_monitor daemon
3. Stops frontend server
4. Stops backend server
5. Cleans up PID files

**Note:** This does NOT stop agentic-core services (PostgreSQL, RabbitMQ, GraphQL, workers). To stop those:

```bash
cd /Users/<USER>/projects/agentic-core
docker compose down
```

---

## Directory Structure

```
axon-pfc/
├── SETUP.md                      # This file
├── README.md                     # Project overview
├── docs/                         # Detailed documentation
│   ├── architecture.md
│   ├── evaluation-system.md
│   ├── api-reference.md
│   ├── workflow-guide.md
│   └── log-writer-service.md    # GraphQL log writer documentation
├── migrations/
│   └── add_evaluation_schema.sql
├── src/
│   ├── backend/
│   │   ├── main.py              # FastAPI application
│   │   ├── models/              # SQLAlchemy models
│   │   ├── routes/              # API endpoints
│   │   │   ├── evaluation.py   # Evaluation endpoints (includes /logs)
│   │   │   └── ...
│   │   └── services/            # Background services
│   │       ├── eval_monitor.py        # Validation daemon
│   │       ├── eval_runner.py         # Execution orchestrator
│   │       └── graphql_log_writer.py  # GraphQL event subscriber
│   └── frontend/
│       ├── index.html
│       ├── app.js               # React application
│       └── style.css
├── tests/
│   ├── test_api.py
│   └── test_json_serializer.py
├── docker-compose.ports.yml      # Static port mappings for agentic-core
├── start.sh                      # Start all services
├── stop.sh                       # Stop all services
├── requirements.txt              # Python dependencies
└── .env                          # Environment configuration
```

---

## Next Steps

1. **Read Architecture Docs:** See `docs/architecture.md` for system design
2. **Create Workflows:** See `docs/workflow-guide.md` for workflow creation
3. **Run Tests:** See `docs/evaluation-system.md` for testing workflows
4. **API Integration:** See `docs/api-reference.md` for API details

---

## Support

- **Issues:** Check troubleshooting section above
- **Logs:**
  - Backend: `tail -f backend.log`
  - Frontend: `tail -f frontend.log`
  - Eval Monitor: `tail -f eval_monitor.log`
  - GraphQL Log Writer: `tail -f graphql_log_writer.log`
  - Agentic-Core: `docker compose logs -f` (in agentic-core directory)
  - Execution Logs: `/Users/<USER>/projects/agentic-core/artifacts/{execution_id}/flow-execution.log`

---

**Last Updated:** 2025-10-15  
**Version:** 1.0

