# Workflow JSON Generator - Update Complete

## Summary

The `workflow_json_generator.py` has been successfully updated to generate Axon workflows following the new team-based guidelines provided in the instruction document.

## Critical Understanding

There are **two different structures** at play:

### 1. OpenAI Agents SDK Structure (How the Generator Works)
The generator itself uses a **sequential 4-agent pattern** (same as `workflow_spec_generator.py`):
- Individual Agent objects  
- Sequential handoffs via Runner.run()
- Each agent produces structured Pydantic output
- Agents: Specification Analyzer → Workflow Architect → Team Designer → JSON Compiler

### 2. Axon Workflow Structure (What the Generator Outputs)
The workflows it creates follow the **new Axon team pattern**:
- All steps are `type: "team"`
- First agent in each team is **coordinator/leader**
- Coordinator uses `{roles}`, `{history}`, `{participants}` for routing
- Remaining agents are **specialists** with procedural skills
- Clear termination patterns (TERMINATE)
- Proper `save_variable` usage

## What Was Updated

### Agent Instructions Enhanced

**Workflow Architect Agent:**
- Now explicitly designs "team" type steps (no delegates)
- Thinks in terms of teams with coordinators and specialists

**Team Designer Agent (MAJOR UPDATE):**
```
CRITICAL - AXON TEAM DESIGN PATTERN:

Each team MUST follow this structure:
1. FIRST agent is the COORDINATOR/LEADER
   - Coordinates and routes work to specialists
   - Uses {roles}, {history}, {participants} for speaker selection
   - NEVER creates content - only delegates
   
2. REMAINING agents (2-3) are SPECIALISTS
   - Each has focused, specific skills
   - Skills written as "When coordinator asks: 1) Do X, 2) Do Y, 3) Hand back"
   - Use save_variable when they need to persist outputs
```

Includes complete patterns for:
- Coordinator agent structure
- Specialist agent structure  
- Termination handling
- Variable management

**JSON Compiler Agent:**
- Validates all Axon requirements
- Ensures coordinator is first in every team
- Verifies `{roles}`, `{history}`, `{participants}` in coordinator skills
- Checks specialist skills are procedural

## Example Output Structure

The generator now produces workflows like this:

```json
{
  "description": "Workflow description",
  "variables": {
    "input": {
      "topic": {
        "type": "string",
        "description": "Input description"
      }
    },
    "output": {
      "result": {
        "type": "string",
        "description": "Output description"
      }
    }
  },
  "steps": [
    {
      "order": 1,
      "type": "team",
      "team": {
        "name": "Task Team",
        "goal": "@task_coordinator: Read the 'topic' variable and coordinate...",
        "results": "- Output 1\n- Output 2\n",
        "termination": {
          "regex": ["TERMINATE"]
        },
        "agents": [
          {
            "name": "task_coordinator",
            "model": "gpt-5-mini",
            "persona": "Coordinator who delegates. Never creates content.",
            "skills": "You are the coordinator:\n\n1. Ask @specialist1 to do X\n2. Ask @specialist2 to do Y\n3. When done, say TERMINATE\n\n{roles}\n{history}\n{participants}"
          },
          {
            "name": "specialist1",
            "model": "gpt-5",
            "persona": "Expert in task X",
            "skills": "When coordinator asks:\n1. Do specific task X\n2. Use save_variable if needed\n3. Say: 'Complete. Coordinator, next step.'\n4. DO NOT do other work"
          }
        ],
        "variables": {}
      }
    }
  ]
}
```

## Model Selection

Agents intelligently use `gpt-5` or `gpt-5-mini`:

- **Coordinators**: Always use `gpt-5-mini` (routing and delegation is simple)
- **Complex specialists**: Use `gpt-5` (analysis, writing, synthesis, complex reasoning)
- **Simple specialists**: Use `gpt-5-mini` (extraction, validation, simple formatting)

This optimizes for both performance and cost.

## Key Principles from the Guidelines Now Implemented

✅ **Decide the steps (teams only)** - Workflow architect designs team-based steps  
✅ **Design each team's agents** - Team designer creates coordinator + specialists  
✅ **First agent is leader** - Coordinator always first in agent list  
✅ **Leader coordinates, doesn't create** - Explicit in persona and skills  
✅ **Specialists have focused skills** - Procedural, "When coordinator asks:" format  
✅ **Clear termination** - Unambiguous TERMINATE patterns  
✅ **Variable management** - save_variable instructions in specialist skills  
✅ **Outcome-oriented language** - Goals describe what to accomplish, not how  

## Testing

To test the updated generator:

1. **Provide a spec document** - Can be simple or complex
2. **Generate workflow** - Use the API endpoint or service directly
3. **Verify structure**:
   - All steps are `type: "team"`
   - First agent in each team is coordinator
   - Coordinator has `{roles}`, `{history}`, `{participants}`
   - Specialists have procedural skills
   - Termination patterns are clear
4. **Check quality**:
   - Goals are outcome-oriented
   - Skills are specific and actionable
   - Variable handling is correct

## Files Modified

- `/Users/<USER>/projects/axon-pfc/src/backend/services/workflow_json_generator.py`
  - Updated module docstring to clarify SDK vs Axon structure
  - Enhanced all 4 agent instructions with Axon guidelines
  - Updated code comments for clarity
  - No changes to execution logic (still sequential handoff)

## No Changes Needed To

- Execution flow (still sequential)
- Pydantic models (still valid)
- API endpoints (still compatible)
- Frontend integration (still works)

The change is purely in **what the agents know** and **what they generate**.

---

Generated: October 30, 2025

