# Workflow 'object' Type Fix

## Issue
The workflows page in agentic-core UI was showing error:
```
Error loading workflows: Enum 'AnyPrimitiveType' cannot represent value: 'object'
```

## Root Cause
Two workflows in the database had variables with type `"object"`, which is not a valid value for the GraphQL `AnyPrimitiveType` enum. The enum only supports primitive types:
- String
- Int
- Float
- Boolean
- Secret
- File

## Affected Workflows
1. **workflow spec For a given IC memo, assess all the risks you can 2** (ID: 5b758f0d-3d9b-4c55-82a6-f0daf9cfb564)
   - Had `traceability_matrix` output variable with type "object"

2. **workflow spec ndgjnrewjognrejongkjrengoje** (ID: cfb13e5d-17e1-4698-9e7d-c6226c0ac495)
   - Had variables with type "object"

## Solution Applied

### 1. Updated Workflow JSON Serializer
Modified `/Users/<USER>/projects/axon-pfc/src/backend/workflow_json_serializer.py`:

- Added handling for "object" type in the `transform_variables_to_array()` function
- Objects are now converted to "String" type (will be JSON-serialized)
- Also added support for "integer" as alias for "int"

```python
elif var_type.lower() == 'object':
    var_type = 'String'  # Objects stored as JSON-serialized String
```

### 2. Fixed Database Records
Ran SQL updates to convert existing "object" types to "String":

```sql
-- Updated 2 flow_versions in output_variables
-- Updated 1 flow_version in input_variables
UPDATE agentic_objects.flow_version
SET output_variables = (
    SELECT jsonb_agg(
        CASE 
            WHEN elem->>'type' = 'object' 
            THEN jsonb_set(elem, '{type}', '"String"')
            ELSE elem
        END
    )
    FROM jsonb_array_elements(output_variables) elem
)
WHERE output_variables::text LIKE '%"type"%"object"%';
```

### 3. Restarted Backend
Restarted the backend service to apply the code changes.

## Verification
- Confirmed no workflows in database have "object" type anymore
- Backend server restarted successfully
- Workflows page should now load without GraphQL enum errors

## Prevention
The updated serializer will now automatically convert any "object" types to "String" when new workflows are created or updated, preventing this issue from occurring again.

## Date
October 30, 2025


