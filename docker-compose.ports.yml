# Docker Compose override for static ports
# This file is used by start.sh to set fixed ports for local development
# It's applied on top of agentic-core's docker-compose.yml

services:
  postgres:
    ports:
      - "5432:5432"
  
  rabbitmq:
    ports:
      - "5672:5672"
      - "15672:15672"
  
  graphql:
    ports:
      - "5002:8000"
  
  # Agentic-core frontend (web UI)
  ui:
    ports:
      - "3001:8080"
  
  # Increase async worker concurrency for parallel execution
  async-engine-async:
    environment:
      NUM_TASKS: "15"  # Process up to 15 workflows in parallel

