{"name": "Simple PPT Test", "description": "Simple test to verify PPT ingestion tool works", "variables": {"input": {"file_path": {"type": "string", "description": "Path to PPTX or PDF file"}}, "output": {"result": {"type": "string", "description": "Processing result"}}}, "steps": [{"order": 1, "type": "team", "team": {"name": "Simple Test Team", "goal": "Get the file path, process it with ppt_ingestion, and save the result", "results": "Result variable with slide count", "termination": {"regex": ["TERMINATE", "FINAL_COMPLETE"]}, "max_turns": 10, "agents": [{"name": "coordinator", "model": "gpt-4o", "persona": "You start the workflow by getting the file_path input.", "skills": "STEP 1: Call read_input(variable_name='file_path', is_text=True) to get the presentation file path.\n\nSTEP 2: After you receive the file path from read_input, tell @processor: 'Please process this file: <the actual file path you received>'\n\nSTEP 3: When @processor says DONE, you say: TERMINATE", "tools": ["read_input"]}, {"name": "processor", "model": "gpt-4o", "persona": "You process presentation files.", "skills": "WAIT for @coordinator to give you a file path.\n\nWhen coordinator tells you the file path:\n1. Call ppt_ingestion tool using process_presentation(file_path='<the path coordinator gave you>', use_vision=True, output_format='text')\n2. After getting results with slide count, save using: save_variable('result', 'Successfully processed X slides from file')\n3. Say: DONE - @coordinator can terminate\n\nDO NOT ask for file path - coordinator will provide it!", "tools": ["ppt_ingestion", "save_variable"]}, {"name": "validator", "model": "gpt-4o", "persona": "You validate completion.", "skills": "When @coordinator says TERMINATE, you say: FINAL_COMPLETE", "tools": ["save_variable"]}], "variables": {"file_path": {"type": "string"}}}}]}