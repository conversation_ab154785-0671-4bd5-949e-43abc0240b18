{"name": "Quick Presentation Summarizer", "description": "Fast extraction and summarization of presentation content - perfect for quick reviews", "variables": {"input": {"file_path": {"type": "string", "description": "Path to PPTX or PDF file"}}, "output": {"summary": {"type": "string", "description": "Concise summary of the presentation"}, "key_points": {"type": "string", "description": "Bullet points of main takeaways"}}}, "steps": [{"order": 1, "type": "team", "team": {"name": "Summarization Team", "goal": "Extract and summarize presentation content quickly", "results": "A concise summary and key bullet points", "termination": {"regex": ["SUMMARY_COMPLETE", "TERMINATE"]}, "agents": [{"name": "coordinator", "model": "gpt-4o", "persona": "You coordinate the summarization workflow.", "skills": "Ask @summarizer to process and summarize the file: ${file_path}\n\nWait for their response, then say: TERMINATE", "tools": ["save_variable"]}, {"name": "summarizer", "model": "gpt-4o", "persona": "You create clear, concise summaries that capture the essence of content.", "skills": "When coordinator asks:\n1. Use ppt_ingestion tool on ${file_path}\n2. From the extracted content, create:\n   - A 3-4 paragraph executive summary\n   - 5-7 bullet points of key takeaways\n3. Save summary using save_variable('summary', '<summary text>')\n4. Save points using save_variable('key_points', '<bullet points>')\n5. Say: 'Done! Coordinator can terminate.'", "tools": ["ppt_ingestion", "save_variable"]}], "variables": {"file_path": {"type": "string"}}}}]}