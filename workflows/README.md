# PPT Ingestion Workflows

Example workflows that use the `ppt_ingestion` tool to process and analyze presentations.

## 📋 Available Workflows

### 1. Presentation Analyzer (`ppt_analysis_workflow.json`)

**Purpose**: Comprehensive analysis of presentations with customizable focus areas.

**Inputs**:
- `presentation_path` (string): Path to PPTX or PDF file
- `analysis_focus` (string): Focus area - "summary", "financial", "technical", "marketing", or "key_points"

**Outputs**:
- `extracted_content` (string): Full text from all slides
- `analysis` (string): Detailed analysis based on focus area
- `slide_count` (integer): Number of slides processed

**Use Cases**:
- Executive summaries of company presentations
- Financial analysis of quarterly reports
- Technical review of architecture decks
- Marketing content analysis

**Example**:
```json
{
  "presentation_path": "/Users/<USER>/Documents/Q4_Report.pptx",
  "analysis_focus": "financial"
}
```

---

### 2. Presentation Comparison (`ppt_comparison_workflow.json`)

**Purpose**: Compare two presentations side-by-side.

**Inputs**:
- `presentation_1_path` (string): First presentation
- `presentation_2_path` (string): Second presentation

**Outputs**:
- `comparison_report` (string): Detailed comparison highlighting similarities and differences

**Use Cases**:
- Compare versions of the same deck
- Analyze competitor presentations
- Review before/after presentations
- Track changes across revisions

**Example**:
```json
{
  "presentation_1_path": "/Users/<USER>/Documents/v1.pptx",
  "presentation_2_path": "/Users/<USER>/Documents/v2.pptx"
}
```

---

### 3. Quick Summarizer (`ppt_summarizer_workflow.json`)

**Purpose**: Fast extraction and summarization for quick reviews.

**Inputs**:
- `file_path` (string): Path to presentation

**Outputs**:
- `summary` (string): 3-4 paragraph executive summary
- `key_points` (string): 5-7 bullet points of main takeaways

**Use Cases**:
- Quick review before meetings
- Email summaries to stakeholders
- TL;DR for long presentations
- Meeting prep

**Example**:
```json
{
  "file_path": "/Users/<USER>/Documents/strategy_deck.pptx"
}
```

---

### 4. Presentation Q&A (`ppt_qa_workflow.json`)

**Purpose**: Answer specific questions about presentation content.

**Inputs**:
- `presentation_path` (string): Path to presentation
- `question` (string): Question to answer

**Outputs**:
- `answer` (string): Comprehensive answer to the question
- `relevant_excerpts` (string): Quoted sections supporting the answer

**Use Cases**:
- "What was the revenue growth?"
- "What are the main risks mentioned?"
- "What's the timeline for the project?"
- "Who are the key stakeholders?"

**Example**:
```json
{
  "presentation_path": "/Users/<USER>/Documents/product_roadmap.pptx",
  "question": "What features are planned for Q1 2025?"
}
```

---

## 🚀 How to Use

### Via API

```bash
# 1. Create the workflow
curl -X POST http://localhost:8000/api/workflows \
  -H "Content-Type: application/json" \
  -d @workflows/ppt_analysis_workflow.json

# 2. Run it (use the workflow ID from step 1)
curl -X POST http://localhost:8000/api/workflows/{WORKFLOW_ID}/run \
  -H "Content-Type: application/json" \
  -d '{
    "input_variables": {
      "presentation_path": "/path/to/your/file.pptx",
      "analysis_focus": "summary"
    }
  }'
```

### Via Agentic-Core UI

1. Open Agentic-Core UI
2. Go to Workflows section
3. Click "Create Workflow"
4. Copy and paste one of the workflow JSONs
5. Click "Save"
6. Click "Run" and provide input values

### Via Axon-PFC Dashboard

1. Open http://localhost:8080
2. Navigate to Workflows
3. Import one of the workflow JSON files
4. Configure input variables
5. Execute

---

## 💡 Customization Tips

### Change the Model

Update the `model` field in agents:
```json
{
  "name": "analyzer",
  "model": "gpt-4o-mini",  // Use cheaper/faster model
  ...
}
```

### Adjust Analysis Style

Modify the agent's `skills` to change behavior:
```json
{
  "skills": "Provide analysis in bullet-point format only, maximum 10 points..."
}
```

### Add More Agents

Add specialized agents for specific tasks:
```json
{
  "name": "fact_checker",
  "model": "gpt-4o",
  "persona": "You verify facts and check for inconsistencies.",
  "skills": "Review the content and identify any contradictions...",
  "tools": ["save_variable"]
}
```

### Chain Multiple Workflows

Use the output of one workflow as input to another:
1. Run presentation analyzer
2. Take the `analysis` output
3. Feed it into another workflow for formatting/reporting

---

## 🔧 Required Tool

All workflows require the **ppt_ingestion** tool to be registered in Agentic-Core.

**Verify it's registered:**
```bash
python scripts/verify_ppt_tool.py
```

**If not registered:**
```bash
python scripts/register_ppt_tool.py
```

---

## 📊 Performance

| Workflow | Avg Time | Use Case |
|----------|----------|----------|
| Summarizer | 30-60s | Quick reviews |
| Analyzer | 60-120s | Detailed analysis |
| Comparison | 90-180s | Side-by-side review |
| Q&A | 30-90s | Specific questions |

*Times vary based on presentation size and GPT-4 API speed*

---

## 🎯 Best Practices

1. **Use Absolute Paths**: Always provide full paths to files
   - ✅ `/Users/<USER>/Documents/file.pptx`
   - ❌ `~/Documents/file.pptx`
   - ❌ `./file.pptx`

2. **Vision Mode**: Enable for presentations with:
   - Complex tables
   - Charts and graphs
   - Diagrams
   - Mixed visual content

3. **File Access**: Ensure workflow workers can access the files
   - Files should be on mounted volumes
   - Check file permissions
   - Use network paths accessible to workers

4. **Error Handling**: Check the `success` field in tool output
   - If `false`, check the `error` field
   - Common issues: file not found, unsupported format

5. **Large Files**: For 100+ slide decks:
   - Consider using OCR-only mode (faster)
   - Split into smaller workflows
   - Process critical slides with vision

---

## 🐛 Troubleshooting

### "Tool not found"
```bash
# Re-register the tool
python scripts/register_ppt_tool.py
```

### "File not found"
- Use absolute paths
- Check file exists: `ls -la /path/to/file.pptx`
- Verify workers have file access

### "Processing failed"
- Check backend logs: `tail -f backend.log`
- Verify OpenAI API key is set
- Ensure file format is PPTX or PDF

### Slow processing
- Use `"use_vision": false` for faster OCR-only
- Reduce file size if possible
- Check API rate limits

---

## 📚 Additional Resources

- **Tool Documentation**: `docs/ppt-ingestion.md`
- **Tool Registration**: `PPT_TOOL_REGISTERED.md`
- **API Reference**: http://localhost:8000/docs

---

## 🎨 Example Output

### Analyzer Workflow Output:
```
analysis: "This Q4 financial presentation shows strong performance...

Key Findings:
1. Revenue: $45.2M (↑23% YoY)
2. Profit Margin: 28.3% (↑5.1 points)
3. Customer Growth: 1,247 new customers

Concerns:
- Operating expenses increased 15%
- Cash flow declined in November

Recommendations:
- Focus on cost optimization
- Accelerate AR collection..."
```

### Summarizer Output:
```
summary: "The strategic roadmap presentation outlines a 3-year vision..."

key_points: "
• Launch new product line in Q2 2025
• Expand to European market by Q3 2025  
• AI integration across all platforms
• Target 50% revenue growth
• Focus on enterprise customers"
```

---

**Created**: 2025-10-24
**Version**: 1.0.0

