{"name": "Presentation Analyzer", "description": "Process PowerPoint or PDF presentations and provide intelligent analysis", "variables": {"input": {"presentation_path": {"type": "string", "description": "Absolute path to the PPTX or PDF file to analyze"}, "analysis_focus": {"type": "string", "description": "What to focus on: 'summary', 'financial', 'technical', 'marketing', or 'key_points'", "default": "summary"}}, "output": {"extracted_content": {"type": "string", "description": "Full text content extracted from the presentation"}, "analysis": {"type": "string", "description": "AI-generated analysis based on the focus area"}, "slide_count": {"type": "integer", "description": "Number of slides processed"}}}, "steps": [{"order": 1, "type": "team", "team": {"name": "Presentation Processing Team", "goal": "Process the presentation file, extract all content, and provide insightful analysis based on the specified focus area", "results": "Complete text extraction from all slides and a detailed analysis report", "termination": {"regex": ["ANALYSIS_COMPLETE", "TERMINATE"]}, "agents": [{"name": "document_processor", "model": "gpt-4o", "persona": "You are an expert document processor who extracts information from presentations efficiently and accurately.", "skills": "Use the ppt_ingestion tool to process the presentation file at path: ${presentation_path}\n\nCall the tool and provide the file_path parameter. The tool will return extracted text content.\n\nAfter receiving the results, save the extracted content using save_variable('extracted_content', '<the full text content>').\nAlso save the slide count using save_variable('slide_count', <number of slides>).\n\nThen say: 'Content extracted successfully. @content_analyzer please analyze this content.'", "tools": ["ppt_ingestion", "save_variable"]}, {"name": "content_analyzer", "model": "gpt-4o", "persona": "You are an expert content analyst who provides insightful analysis of presentation materials.", "skills": "Wait for @document_processor to extract the content.\n\nOnce you receive the extracted_content variable, analyze it based on the focus area: ${analysis_focus}\n\nProvide analysis based on the focus:\n- summary: Executive summary with key takeaways\n- financial: Financial metrics, trends, and insights\n- technical: Technical details, architecture, implementation\n- marketing: Marketing messages, value propositions, target audience\n- key_points: Bullet-point list of most important information\n\nSave your analysis using save_variable('analysis', '<your detailed analysis>').\n\nThen say: 'TERMINATE'", "tools": ["save_variable"]}], "variables": {"presentation_path": {"type": "string", "description": "Path to the presentation file"}, "analysis_focus": {"type": "string", "description": "Focus area for analysis"}, "extracted_content": {"type": "string", "description": "Extracted content from presentation"}, "slide_count": {"type": "integer", "description": "Number of slides"}}}}]}