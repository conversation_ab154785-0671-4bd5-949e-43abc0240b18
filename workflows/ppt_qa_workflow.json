{"name": "Presentation Q&A", "description": "Extract presentation content and answer specific questions about it", "variables": {"input": {"presentation_path": {"type": "string", "description": "Path to the presentation file"}, "question": {"type": "string", "description": "Question to answer about the presentation"}}, "output": {"answer": {"type": "string", "description": "Answer to the question based on presentation content"}, "relevant_excerpts": {"type": "string", "description": "Relevant sections from the presentation"}}}, "steps": [{"order": 1, "type": "team", "team": {"name": "Q&A Team", "goal": "Process the presentation and answer the user's question with supporting evidence", "results": "A well-researched answer with relevant excerpts from the presentation", "termination": {"regex": ["ANSWER_COMPLETE", "TERMINATE"]}, "agents": [{"name": "coordinator", "model": "gpt-4o", "persona": "You coordinate the Q&A process.", "skills": "Ask @researcher to answer the question: ${question}\nAbout the file: ${presentation_path}\n\nWait for their response, then say: TERMINATE", "tools": ["save_variable"]}, {"name": "researcher", "model": "gpt-4o", "persona": "You are a thorough researcher who finds accurate information and cites sources.", "skills": "When coordinator asks:\n1. Use ppt_ingestion on ${presentation_path}\n2. Review the extracted content\n3. Find information relevant to: ${question}\n4. Extract relevant sections and quote them\n5. Save excerpts: save_variable('relevant_excerpts', '<quoted sections>')\n6. Formulate a comprehensive answer\n7. Save answer: save_variable('answer', '<your answer>')\n8. Say: 'Done! Coordinator can terminate.'", "tools": ["ppt_ingestion", "save_variable"]}], "variables": {"presentation_path": {"type": "string"}, "question": {"type": "string"}}}}]}