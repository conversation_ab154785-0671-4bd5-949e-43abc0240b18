{"name": "Presentation Comparison", "description": "Compare two presentations and identify key differences, similarities, and insights", "variables": {"input": {"presentation_1_path": {"type": "string", "description": "Path to the first presentation"}, "presentation_2_path": {"type": "string", "description": "Path to the second presentation"}}, "output": {"comparison_report": {"type": "string", "description": "Detailed comparison report"}}}, "steps": [{"order": 1, "type": "team", "team": {"name": "Comparison Team", "goal": "Process both presentations and create a comprehensive comparison report", "results": "A detailed report highlighting similarities, differences, and key insights from both presentations", "termination": {"regex": ["COMPARISON_COMPLETE", "TERMINATE"]}, "agents": [{"name": "document_processor", "model": "gpt-4o", "persona": "You efficiently process multiple documents in parallel.", "skills": "Process both presentations:\n1. Use ppt_ingestion tool on ${presentation_1_path} first\n2. Save result as save_variable('content_1', '<text>')\n3. Then use ppt_ingestion tool on ${presentation_2_path}\n4. Save result as save_variable('content_2', '<text>')\n5. Say: '@analyzer both documents are ready for comparison.'", "tools": ["ppt_ingestion", "save_variable"]}, {"name": "analyzer", "model": "gpt-4o", "persona": "You are an expert at comparative analysis.", "skills": "Once both documents are processed, compare ${content_1} and ${content_2}.\n\nCreate a report covering:\n1. Main themes in each presentation\n2. Key similarities\n3. Important differences\n4. Unique insights from each\n5. Overall assessment\n\nSave using save_variable('comparison_report', '<report>').\nThen say: 'TERMINATE'", "tools": ["save_variable"]}], "variables": {"presentation_1_path": {"type": "string"}, "presentation_2_path": {"type": "string"}, "content_1": {"type": "string"}, "content_2": {"type": "string"}}}}]}