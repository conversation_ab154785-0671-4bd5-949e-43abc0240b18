#!/usr/bin/env python3
"""
Example usage of the PPT Ingestion Service

This script demonstrates how to use the PPT ingestion service
to process PowerPoint and PDF files.
"""

import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.backend.services.ppt_ingestion import get_ppt_ingestion_service


def example_basic_processing():
    """Example: Basic file processing."""
    print("=" * 80)
    print("Example 1: Basic File Processing")
    print("=" * 80)
    
    # Initialize service
    service = get_ppt_ingestion_service()
    
    # Process a file (replace with your file path)
    file_path = "path/to/your/presentation.pptx"
    
    print(f"\nProcessing: {file_path}")
    slides = service.process_file(
        file_path=file_path,
        use_vision=True
    )
    
    print(f"\nExtracted {len(slides)} slides")
    
    # Display first slide
    if slides:
        first_slide = slides[0]
        print(f"\nFirst slide preview:")
        print(f"OCR Text: {first_slide.ocr_text[:200]}...")
        if first_slide.enhanced_text:
            print(f"Enhanced Text: {first_slide.enhanced_text[:200]}...")


def example_ocr_only():
    """Example: Fast OCR-only processing."""
    print("\n" + "=" * 80)
    print("Example 2: OCR-Only Processing (Fast)")
    print("=" * 80)
    
    service = get_ppt_ingestion_service()
    
    file_path = "path/to/your/document.pdf"
    
    print(f"\nProcessing: {file_path} (OCR only)")
    slides = service.process_file(
        file_path=file_path,
        use_vision=False  # Disable vision for faster processing
    )
    
    print(f"Extracted {len(slides)} pages")


def example_custom_prompt():
    """Example: Using a custom vision prompt."""
    print("\n" + "=" * 80)
    print("Example 3: Custom Vision Prompt")
    print("=" * 80)
    
    service = get_ppt_ingestion_service()
    
    custom_prompt = """
You are analyzing a technical presentation slide about data science.

OCR Text:
{ocr_text}

Please provide:
1. All text content with corrected formatting
2. Detailed descriptions of any code snippets
3. Explanations of any diagrams or flowcharts
4. Table data in markdown format

Focus on technical accuracy and detail.
"""
    
    file_path = "path/to/technical/presentation.pptx"
    
    print(f"\nProcessing: {file_path} with custom prompt")
    slides = service.process_file(
        file_path=file_path,
        use_vision=True,
        vision_prompt=custom_prompt
    )
    
    print(f"Processed {len(slides)} slides with custom analysis")


def example_export_formats():
    """Example: Exporting to different formats."""
    print("\n" + "=" * 80)
    print("Example 4: Export to Different Formats")
    print("=" * 80)
    
    service = get_ppt_ingestion_service()
    
    file_path = "path/to/your/presentation.pptx"
    slides = service.process_file(file_path, use_vision=True)
    
    # Export to text
    text_output = "output_text.txt"
    service.export_to_text(slides, text_output)
    print(f"\nExported to text: {text_output}")
    
    # Export to JSON
    json_output = "output_data.json"
    service.export_to_json(slides, json_output)
    print(f"Exported to JSON: {json_output}")


def example_batch_processing():
    """Example: Process multiple files."""
    print("\n" + "=" * 80)
    print("Example 5: Batch Processing")
    print("=" * 80)
    
    service = get_ppt_ingestion_service()
    
    files = [
        "presentations/deck1.pptx",
        "presentations/deck2.pptx",
        "presentations/report.pdf"
    ]
    
    for file_path in files:
        print(f"\nProcessing: {file_path}")
        try:
            slides = service.process_file(file_path, use_vision=True)
            
            # Export each file
            output_name = Path(file_path).stem + "_processed.txt"
            service.export_to_text(slides, output_name)
            print(f"  ✓ Exported {len(slides)} slides to {output_name}")
        
        except Exception as e:
            print(f"  ✗ Error: {e}")


def example_slide_analysis():
    """Example: Analyzing specific slides."""
    print("\n" + "=" * 80)
    print("Example 6: Analyzing Specific Slides")
    print("=" * 80)
    
    service = get_ppt_ingestion_service()
    
    file_path = "path/to/your/presentation.pptx"
    slides = service.process_file(file_path, use_vision=True)
    
    # Find slides with specific content
    keyword = "revenue"
    matching_slides = [
        slide for slide in slides
        if keyword.lower() in (slide.enhanced_text or slide.ocr_text).lower()
    ]
    
    print(f"\nFound {len(matching_slides)} slides containing '{keyword}':")
    for slide in matching_slides:
        print(f"  - Slide {slide.page_number}")


def main():
    """Run all examples."""
    print("\nPPT Ingestion Service - Examples")
    print("=" * 80)
    print("\nNote: Update file paths in the examples before running!")
    print()
    
    # Uncomment the examples you want to run:
    
    # example_basic_processing()
    # example_ocr_only()
    # example_custom_prompt()
    # example_export_formats()
    # example_batch_processing()
    # example_slide_analysis()
    
    print("\n" + "=" * 80)
    print("Examples complete!")


if __name__ == "__main__":
    main()

