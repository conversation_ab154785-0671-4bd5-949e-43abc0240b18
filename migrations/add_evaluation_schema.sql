-- Migration: Add Evaluation Schema (REVISED - No Duplication)
-- Description: Minimal evaluation schema that extends flow_execution rather than duplicating it
-- Date: 2025-10-15
-- Author: System

-- ============================================================================
-- Create Schema
-- ============================================================================

CREATE SCHEMA IF NOT EXISTS evaluation;

-- ============================================================================
-- Create Tables
-- ============================================================================

-- Test Case Definitions
CREATE TABLE evaluation.eval_case (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES agentic_objects.flow(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    input_variables JSONB NOT NULL,  
    expected_output JSONB,            
    validation_rules JSONB,           -- Optional: JSON schema, custom rules
    tags TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_by UUID NOT NULL REFERENCES users.user(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT unique_eval_case_name_per_workflow UNIQUE(workflow_id, name)
);

COMMENT ON TABLE evaluation.eval_case IS 'Test case definitions - what inputs to use and what outputs to expect';
COMMENT ON COLUMN evaluation.eval_case.input_variables IS 'Inputs for the workflow (same format as FlowExecution.variables)';
COMMENT ON COLUMN evaluation.eval_case.expected_output IS 'Expected outputs for validation';
COMMENT ON COLUMN evaluation.eval_case.validation_rules IS 'Custom validation rules (JSON schema, regex, etc.)';

-- Test Suites - Group related test cases
CREATE TABLE evaluation.eval_suite (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES agentic_objects.flow(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_by UUID NOT NULL REFERENCES users.user(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT unique_suite_name_per_workflow UNIQUE(workflow_id, name)
);

COMMENT ON TABLE evaluation.eval_suite IS 'Groups of test cases that can be run together';

-- Suite-Case mapping
CREATE TABLE evaluation.eval_suite_cases (
    suite_id UUID NOT NULL REFERENCES evaluation.eval_suite(id) ON DELETE CASCADE,
    eval_case_id UUID NOT NULL REFERENCES evaluation.eval_case(id) ON DELETE CASCADE,
    order_index INTEGER NOT NULL,
    PRIMARY KEY (suite_id, eval_case_id)
);

-- Batch Runs - Track bulk test execution (must come before eval_run due to FK)
CREATE TABLE evaluation.eval_batch_run (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES agentic_objects.flow(id),
    suite_id UUID REFERENCES evaluation.eval_suite(id),
    name VARCHAR(255),
    started_by UUID NOT NULL REFERENCES users.user(id),
    started_at TIMESTAMP DEFAULT NOW(),
    status VARCHAR(50) NOT NULL DEFAULT 'RUNNING',
    
    CONSTRAINT valid_batch_status CHECK (status IN ('RUNNING', 'COMPLETED', 'CANCELLED'))
);

COMMENT ON TABLE evaluation.eval_batch_run IS 'Tracks bulk execution of multiple test cases';

-- Eval Runs - Links cases to executions + validation results ONLY
CREATE TABLE evaluation.eval_run (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    eval_case_id UUID NOT NULL REFERENCES evaluation.eval_case(id) ON DELETE CASCADE,
    flow_execution_id UUID NOT NULL UNIQUE REFERENCES flow_execution.flow_execution(id) ON DELETE CASCADE,
    batch_run_id UUID REFERENCES evaluation.eval_batch_run(id) ON DELETE SET NULL,
    
    -- ONLY evaluation-specific fields - everything else comes from flow_execution
    passed BOOLEAN,                    -- Validation result
    validation_output JSONB,           -- Detailed validation results
    validation_notes TEXT,             -- Human-readable explanation
    evaluated_at TIMESTAMP             -- When validation was performed
);

COMMENT ON TABLE evaluation.eval_run IS 'Links eval cases to flow executions and stores validation results. Get execution status/timing from flow_execution.';
COMMENT ON COLUMN evaluation.eval_run.flow_execution_id IS 'Foreign key to flow_execution - execution state lives there';
COMMENT ON COLUMN evaluation.eval_run.passed IS 'Whether the execution met expectations (NULL = not yet validated)';

-- ============================================================================
-- Create Indexes
-- ============================================================================

-- Eval Case Indexes
CREATE INDEX idx_eval_case_workflow ON evaluation.eval_case(workflow_id);
CREATE INDEX idx_eval_case_active ON evaluation.eval_case(is_active);
CREATE INDEX idx_eval_case_tags ON evaluation.eval_case USING GIN(tags);
CREATE INDEX idx_eval_case_created ON evaluation.eval_case(created_at DESC);

-- Eval Run Indexes
CREATE INDEX idx_eval_run_case ON evaluation.eval_run(eval_case_id);
CREATE INDEX idx_eval_run_execution ON evaluation.eval_run(flow_execution_id);
CREATE INDEX idx_eval_run_batch ON evaluation.eval_run(batch_run_id);
CREATE INDEX idx_eval_run_passed ON evaluation.eval_run(passed) WHERE passed IS NOT NULL;
CREATE INDEX idx_eval_run_evaluated ON evaluation.eval_run(evaluated_at DESC);

-- Eval Suite Indexes
CREATE INDEX idx_eval_suite_workflow ON evaluation.eval_suite(workflow_id);

-- Batch Run Indexes
CREATE INDEX idx_eval_batch_workflow ON evaluation.eval_batch_run(workflow_id);
CREATE INDEX idx_eval_batch_suite ON evaluation.eval_batch_run(suite_id);
CREATE INDEX idx_eval_batch_started ON evaluation.eval_batch_run(started_at DESC);

-- ============================================================================
-- Create Views - Join with flow_execution for complete picture
-- ============================================================================

-- View: Complete Eval Run Info (joins with flow_execution)
CREATE VIEW evaluation.eval_run_detail AS
SELECT 
    er.id as eval_run_id,
    er.eval_case_id,
    ec.name as eval_case_name,
    ec.expected_output,
    
    -- From flow_execution (execution state)
    er.flow_execution_id,
    fe.result as execution_status,
    fe.start_time as started_at,
    fe.end_time as completed_at,
    EXTRACT(EPOCH FROM (fe.end_time - fe.start_time)) * 1000 as duration_ms,
    fe.output_variables,
    fe.failure_reason as error_message,
    fe.failure_detail,
    
    -- From evaluation (validation results)
    er.passed,
    er.validation_output,
    er.validation_notes,
    er.evaluated_at,
    
    -- From workflow
    ec.workflow_id,
    f.name as workflow_name,
    
    -- From batch
    er.batch_run_id,
    eb.name as batch_name
    
FROM evaluation.eval_run er
JOIN evaluation.eval_case ec ON er.eval_case_id = ec.id
JOIN flow_execution.flow_execution fe ON er.flow_execution_id = fe.id
JOIN agentic_objects.flow f ON ec.workflow_id = f.id
LEFT JOIN evaluation.eval_batch_run eb ON er.batch_run_id = eb.id;

COMMENT ON VIEW evaluation.eval_run_detail IS 'Complete eval run information with execution state from flow_execution';

-- View: Test Case Statistics
CREATE VIEW evaluation.eval_case_stats AS
SELECT 
    ec.id,
    ec.workflow_id,
    ec.name,
    ec.description,
    ec.tags,
    ec.is_active,
    
    -- Execution counts (from flow_execution via eval_run)
    COUNT(er.id) as total_runs,
    COUNT(er.id) FILTER (WHERE fe.result = 'COMPLETE') as completed_runs,
    COUNT(er.id) FILTER (WHERE fe.result = 'FAILED') as failed_runs,
    COUNT(er.id) FILTER (WHERE fe.result = 'PENDING') as pending_runs,
    
    -- Validation counts
    COUNT(er.id) FILTER (WHERE er.passed = true) as passed_validations,
    COUNT(er.id) FILTER (WHERE er.passed = false) as failed_validations,
    COUNT(er.id) FILTER (WHERE er.passed IS NULL AND fe.result = 'COMPLETE') as unvalidated,
    
    -- Success rate (of validated runs)
    CASE 
        WHEN COUNT(er.id) FILTER (WHERE er.passed IS NOT NULL) > 0 
        THEN ROUND(
            (COUNT(er.id) FILTER (WHERE er.passed = true)::NUMERIC / 
             COUNT(er.id) FILTER (WHERE er.passed IS NOT NULL)) * 100,
            2
        )
        ELSE NULL
    END as success_rate,
    
    -- Timing
    MAX(fe.start_time) as last_run_at,
    AVG(EXTRACT(EPOCH FROM (fe.end_time - fe.start_time)) * 1000) 
        FILTER (WHERE fe.result = 'COMPLETE') as avg_duration_ms
        
FROM evaluation.eval_case ec
LEFT JOIN evaluation.eval_run er ON ec.id = er.eval_case_id
LEFT JOIN flow_execution.flow_execution fe ON er.flow_execution_id = fe.id
GROUP BY ec.id, ec.workflow_id, ec.name, ec.description, ec.tags, ec.is_active;

COMMENT ON VIEW evaluation.eval_case_stats IS 'Aggregated statistics for test cases using flow_execution for execution state';

-- View: Active Eval Runs
CREATE VIEW evaluation.active_eval_runs AS
SELECT 
    er.id as eval_run_id,
    ec.name as test_case_name,
    f.name as workflow_name,
    fe.result as status,
    fe.start_time as started_at,
    EXTRACT(EPOCH FROM (NOW() - fe.start_time)) as elapsed_seconds,
    er.batch_run_id,
    eb.name as batch_name
FROM evaluation.eval_run er
JOIN evaluation.eval_case ec ON er.eval_case_id = ec.id
JOIN flow_execution.flow_execution fe ON er.flow_execution_id = fe.id
JOIN agentic_objects.flow f ON ec.workflow_id = f.id
LEFT JOIN evaluation.eval_batch_run eb ON er.batch_run_id = eb.id
WHERE fe.result IN ('PENDING', 'RUNNING')
ORDER BY fe.start_time DESC;

COMMENT ON VIEW evaluation.active_eval_runs IS 'Currently executing eval runs';

-- View: Batch Run Progress
CREATE VIEW evaluation.batch_run_progress AS
SELECT 
    eb.id as batch_id,
    eb.name as batch_name,
    eb.workflow_id,
    f.name as workflow_name,
    eb.started_at,
    eb.status as batch_status,
    
    -- Execution counts (from flow_execution)
    COUNT(er.id) as total_cases,
    COUNT(er.id) FILTER (WHERE fe.result = 'COMPLETE') as completed,
    COUNT(er.id) FILTER (WHERE fe.result = 'FAILED') as failed,
    COUNT(er.id) FILTER (WHERE fe.result IN ('PENDING', 'RUNNING')) as in_progress,
    
    -- Validation counts
    COUNT(er.id) FILTER (WHERE er.passed = true) as passed,
    COUNT(er.id) FILTER (WHERE er.passed = false) as failed_validation,
    
    -- Progress percentage
    CASE 
        WHEN COUNT(er.id) > 0 
        THEN ROUND((COUNT(er.id) FILTER (WHERE fe.result IN ('COMPLETE', 'FAILED'))::NUMERIC / COUNT(er.id)) * 100, 2)
        ELSE 0
    END as progress_percent
    
FROM evaluation.eval_batch_run eb
JOIN agentic_objects.flow f ON eb.workflow_id = f.id
LEFT JOIN evaluation.eval_run er ON er.batch_run_id = eb.id
LEFT JOIN flow_execution.flow_execution fe ON er.flow_execution_id = fe.id
GROUP BY eb.id, eb.name, eb.workflow_id, f.name, eb.started_at, eb.status;

COMMENT ON VIEW evaluation.batch_run_progress IS 'Progress tracking for batch runs using flow_execution state';

-- View: All Executions with Source (eval vs manual)
CREATE VIEW evaluation.all_executions AS
SELECT 
    fe.id as execution_id,
    fe.flow_version_id,
    f.name as workflow_name,
    fe.result as status,
    fe.start_time,
    fe.end_time,
    EXTRACT(EPOCH FROM (fe.end_time - fe.start_time)) * 1000 as duration_ms,
    
    -- Eval info (NULL if manual execution)
    er.id as eval_run_id,
    ec.name as eval_case_name,
    er.passed as validation_passed,
    
    -- Source type
    CASE 
        WHEN er.id IS NOT NULL THEN 'eval'
        ELSE 'manual'
    END as source_type
    
FROM flow_execution.flow_execution fe
JOIN agentic_objects.flow_version fv ON fe.flow_version_id = fv.id
JOIN agentic_objects.flow f ON fv.flow_id = f.id
LEFT JOIN evaluation.eval_run er ON fe.id = er.flow_execution_id
LEFT JOIN evaluation.eval_case ec ON er.eval_case_id = ec.id
ORDER BY fe.start_time DESC;

COMMENT ON VIEW evaluation.all_executions IS 'All workflow executions with their source (eval or manual)';

-- ============================================================================
-- Grant Permissions
-- ============================================================================

GRANT USAGE ON SCHEMA evaluation TO postgres;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA evaluation TO postgres;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA evaluation TO postgres;

-- ============================================================================
-- Create Triggers
-- ============================================================================

CREATE OR REPLACE FUNCTION evaluation.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_eval_case_updated_at
    BEFORE UPDATE ON evaluation.eval_case
    FOR EACH ROW
    EXECUTE FUNCTION evaluation.update_updated_at_column();

CREATE TRIGGER update_eval_suite_updated_at
    BEFORE UPDATE ON evaluation.eval_suite
    FOR EACH ROW
    EXECUTE FUNCTION evaluation.update_updated_at_column();

-- ============================================================================
-- Verification Queries
-- ============================================================================

-- Run after migration to verify:

-- 1. Check all tables exist
-- SELECT table_name FROM information_schema.tables WHERE table_schema = 'evaluation' ORDER BY table_name;

-- 2. Check views
-- SELECT table_name FROM information_schema.views WHERE table_schema = 'evaluation' ORDER BY table_name;

-- 3. Test view (after you have data)
-- SELECT * FROM evaluation.eval_run_detail LIMIT 5;

-- ============================================================================
-- Rollback
-- ============================================================================

/*
DROP SCHEMA evaluation CASCADE;
*/

-- ============================================================================
-- Migration Complete
-- ============================================================================

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = 'evaluation') THEN
        RAISE NOTICE '✓ Migration successful: evaluation schema created (minimal, no duplication)';
    ELSE
        RAISE EXCEPTION '✗ Migration failed: evaluation schema not found';
    END IF;
END $$;

