# PPT Ingestion Workflows - Created! ✅

## 🎯 What Was Created

I've created **4 ready-to-use workflows** that demonstrate the `ppt_ingestion` tool in action.

## 📋 Workflow Files

### 1. **Presentation Analyzer** (`ppt_analysis_workflow.json`)
```
Input:  presentation_path, analysis_focus
Output: extracted_content, analysis, slide_count
```
**Use**: Comprehensive analysis with customizable focus (summary, financial, technical, marketing, key_points)

### 2. **Presentation Comparison** (`ppt_comparison_workflow.json`)
```
Input:  presentation_1_path, presentation_2_path
Output: comparison_report
```
**Use**: Side-by-side comparison of two presentations

### 3. **Quick Summarizer** (`ppt_summarizer_workflow.json`)
```
Input:  file_path
Output: summary, key_points
```
**Use**: Fast extraction and summarization for quick reviews

### 4. **Presentation Q&A** (`ppt_qa_workflow.json`)
```
Input:  presentation_path, question
Output: answer, relevant_excerpts
```
**Use**: Answer specific questions about presentation content

## 🚀 How to Use Them

### Option 1: Via API

```bash
# Create the workflow
curl -X POST http://localhost:8000/api/workflows \
  -H "Content-Type: application/json" \
  -d @workflows/ppt_analysis_workflow.json

# Run it (replace WORKFLOW_ID)
curl -X POST http://localhost:8000/api/workflows/{WORKFLOW_ID}/run \
  -d '{
    "input_variables": {
      "presentation_path": "/Users/<USER>/Documents/my_presentation.pptx",
      "analysis_focus": "summary"
    }
  }'
```

### Option 2: Via Agentic-Core UI

1. Open your Agentic-Core UI
2. Navigate to Workflows
3. Click "Create Workflow" or "Import"
4. Copy/paste the workflow JSON
5. Save and run with your input values

### Option 3: Via Axon-PFC Dashboard

1. Open http://localhost:8080
2. Go to Workflows section
3. Import the JSON file
4. Configure and execute

## 📝 Example Inputs

### For Analyzer:
```json
{
  "presentation_path": "/Users/<USER>/Documents/Q4_Report.pptx",
  "analysis_focus": "financial"
}
```

### For Comparison:
```json
{
  "presentation_1_path": "/Users/<USER>/Documents/old_version.pptx",
  "presentation_2_path": "/Users/<USER>/Documents/new_version.pptx"
}
```

### For Summarizer:
```json
{
  "file_path": "/Users/<USER>/Documents/strategy_deck.pptx"
}
```

### For Q&A:
```json
{
  "presentation_path": "/Users/<USER>/Documents/roadmap.pptx",
  "question": "What are the Q1 2025 deliverables?"
}
```

## 🎨 What Each Workflow Does

### 1. Analyzer Workflow
- **Agent 1**: Uses `ppt_ingestion` tool to extract content
- **Agent 2**: Analyzes based on focus area (financial, technical, etc.)
- **Output**: Full extraction + tailored analysis

### 2. Comparison Workflow
- **Agent 1**: Processes both presentations sequentially
- **Agent 2**: Compares content and identifies key differences
- **Output**: Detailed comparison report

### 3. Summarizer Workflow
- **Single Agent**: Extracts content and creates concise summary
- **Fast**: Optimized for quick reviews
- **Output**: Executive summary + bullet points

### 4. Q&A Workflow
- **Single Agent**: Extracts content and finds relevant information
- **Focused**: Answers specific questions with evidence
- **Output**: Answer + supporting quotes

## 🔧 Customization

All workflows are fully customizable:

**Change the model:**
```json
"model": "gpt-4o-mini"  // Cheaper/faster
```

**Adjust agent behavior:**
```json
"skills": "Provide analysis in bullet points only..."
```

**Add more agents:**
```json
{
  "name": "fact_checker",
  "skills": "Verify all numerical data..."
}
```

## 📁 File Locations

```
/Users/<USER>/axon-pfc/workflows/
├── README.md                          # Full documentation
├── ppt_analysis_workflow.json         # Analyzer workflow
├── ppt_comparison_workflow.json       # Comparison workflow
├── ppt_summarizer_workflow.json       # Summarizer workflow
└── ppt_qa_workflow.json               # Q&A workflow
```

## ✅ Ready to Use!

All workflows are:
- ✅ Tested and validated JSON format
- ✅ Use the registered `ppt_ingestion` tool
- ✅ Include proper variable definitions
- ✅ Have clear agent instructions
- ✅ Include termination conditions
- ✅ Documented with examples

## 🎯 Quick Test

Try the summarizer workflow:

```bash
# 1. Create it
curl -X POST http://localhost:8000/api/workflows \
  -H "Content-Type: application/json" \
  -d @workflows/ppt_summarizer_workflow.json

# 2. Run it (replace with your file path)
curl -X POST http://localhost:8000/api/workflows/{ID}/run \
  -d '{"input_variables": {"file_path": "/path/to/test.pptx"}}'
```

## 📚 Documentation

- **Workflow Guide**: `workflows/README.md` - Complete guide with examples
- **Tool Documentation**: `docs/ppt-ingestion.md` - How the tool works
- **Tool Registration**: `PPT_TOOL_REGISTERED.md` - Tool setup info

## 🎉 What You Can Do Now

1. ✅ **Import workflows** into Agentic-Core UI
2. ✅ **Run them** on your presentations
3. ✅ **Customize** for your specific needs
4. ✅ **Create new workflows** using these as templates
5. ✅ **Chain workflows** together for complex pipelines

---

**All set! Your PPT ingestion workflows are ready to use! 🚀**

