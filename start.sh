#!/bin/bash
# Robust startup script for Axon-PFC
# Starts agentic-core infrastructure (postgres, rabbitmq) and the frontend/backend

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Axon-PFC Startup Script ===${NC}"

# Configuration
AGENTIC_CORE_PATH="${AGENTIC_CORE_PATH:-../agentic-core}"
COMPOSE_FILE="$AGENTIC_CORE_PATH/docker-compose.yml"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$PROJECT_ROOT/src/backend"
FRONTEND_DIR="$PROJECT_ROOT/src/frontend"

# Check if agentic-core exists
if [ ! -d "$AGENTIC_CORE_PATH" ]; then
    echo -e "${RED}Error: agentic-core not found at $AGENTIC_CORE_PATH${NC}"
    echo "Please set AGENTIC_CORE_PATH environment variable or ensure agentic-core is at ../agentic-core"
    exit 1
fi

if [ ! -f "$COMPOSE_FILE" ]; then
    echo -e "${RED}Error: docker-compose.yml not found at $COMPOSE_FILE${NC}"
    exit 1
fi

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check dependencies
echo -e "${YELLOW}Checking dependencies...${NC}"
if ! command_exists docker; then
    echo -e "${RED}Error: docker is not installed${NC}"
    exit 1
fi

if ! command_exists python3; then
    echo -e "${RED}Error: python3 is not installed${NC}"
    exit 1
fi

# Start docker containers with port overrides
echo -e "${YELLOW}Starting Docker containers (postgres, rabbitmq, graphql, ui, workers) with fixed ports...${NC}"
cd "$AGENTIC_CORE_PATH"

# Use the ports override file from axon-pfc to set static ports
PORTS_OVERRIDE="$PROJECT_ROOT/docker-compose.ports.yml"
if [ -f "$PORTS_OVERRIDE" ]; then
    docker compose -f docker-compose.yml -f "$PORTS_OVERRIDE" up -d postgres rabbitmq graphql ui async-engine-async async-engine-flyweight
else
    echo -e "${YELLOW}Note: $PORTS_OVERRIDE not found, using dynamic ports${NC}"
    docker compose up -d postgres rabbitmq graphql ui async-engine-async async-engine-flyweight
fi

# Wait for containers to be ready
echo -e "${YELLOW}Waiting for containers to be ready...${NC}"
sleep 3

# Discover postgres port
echo -e "${YELLOW}Discovering postgres port...${NC}"
POSTGRES_PORT=$(docker compose port postgres 5432 2>/dev/null | tail -n1 | cut -d: -f2 | tr -d '[:space:]')
CONTAINER_NAME=$(docker compose ps -q postgres 2>/dev/null)

# When both static and dynamically assigned ports are present, prefer the highest host port.
if [ -n "$CONTAINER_NAME" ]; then
    PORT_CANDIDATES=$(docker port "$CONTAINER_NAME" 5432 2>/dev/null | awk -F: '{print $2}')
    if [ -n "$PORT_CANDIDATES" ]; then
        for candidate in $PORT_CANDIDATES; do
            candidate=$(echo "$candidate" | tr -d '[:space:]')
            if [ -z "$candidate" ]; then
                continue
            fi
            if [[ ! "$candidate" =~ ^[0-9]+$ ]]; then
                continue
            fi
            if [ -z "$POSTGRES_PORT" ] || [ "$candidate" -gt "$POSTGRES_PORT" ]; then
                POSTGRES_PORT=$candidate
            fi
        done
    fi
fi

if [ -z "$POSTGRES_PORT" ] && [ -n "$CONTAINER_NAME" ]; then
    # Fallback to docker inspect when docker port fails
    POSTGRES_PORT=$(docker inspect "$CONTAINER_NAME" --format='{{range $i, $p := index .NetworkSettings.Ports "5432/tcp"}}{{$p.HostPort}} {{end}}' 2>/dev/null | awk '{print $NF}' | tr -d '[:space:]')
fi

if [ -z "$POSTGRES_PORT" ]; then
    echo -e "${RED}Error: Could not discover postgres port${NC}"
    echo "Trying to find postgres container manually..."
    docker ps | grep postgres
    exit 1
fi

echo -e "${GREEN}✓ Postgres is running on port: $POSTGRES_PORT${NC}"

# Discover rabbitmq port
echo -e "${YELLOW}Discovering rabbitmq port...${NC}"
RABBITMQ_PORT=$(docker compose port rabbitmq 5672 2>/dev/null | cut -d: -f2)

if [ -z "$RABBITMQ_PORT" ]; then
    # Alternative method using docker inspect
    CONTAINER_NAME=$(docker compose ps -q rabbitmq 2>/dev/null)
    if [ -n "$CONTAINER_NAME" ]; then
        RABBITMQ_PORT=$(docker inspect "$CONTAINER_NAME" --format='{{(index (index .NetworkSettings.Ports "5672/tcp") 0).HostPort}}' 2>/dev/null)
    fi
fi

if [ -z "$RABBITMQ_PORT" ]; then
    echo -e "${YELLOW}Warning: Could not discover rabbitmq port (not critical for basic operation)${NC}"
    RABBITMQ_PORT="5672"
fi

echo -e "${GREEN}✓ RabbitMQ is running on port: $RABBITMQ_PORT${NC}"

# Create/update .env file
cd "$PROJECT_ROOT"
echo -e "${YELLOW}Updating .env file...${NC}"

# Preserve existing .env variables (especially OPENAI_API_KEY)
if [ -f .env ]; then
    # Backup existing .env
    cp .env .env.backup

    # Extract custom variables (non-standard ones)
    OPENAI_API_KEY=$(grep '^OPENAI_API_KEY=' .env 2>/dev/null | cut -d= -f2-)

    echo -e "${GREEN}✓ Preserving existing .env variables${NC}"
fi

# Create/update .env with dynamic ports
cat > .env << EOF
# Generated by start.sh on $(date)
AGENTIC_CORE_PATH=$AGENTIC_CORE_PATH
DATABASE_URL=postgresql://postgres:postgres@localhost:$POSTGRES_PORT/invisible
RABBITMQ_URL=amqp://guest:guest@localhost:$RABBITMQ_PORT/
ENABLE_RELOAD=true
EOF

# Re-add preserved custom variables
if [ -n "$OPENAI_API_KEY" ]; then
    echo "OPENAI_API_KEY=$OPENAI_API_KEY" >> .env
    echo -e "${GREEN}✓ Preserved OPENAI_API_KEY${NC}"
fi

echo -e "${GREEN}✓ .env file updated${NC}"

# Create/update .env.ports for agentic-core migrations
echo -e "${YELLOW}Creating .env.ports for agentic-core migrations...${NC}"
cat > "$AGENTIC_CORE_PATH/.env.ports" << EOF
DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:postgres@localhost:$POSTGRES_PORT/invisible
DATABASE_URL=postgresql://postgres:postgres@localhost:$POSTGRES_PORT/invisible
EOF
echo -e "${GREEN}✓ .env.ports created${NC}"

# Check if virtual environment exists
if [ ! -d "$PROJECT_ROOT/venv" ]; then
    echo -e "${YELLOW}Creating virtual environment...${NC}"
    python3 -m venv "$PROJECT_ROOT/venv"
    echo -e "${GREEN}✓ Virtual environment created${NC}"

    echo -e "${YELLOW}Installing dependencies...${NC}"
    source "$PROJECT_ROOT/venv/bin/activate"
    pip install -q --upgrade pip
    pip install -q -r "$PROJECT_ROOT/requirements.txt"
    echo -e "${GREEN}✓ Dependencies installed${NC}"
else
    source "$PROJECT_ROOT/venv/bin/activate"
fi

# Run database migrations
echo -e "${YELLOW}Running database migrations...${NC}"
cd "$AGENTIC_CORE_PATH/backend/services/graphql"
if [ -f "alembic.ini" ] && [ -d "migrations" ]; then
    # Make sure we have alembic
    pip show alembic > /dev/null 2>&1 || pip install -q alembic

    # Run migrations to latest version
    alembic upgrade head
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Database migrations completed${NC}"
    else
        echo -e "${RED}Warning: Database migration had issues (continuing anyway)${NC}"
    fi
else
    echo -e "${YELLOW}Warning: No migrations found in agentic-core, skipping...${NC}"
fi
cd "$PROJECT_ROOT"

# Kill any existing backend/frontend processes on our ports
echo -e "${YELLOW}Checking for existing processes on ports 8000 and 8080...${NC}"
lsof -ti:8000 | xargs kill -9 2>/dev/null || true
lsof -ti:8080 | xargs kill -9 2>/dev/null || true

# Start backend
echo -e "${YELLOW}Starting backend server on port 8000...${NC}"
cd "$BACKEND_DIR"
PYTHONPATH="$BACKEND_DIR:$PYTHONPATH" python main.py > "$PROJECT_ROOT/backend.log" 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > "$PROJECT_ROOT/.backend.pid"
echo -e "${GREEN}✓ Backend started (PID: $BACKEND_PID)${NC}"

# Start eval monitor
echo -e "${YELLOW}Starting eval monitor daemon...${NC}"
cd "$BACKEND_DIR"
DATABASE_URL="postgresql://postgres:postgres@localhost:$POSTGRES_PORT/invisible" RABBITMQ_URL="amqp://guest:guest@localhost:$RABBITMQ_PORT/" PYTHONPATH="$BACKEND_DIR:$PYTHONPATH" python services/eval_monitor.py > "$PROJECT_ROOT/eval_monitor.log" 2>&1 &
EVAL_MONITOR_PID=$!
echo $EVAL_MONITOR_PID > "$PROJECT_ROOT/.eval_monitor.pid"
echo -e "${GREEN}✓ Eval monitor started (PID: $EVAL_MONITOR_PID)${NC}"

# Start GraphQL log writer
echo -e "${YELLOW}Starting GraphQL log writer daemon...${NC}"
cd "$PROJECT_ROOT"
PYTHONUNBUFFERED=1 PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH" python -u -m src.backend.services.graphql_log_writer > "$PROJECT_ROOT/graphql_log_writer.log" 2>&1 &
LOG_WRITER_PID=$!
echo $LOG_WRITER_PID > "$PROJECT_ROOT/.graphql_log_writer.pid"
echo -e "${GREEN}✓ GraphQL log writer started (PID: $LOG_WRITER_PID)${NC}"



# Start frontend
echo -e "${YELLOW}Starting frontend server on port 8080...${NC}"
cd "$FRONTEND_DIR"
python -m http.server 8080 > "$PROJECT_ROOT/frontend.log" 2>&1 &
FRONTEND_PID=$!
echo $FRONTEND_PID > "$PROJECT_ROOT/.frontend.pid"
echo -e "${GREEN}✓ Frontend started (PID: $FRONTEND_PID)${NC}"

# Wait a moment for servers to start
sleep 2

# Check if servers are running
if ! kill -0 $BACKEND_PID 2>/dev/null; then
    echo -e "${RED}Error: Backend failed to start. Check backend.log${NC}"
    exit 1
fi

if ! kill -0 $EVAL_MONITOR_PID 2>/dev/null; then
    echo -e "${RED}Error: Eval monitor failed to start. Check eval_monitor.log${NC}"
    exit 1
fi

if ! kill -0 $LOG_WRITER_PID 2>/dev/null; then
    echo -e "${RED}Error: GraphQL log writer failed to start. Check graphql_log_writer.log${NC}"
    exit 1
fi



if ! kill -0 $FRONTEND_PID 2>/dev/null; then
    echo -e "${RED}Error: Frontend failed to start. Check frontend.log${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}=== All services started successfully! ===${NC}"
echo ""
echo -e "📊 ${GREEN}Services:${NC}"
echo -e "   • Postgres:      localhost:$POSTGRES_PORT"
echo -e "   • RabbitMQ:      localhost:$RABBITMQ_PORT"
echo -e "   • GraphQL:       http://localhost:5002/graphql/"
echo -e "   • Agentic UI:    http://localhost:3001"
echo -e "   • Async Workers: Running (Docker)"
echo -e "   • Backend:       http://localhost:8000"
echo -e "   • Eval Monitor:  Running (background)"
echo -e "   • Log Writer:    Running (background)"
echo -e "   • Frontend:      http://localhost:8080"
echo ""
echo -e "📝 ${GREEN}Logs:${NC}"
echo -e "   • Backend:       tail -f backend.log"
echo -e "   • Eval Monitor:  tail -f eval_monitor.log"
echo -e "   • Log Writer:    tail -f graphql_log_writer.log"
echo -e "   • Frontend:      tail -f frontend.log"
echo -e "   • Workers:       cd $AGENTIC_CORE_PATH && docker compose logs -f async-engine-async"
echo ""
echo -e "📊 ${GREEN}API Endpoints:${NC}"
echo -e "   • Workflows:    http://localhost:8000/api/workflows"
echo -e "   • Dashboard:    http://localhost:8000/api/dashboard/status"
echo -e "   • API Docs:     http://localhost:8000/docs"
echo ""
echo -e "🛑 ${GREEN}To stop:${NC}"
echo -e "   • Run: ./stop.sh"
echo ""
echo -e "🌐 ${GREEN}Open in browser:${NC}"
echo -e "   • Axon Frontend:     http://localhost:8080"
echo -e "   • Axon Dashboard:    http://localhost:8080 (click Dashboard)"
echo -e "   • Agentic-Core UI:   http://localhost:3001"
echo ""
