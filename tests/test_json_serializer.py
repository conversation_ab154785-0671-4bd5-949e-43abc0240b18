#!/usr/bin/env python3
"""
Test suite for the JSON Workflow Serializer

This demonstrates the functionality of the JSON serializer for both
Team-based and Delegate-based workflows.
"""

import os
import sys
import json
from pathlib import Path
from uuid import uuid4, UUID
from dotenv import load_dotenv

# Add project root and agentic-core to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src" / "backend"))

load_dotenv()
AGENTIC_CORE_PATH = os.getenv("AGENTIC_CORE_PATH", "../agentic-core")
backend_path = Path(AGENTIC_CORE_PATH) / "backend"
backend_lib_path = backend_path / "lib"
if backend_path.exists():
    sys.path.insert(0, str(backend_lib_path))
    sys.path.insert(0, str(backend_path))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

try:
    from database.users.models import User, Tenant
    from database.agentic_objects.models import (
        Namespace, Flow, FlowVersion, Delegate, DelegateVersion,
        Team, TeamVersion, Step, AgenticEntityType
    )
except ImportError:
    # Types will be available at runtime in agentic-core environment
    pass  # type: ignore

# Import the serializer
from workflow_json_serializer import (
    flow_to_json, json_to_flow, compare_workflows,
    update_workflow_from_json, export_workflow_to_json_file,
    import_workflow_from_json_file, flow_to_json_string,
    json_string_to_flow
)

# Database configuration
DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "postgresql://postgres:postgres@localhost:5432/invisible"
)


def get_system_user(db):
    """Get or create system user."""
    system_user = db.query(User).filter(User.email == "system@localhost").first()
    if not system_user:
        # Create system tenant first
        system_tenant = db.query(Tenant).filter(Tenant.name == "system").first()
        if not system_tenant:
            system_tenant = Tenant(id=uuid4(), name="system")
            db.add(system_tenant)
            db.flush()
        
        system_user = User(
            id=uuid4(),
            email="system@localhost",
            display_name="System User",
            system_admin=True
        )
        db.add(system_user)
        db.commit()
    
    return system_user


def get_default_namespace(db, user_id: UUID):
    """Get or create default namespace."""
    namespace = db.query(Namespace).filter(Namespace.name == "default").first()
    if not namespace:
        namespace = Namespace(
            id=uuid4(),
            name="default",
            created_by=user_id
        )
        db.add(namespace)
        db.commit()
    
    return namespace


# ============================================================================
# Test 1: Team Workflow - Database to JSON
# ============================================================================

def test_team_workflow_to_json():
    """Test reading an existing team workflow and converting it to JSON."""
    print("\n" + "="*70)
    print("TEST 1: Team Workflow - Database to JSON")
    print("="*70)
    
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(bind=engine)
    db = SessionLocal()
    
    try:
        # Find an existing team workflow
        flow = db.query(Flow).filter(
            Flow.is_archived.is_(False),
            Flow.name.like('%Team%')
        ).first()
        
        if not flow:
            print("❌ No team workflow found in database")
            return False
        
        # Get latest version
        flow_version = db.query(FlowVersion).filter(
            FlowVersion.flow_id == flow.id
        ).order_by(FlowVersion.modified_date.desc()).first()
        
        if not flow_version:
            print("❌ No flow version found")
            return False
        
        print(f"✓ Found workflow: {flow.name}")
        print(f"  Flow ID: {flow.id}")
        print(f"  Version ID: {flow_version.id}")
        
        # Convert to JSON
        workflow_json = flow_to_json(db, flow, flow_version)
        
        print(f"\n✓ Converted to JSON")
        print(f"  Workflow Type: {workflow_json['workflow_type']}")
        print(f"  Name: {workflow_json['metadata']['name']}")
        
        # Display JSON (pretty-printed)
        print("\n" + "-"*70)
        print("JSON OUTPUT:")
        print("-"*70)
        print(json.dumps(workflow_json, indent=2))
        
        # Save to file
        output_path = "output_team_workflow.json"
        export_workflow_to_json_file(db, flow_version.id, output_path)
        print(f"\n✓ Saved to {output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


# ============================================================================
# Test 2: Delegate Workflow - Database to JSON
# ============================================================================

def test_delegate_workflow_to_json():
    """Test reading a delegate workflow and converting it to JSON."""
    print("\n" + "="*70)
    print("TEST 2: Delegate Workflow - Database to JSON")
    print("="*70)
    
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(bind=engine)
    db = SessionLocal()
    
    try:
        # Find a delegate-based workflow
        # Look for workflows with delegate steps
        flows = db.query(Flow).filter(Flow.is_archived.is_(False)).all()
        
        delegate_flow = None
        delegate_flow_version = None
        
        for flow in flows:
            flow_version = db.query(FlowVersion).filter(
                FlowVersion.flow_id == flow.id
            ).order_by(FlowVersion.modified_date.desc()).first()
            
            if flow_version:
                steps = db.query(Step).filter(
                    Step.flow_version_id == flow_version.id
                ).all()
                
                # Check if it has delegate steps (and no team steps)
                has_delegates = any(s.entity_type == AgenticEntityType.delegate for s in steps)
                has_teams = any(s.entity_type == AgenticEntityType.team for s in steps)
                
                if has_delegates and not has_teams:
                    delegate_flow = flow
                    delegate_flow_version = flow_version
                    break
        
        if not delegate_flow:
            print("❌ No delegate workflow found in database")
            print("   Creating a sample delegate workflow...")
            return test_create_delegate_workflow_from_json()
        
        print(f"✓ Found workflow: {delegate_flow.name}")
        print(f"  Flow ID: {delegate_flow.id}")
        print(f"  Version ID: {delegate_flow_version.id}")
        
        # Convert to JSON
        workflow_json = flow_to_json(db, delegate_flow, delegate_flow_version)
        
        print(f"\n✓ Converted to JSON")
        print(f"  Workflow Type: {workflow_json['workflow_type']}")
        print(f"  Name: {workflow_json['metadata']['name']}")
        print(f"  Steps: {len(workflow_json['steps'])}")
        
        # Display JSON (pretty-printed)
        print("\n" + "-"*70)
        print("JSON OUTPUT:")
        print("-"*70)
        print(json.dumps(workflow_json, indent=2))
        
        # Save to file
        output_path = "output_delegate_workflow.json"
        export_workflow_to_json_file(db, delegate_flow_version.id, output_path)
        print(f"\n✓ Saved to {output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


# ============================================================================
# Test 3: Create Team Workflow from JSON
# ============================================================================

def test_create_team_workflow_from_json():
    """Test creating a new team workflow from JSON."""
    print("\n" + "="*70)
    print("TEST 3: Create Team Workflow from JSON")
    print("="*70)
    
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(bind=engine)
    db = SessionLocal()
    
    try:
        system_user = get_system_user(db)
        
        # Create sample team workflow JSON
        workflow_json = {
            "workflow_type": "team",
            "metadata": {
                "name": "JSON Test Team Workflow",
                "description": "A team workflow created from JSON",
                "namespace": "default",
                "version": "1.0"
            },
            "team": {
                "name": "Test Greeting Team",
                "goal": "Create a simple greeting message",
                "results": "A greeting message",
                "termination": {
                    "regex": ["COMPLETE", "DONE"]
                },
                "agents": [
                    {
                        "name": "greeter",
                        "model": "gpt-4o",
                        "persona": "You are a friendly greeter",
                        "skills": "Create warm greetings"
                    }
                ]
            },
            "variables": {
                "input": {
                    "topic": {
                        "type": "string",
                        "description": "Greeting topic",
                        "default": "hello"
                    }
                },
                "output": {
                    "message": {
                        "type": "string",
                        "description": "The greeting message"
                    }
                },
                "team_variables": {}
            }
        }
        
        print("Creating team workflow from JSON...")
        print(json.dumps(workflow_json, indent=2))
        
        # Create workflow
        flow, flow_version = json_to_flow(db, workflow_json, system_user.id)
        db.commit()
        
        print(f"\n✓ Created workflow successfully")
        print(f"  Flow ID: {flow.id}")
        print(f"  FlowVersion ID: {flow_version.id}")
        print(f"  Name: {flow.name}")
        
        # Read it back and verify
        print("\n✓ Reading back from database...")
        read_back_json = flow_to_json(db, flow, flow_version)
        
        print(f"  Workflow Type: {read_back_json['workflow_type']}")
        print(f"  Team Name: {read_back_json['team']['name']}")
        print(f"  Agent Count: {len(read_back_json['team']['agents'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        db.rollback()
        return False
    finally:
        db.close()


# ============================================================================
# Test 4: Create Delegate Workflow from JSON
# ============================================================================

def test_create_delegate_workflow_from_json():
    """Test creating a delegate workflow from JSON."""
    print("\n" + "="*70)
    print("TEST 4: Create Delegate Workflow from JSON")
    print("="*70)
    
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(bind=engine)
    db = SessionLocal()
    
    try:
        system_user = get_system_user(db)
        namespace = get_default_namespace(db, system_user.id)
        
        # First, create some sample delegates
        print("Creating sample delegates...")
        
        delegates = []
        for i, name in enumerate(["Validator", "Processor", "Formatter"], 1):
            delegate = Delegate(
                id=uuid4(),
                namespace_id=namespace.id,
                name=f"JSON_Test_{name}",
                is_archived=False,
                created_by=system_user.id
            )
            db.add(delegate)
            db.flush()
            
            delegate_version = DelegateVersion(
                id=uuid4(),
                delegate_id=delegate.id,
                type="python",
                code=f"def execute(context):\n    return {{'result': 'processed by {name}'}}",
                description=f"Test delegate {name}",
                variables={},
                is_draft=False,
                modified_by=system_user.id
            )
            db.add(delegate_version)
            db.flush()
            
            delegates.append((delegate, delegate_version))
            print(f"  ✓ Created {name}: {delegate.id}")
        
        db.commit()
        
        # Create workflow JSON
        workflow_json = {
            "workflow_type": "delegate",
            "metadata": {
                "name": "JSON Test Delegate Workflow",
                "description": "A delegate workflow created from JSON",
                "namespace": "default",
                "version": "1.0"
            },
            "variables": {
                "input": {
                    "data": {
                        "type": "string",
                        "description": "Input data"
                    }
                },
                "output": {
                    "result": {
                        "type": "string",
                        "description": "Processed result"
                    }
                }
            },
            "steps": [
                {
                    "order": 1,
                    "type": "delegate",
                    "delegate_name": delegates[0][0].name,
                    "delegate_id": str(delegates[0][0].id),
                    "entity_id": str(delegates[0][1].id),
                    "description": "Validate input"
                },
                {
                    "order": 2,
                    "type": "delegate",
                    "delegate_name": delegates[1][0].name,
                    "delegate_id": str(delegates[1][0].id),
                    "entity_id": str(delegates[1][1].id),
                    "description": "Process data"
                },
                {
                    "order": 3,
                    "type": "delegate",
                    "delegate_name": delegates[2][0].name,
                    "delegate_id": str(delegates[2][0].id),
                    "entity_id": str(delegates[2][1].id),
                    "description": "Format output"
                }
            ]
        }
        
        print("\nCreating delegate workflow from JSON...")
        
        # Create workflow
        flow, flow_version = json_to_flow(db, workflow_json, system_user.id)
        db.commit()
        
        print(f"\n✓ Created workflow successfully")
        print(f"  Flow ID: {flow.id}")
        print(f"  FlowVersion ID: {flow_version.id}")
        print(f"  Name: {flow.name}")
        
        # Read it back and verify
        print("\n✓ Reading back from database...")
        read_back_json = flow_to_json(db, flow, flow_version)
        
        print(f"  Workflow Type: {read_back_json['workflow_type']}")
        print(f"  Step Count: {len(read_back_json['steps'])}")
        
        for i, step in enumerate(read_back_json['steps'], 1):
            print(f"  Step {i}: {step.get('delegate_name', 'Unknown')}")
        
        # Save to file
        output_path = "output_created_delegate_workflow.json"
        with open(output_path, 'w') as f:
            json.dump(read_back_json, f, indent=2)
        print(f"\n✓ Saved to {output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        db.rollback()
        return False
    finally:
        db.close()


# ============================================================================
# Test 5: Compare and Update Workflow
# ============================================================================

def test_compare_and_update():
    """Test comparing workflows and updating with diff detection."""
    print("\n" + "="*70)
    print("TEST 5: Compare and Update Workflow")
    print("="*70)
    
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(bind=engine)
    db = SessionLocal()
    
    try:
        system_user = get_system_user(db)
        
        # Create initial workflow
        print("Creating initial workflow...")
        workflow_json = {
            "workflow_type": "team",
            "metadata": {
                "name": "Test Update Workflow",
                "description": "Original description",
                "namespace": "default"
            },
            "team": {
                "name": "Test Team",
                "goal": "Original goal",
                "results": "Original results",
                "termination": {"regex": ["DONE"]},
                "agents": [
                    {
                        "name": "agent1",
                        "model": "gpt-4o",
                        "persona": "Test agent",
                        "skills": "Testing"
                    }
                ]
            },
            "variables": {
                "input": {},
                "output": {},
                "team_variables": {}
            }
        }
        
        flow, flow_version = json_to_flow(db, workflow_json, system_user.id)
        db.commit()
        print(f"✓ Created: {flow.id}")
        
        # Get current state
        current_json = flow_to_json(db, flow, flow_version)
        
        # Create modified version
        print("\nModifying workflow...")
        modified_json = current_json.copy()
        modified_json["metadata"]["description"] = "Updated description"
        modified_json["team"]["goal"] = "Updated goal"
        modified_json["team"]["agents"].append({
            "name": "agent2",
            "model": "gpt-4o",
            "persona": "Second agent",
            "skills": "More testing"
        })
        
        # Compare
        print("\nComparing workflows...")
        diff = compare_workflows(current_json, modified_json)
        
        print(f"✓ Has changes: {diff['has_changes']}")
        if diff["has_changes"]:
            print("\nDetected changes:")
            if diff["metadata_changes"]:
                print("  Metadata changes:")
                for key, change in diff["metadata_changes"].items():
                    print(f"    - {key}: '{change['old']}' → '{change['new']}'")
            
            if diff["team_changes"]:
                print("  Team changes:")
                for key, change in diff["team_changes"].items():
                    if key == "agents":
                        print(f"    - agents: {len(change['old'])} → {len(change['new'])} agents")
                    else:
                        print(f"    - {key}: changed")
        
        # Update workflow
        print("\nUpdating workflow...")
        updated_flow, updated_version, was_updated = update_workflow_from_json(
            db, flow, flow_version, modified_json, system_user.id
        )
        db.commit()
        
        print(f"✓ Update result:")
        print(f"  Was updated: {was_updated}")
        print(f"  New version ID: {updated_version.id}")
        
        # Verify changes
        final_json = flow_to_json(db, updated_flow, updated_version)
        print(f"\n✓ Verified changes:")
        print(f"  Description: {final_json['metadata']['description']}")
        print(f"  Goal: {final_json['team']['goal']}")
        print(f"  Agent count: {len(final_json['team']['agents'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        db.rollback()
        return False
    finally:
        db.close()


# ============================================================================
# Main Test Runner
# ============================================================================

def run_all_tests():
    """Run all tests."""
    print("\n" + "="*70)
    print("JSON WORKFLOW SERIALIZER TEST SUITE")
    print("="*70)
    
    tests = [
        ("Team Workflow to JSON", test_team_workflow_to_json),
        ("Delegate Workflow to JSON", test_delegate_workflow_to_json),
        ("Create Team from JSON", test_create_team_workflow_from_json),
        ("Create Delegate from JSON", test_create_delegate_workflow_from_json),
        ("Compare and Update", test_compare_and_update),
    ]
    
    results = []
    
    for name, test_func in tests:
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"\n❌ Test '{name}' crashed: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "="*70)
    print("TEST SUMMARY")
    print("="*70)
    
    for name, result in results:
        status = "✓ PASS" if result else "❌ FAIL"
        print(f"{status}: {name}")
    
    passed = sum(1 for _, r in results if r)
    total = len(results)
    print(f"\nTotal: {passed}/{total} tests passed")
    
    return passed == total


if __name__ == "__main__":
    import sys
    
    # Check if specific test requested
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        test_map = {
            "team": test_team_workflow_to_json,
            "delegate": test_delegate_workflow_to_json,
            "create-team": test_create_team_workflow_from_json,
            "create-delegate": test_create_delegate_workflow_from_json,
            "update": test_compare_and_update,
        }
        
        if test_name in test_map:
            success = test_map[test_name]()
            sys.exit(0 if success else 1)
        else:
            print(f"Unknown test: {test_name}")
            print(f"Available tests: {', '.join(test_map.keys())}")
            sys.exit(1)
    else:
        # Run all tests
        success = run_all_tests()
        sys.exit(0 if success else 1)

