"""
Unit tests for PPT Ingestion Service
"""

import pytest
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.backend.services.ppt_ingestion import (
    PPTIngestionService,
    SlideData,
    get_ppt_ingestion_service
)


class TestSlideData:
    """Tests for SlideData class."""
    
    def test_slide_data_creation(self):
        """Test creating a SlideData object."""
        slide = SlideData(
            page_number=1,
            ocr_text="Sample text",
            image_data=b"fake_image_data"
        )
        
        assert slide.page_number == 1
        assert slide.ocr_text == "Sample text"
        assert slide.image_data == b"fake_image_data"
        assert slide.image_format == "png"
        assert slide.enhanced_text is None
    
    def test_slide_data_to_dict(self):
        """Test converting SlideData to dictionary."""
        slide = SlideData(
            page_number=2,
            ocr_text="Test",
            image_data=b"data",
            image_format="png"
        )
        slide.enhanced_text = "Enhanced test"
        
        result = slide.to_dict()
        
        assert result["page_number"] == 2
        assert result["ocr_text"] == "Test"
        assert result["enhanced_text"] == "Enhanced test"
        assert result["has_image"] is True
        assert result["image_format"] == "png"


class TestPPTIngestionService:
    """Tests for PPTIngestionService class."""
    
    def test_service_initialization_with_api_key(self):
        """Test service initialization with API key."""
        service = PPTIngestionService(openai_api_key="test-key")
        assert service.client is not None
        assert service.vision_model in ["gpt-4o", "gpt-4-vision-preview", "gpt-4-turbo"]
    
    def test_service_initialization_without_api_key(self):
        """Test service initialization fails without API key."""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="OpenAI API key is required"):
                PPTIngestionService()
    
    def test_service_initialization_with_env_var(self):
        """Test service initialization with environment variable."""
        with patch.dict(os.environ, {"OPENAI_API_KEY": "env-key"}):
            service = PPTIngestionService()
            assert service.client is not None
    
    def test_unsupported_file_format(self):
        """Test that unsupported file formats raise an error."""
        service = PPTIngestionService(openai_api_key="test-key")
        
        with pytest.raises(ValueError, match="Unsupported file format"):
            service.process_file("test.doc")
    
    def test_file_not_found(self):
        """Test that non-existent files raise an error."""
        service = PPTIngestionService(openai_api_key="test-key")
        
        with pytest.raises(FileNotFoundError):
            service.process_file("nonexistent_file.pdf")
    
    @patch('src.backend.services.ppt_ingestion.fitz')
    def test_process_pdf_basic(self, mock_fitz):
        """Test basic PDF processing."""
        # Mock PyMuPDF document
        mock_doc = MagicMock()
        mock_page = MagicMock()
        mock_page.get_text.return_value = "Sample PDF text"
        
        mock_pix = MagicMock()
        mock_pix.tobytes.return_value = b"fake_png_data"
        mock_page.get_pixmap.return_value = mock_pix
        
        mock_doc.__len__.return_value = 1
        mock_doc.__getitem__.return_value = mock_page
        mock_fitz.open.return_value = mock_doc
        
        service = PPTIngestionService(openai_api_key="test-key")
        
        # Create a temporary PDF file for testing
        import tempfile
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
            tmp_path = tmp_file.name
            tmp_file.write(b"fake pdf content")
        
        try:
            slides = service._process_pdf(Path(tmp_path))
            
            assert len(slides) == 1
            assert slides[0].page_number == 1
            assert slides[0].ocr_text == "Sample PDF text"
            assert slides[0].image_data == b"fake_png_data"
        finally:
            os.unlink(tmp_path)
    
    def test_export_to_text(self):
        """Test exporting slides to text file."""
        service = PPTIngestionService(openai_api_key="test-key")
        
        slides = [
            SlideData(1, "First slide", b"data1"),
            SlideData(2, "Second slide", b"data2")
        ]
        slides[0].enhanced_text = "Enhanced first slide"
        
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix=".txt", delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        try:
            service.export_to_text(slides, tmp_path, use_enhanced=True)
            
            with open(tmp_path, 'r') as f:
                content = f.read()
            
            assert "Slide/Page 1" in content
            assert "Enhanced first slide" in content
            assert "Second slide" in content
        finally:
            os.unlink(tmp_path)
    
    def test_export_to_json(self):
        """Test exporting slides to JSON file."""
        service = PPTIngestionService(openai_api_key="test-key")
        
        slides = [
            SlideData(1, "First slide", b"data1"),
            SlideData(2, "Second slide", b"data2")
        ]
        
        import tempfile
        import json
        with tempfile.NamedTemporaryFile(mode='w', suffix=".json", delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        try:
            service.export_to_json(slides, tmp_path)
            
            with open(tmp_path, 'r') as f:
                data = json.load(f)
            
            assert data["total_slides"] == 2
            assert len(data["slides"]) == 2
            assert data["slides"][0]["page_number"] == 1
            assert data["slides"][0]["ocr_text"] == "First slide"
        finally:
            os.unlink(tmp_path)


class TestHelperFunctions:
    """Tests for helper functions."""
    
    def test_get_ppt_ingestion_service(self):
        """Test getting service instance via helper function."""
        with patch.dict(os.environ, {"OPENAI_API_KEY": "test-key"}):
            service = get_ppt_ingestion_service()
            assert isinstance(service, PPTIngestionService)
    
    def test_get_ppt_ingestion_service_with_key(self):
        """Test getting service instance with explicit API key."""
        service = get_ppt_ingestion_service(openai_api_key="explicit-key")
        assert isinstance(service, PPTIngestionService)


# Integration tests (require actual API key and files)
@pytest.mark.integration
class TestPPTIngestionIntegration:
    """Integration tests that require actual files and API access."""
    
    @pytest.mark.skipif(
        not os.getenv("OPENAI_API_KEY"),
        reason="Requires OPENAI_API_KEY environment variable"
    )
    def test_real_pdf_processing(self):
        """Test processing a real PDF file."""
        # This test would require a sample PDF file
        # Skipped by default
        pass
    
    @pytest.mark.skipif(
        not os.getenv("OPENAI_API_KEY"),
        reason="Requires OPENAI_API_KEY environment variable"
    )
    def test_real_pptx_processing(self):
        """Test processing a real PPTX file."""
        # This test would require a sample PPTX file
        # Skipped by default
        pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

