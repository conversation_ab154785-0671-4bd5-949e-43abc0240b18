# Tests

Test suite for Axon-PFC backend functionality.

## Test Files

### test_json_serializer.py

Tests for workflow JSON serialization:
- Converting database models to JSON
- Creating database models from JSON
- Team and delegate handling
- Variable mapping

### test_api.py

Tests for REST API endpoints:
- Workflow CRUD operations
- Request validation
- Error handling

## Running Tests

```bash
source venv/bin/activate
pytest tests/
```

Or run individual test files:
```bash
pytest tests/test_json_serializer.py
pytest tests/test_api.py
```

## Requirements

Tests require:
- pytest
- pytest-asyncio
- Running database (start services with `./start.sh`)
