#!/usr/bin/env python3
"""
Basic API endpoint tests for Axon-PFC backend

These tests verify that the API endpoints are working correctly
without requiring a full database setup.
"""

import requests
import json
from typing import Optional

# Configuration
API_BASE_URL = "http://localhost:8000"


def check_health() -> bool:
    """Check if the API is running and healthy."""
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Health check passed: {data}")
            return data.get("status") == "healthy"
        else:
            print(f"✗ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Could not connect to API: {e}")
        return False


def list_workflows() -> Optional[list]:
    """List all workflows."""
    try:
        response = requests.get(f"{API_BASE_URL}/api/workflows")
        if response.status_code == 200:
            workflows = response.json()
            print(f"✓ Listed {len(workflows)} workflows")
            return workflows
        else:
            print(f"✗ Failed to list workflows: {response.status_code}")
            return None
    except Exception as e:
        print(f"✗ Error listing workflows: {e}")
        return None


def create_test_workflow() -> Optional[str]:
    """Create a test workflow."""
    workflow_data = {
        "workflow": {
            "metadata": {
                "name": "Test Workflow API",
                "description": "Test workflow created via API test",
                "namespace": "default",
                "version": "1.0"
            },
            "variables": {
                "input": {
                    "test_input": {
                        "type": "string",
                        "description": "Test input"
                    }
                },
                "output": {
                    "test_output": {
                        "type": "string",
                        "description": "Test output"
                    }
                }
            },
            "steps": []
        }
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/api/workflows",
            json=workflow_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 201:
            data = response.json()
            workflow_id = data.get("id")
            print(f"✓ Created workflow with ID: {workflow_id}")
            return workflow_id
        else:
            print(f"✗ Failed to create workflow: {response.status_code}")
            print(f"  Response: {response.text}")
            return None
    except Exception as e:
        print(f"✗ Error creating workflow: {e}")
        return None


def get_workflow(workflow_id: str) -> Optional[dict]:
    """Get a specific workflow."""
    try:
        response = requests.get(f"{API_BASE_URL}/api/workflows/{workflow_id}")
        if response.status_code == 200:
            workflow = response.json()
            print(f"✓ Retrieved workflow: {workflow.get('name')}")
            return workflow
        else:
            print(f"✗ Failed to get workflow: {response.status_code}")
            return None
    except Exception as e:
        print(f"✗ Error getting workflow: {e}")
        return None


def update_workflow(workflow_id: str) -> bool:
    """Update a workflow."""
    workflow_data = {
        "workflow": {
            "metadata": {
                "name": "Test Workflow API Updated",
                "description": "Updated test workflow",
                "namespace": "default",
                "version": "1.0"
            },
            "variables": {
                "input": {},
                "output": {}
            },
            "steps": []
        }
    }
    
    try:
        response = requests.put(
            f"{API_BASE_URL}/api/workflows/{workflow_id}",
            json=workflow_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Updated workflow: {data.get('name')}")
            return True
        else:
            print(f"✗ Failed to update workflow: {response.status_code}")
            print(f"  Response: {response.text}")
            return False
    except Exception as e:
        print(f"✗ Error updating workflow: {e}")
        return False


def delete_workflow(workflow_id: str) -> bool:
    """Delete a workflow."""
    try:
        response = requests.delete(f"{API_BASE_URL}/api/workflows/{workflow_id}")
        if response.status_code == 204:
            print(f"✓ Deleted workflow: {workflow_id}")
            return True
        else:
            print(f"✗ Failed to delete workflow: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Error deleting workflow: {e}")
        return False


def run_api_tests():
    """Run all API tests."""
    print("=" * 60)
    print("Axon-PFC API Tests")
    print("=" * 60)
    print()
    
    # Check health
    print("1. Health Check")
    if not check_health():
        print("\n✗ API is not healthy. Please start the server with ./start.sh")
        return False
    print()
    
    # List workflows
    print("2. List Workflows")
    initial_workflows = list_workflows()
    if initial_workflows is None:
        return False
    print()
    
    # Create workflow
    print("3. Create Workflow")
    workflow_id = create_test_workflow()
    if not workflow_id:
        return False
    print()
    
    # Get workflow
    print("4. Get Workflow")
    workflow = get_workflow(workflow_id)
    if not workflow:
        return False
    print()
    
    # Update workflow
    print("5. Update Workflow")
    if not update_workflow(workflow_id):
        return False
    print()
    
    # Verify update
    print("6. Verify Update")
    updated_workflow = get_workflow(workflow_id)
    if not updated_workflow:
        return False
    if updated_workflow.get("name") != "Test Workflow API Updated":
        print(f"✗ Update verification failed")
        return False
    print(f"✓ Update verified: {updated_workflow.get('name')}")
    print()
    
    # Delete workflow
    print("7. Delete Workflow")
    if not delete_workflow(workflow_id):
        return False
    print()
    
    # Verify deletion
    print("8. Verify Deletion")
    final_workflows = list_workflows()
    if final_workflows is None:
        return False
    if len(final_workflows) != len(initial_workflows):
        print(f"✗ Deletion verification failed")
        return False
    print(f"✓ Deletion verified")
    print()
    
    print("=" * 60)
    print("✓ All API tests passed!")
    print("=" * 60)
    return True


if __name__ == "__main__":
    import sys
    
    success = run_api_tests()
    sys.exit(0 if success else 1)


