# OpenAI Agents SDK Implementation - Workflow Specification Generator

## Summary

The `workflow_spec_generator.py` service has been successfully reimplemented using the **OpenAI Agents SDK**, creating a **multi-agent specification team** that takes brief requirements and generates comprehensive workflow specifications for asset management report generation.

## Purpose

This agent team **does NOT generate the actual reports**. Instead, it:

1. Takes a brief requirement (e.g., "Generate quarterly investor performance report")
2. Generates a detailed **specification document** that describes:
   - What the output report should contain
   - How to structure the report  
   - What analyses to perform
   - What data processing is needed
   - How to handle input files

3. A **future execution team** will use these specifications to actually generate the reports

Think of this as the "planning team" that creates the blueprint, while a future "execution team" will do the actual work.

## Multi-Agent Architecture

The system uses 6 specialized planning agents:

| Agent | Role | Progress |
|-------|------|----------|
| **Requirements Analysis Agent** | Understands what's being requested | 15% |
| **Report Structure Designer Agent** | Designs the output report structure | 30% |
| **Data Processing Specification Agent** | Defines data requirements & processing | 45% |
| **Analysis Methodology Agent** | Defines analytical approaches & metrics | 60% |
| **Risk Assessment Framework Agent** | Defines risk assessment framework | 75% |
| **Workflow Specification Compiler Agent** | Compiles everything into executable spec | 90% |

### Example Workflow

```
User: "Create a quarterly performance report for hedge fund investors"
    ↓
[Requirements Analysis] → Purpose: investor communication
                        → Audience: qualified investors, regulators
                        → Objectives: performance transparency, risk disclosure
    ↓
[Report Structure] → Executive Summary
                   → Performance Analysis section
                   → Risk Assessment section
                   → Holdings Detail appendix
    ↓
[Data Processing] → Input: portfolio_holdings.csv (required fields: ISIN, quantity, price)
                  → Input: transaction_history.xlsx
                  → Calculate: position values, period returns
                  → Validate: no missing prices, valid ISINs
    ↓
[Analysis Methodology] → Calculate annualized returns (formula: ...)
                       → Calculate Sharpe ratio (formula: ...)
                       → Benchmark against S&P 500
                       → Period-over-period comparison
    ↓
[Risk Framework] → Assess market risk (VaR calculation)
                → Assess concentration risk (top 10 holdings)
                → Calculate volatility metrics
                → Note regulatory requirements (13F, etc.)
    ↓
[Workflow Compiler] → Step 1: Load and validate input files
                    → Step 2: Calculate position valuations
                    → Step 3: Perform return analysis
                    → Step 4: Calculate risk metrics
                    → Step 5: Generate report sections
                    → Step 6: Format and output document
    ↓
Specification Document Generated (Word doc)
```

## Output: Comprehensive Specification Document

The final Word document contains:

### 1. Specification Overview
Clear description of what workflow this spec defines

### 2. Requirements
- **Purpose**: Why this report is needed
- **Target Audience**: Who will read it
- **Key Objectives**: What it must accomplish
- **Scope**: What's included and excluded

### 3. Workflow Steps
Step-by-step instructions an execution team can follow:
1. Load input files and validate
2. Transform data as needed
3. Perform calculations
4. Run analyses
5. Generate output sections
6. Format and deliver

### 4. Data Requirements
- **Input Files**: Expected files with formats and required fields
- **Validation Rules**: How to check data quality
- **Transformations**: How to process the data
- **Calculations**: Specific formulas to apply

### 5. Analysis Requirements
- **Financial Analyses**: What analyses to perform with methodologies
- **Performance Metrics**: What metrics to calculate (with formulas)
- **Benchmarking**: What to compare against and how
- **Market Context**: What external data to gather

### 6. Risk Assessment Framework
- **Risk Categories**: Types of risks to assess
- **Methodology**: How to identify and evaluate risks
- **Risk Metrics**: What to calculate (VaR, concentration, etc.)
- **Regulatory Considerations**: Compliance requirements

### 7. Output Report Structure
- **Report Sections**: All sections with purpose and content description
- **Executive Summary**: What to include
- **Formatting Guidelines**: How to present the information

### 8. Quality Criteria
How to know if the workflow executed successfully

### 9. Recommendations & Best Practices
Additional guidance for the execution team

## Key Features

### 1. Planning, Not Execution
The agents create specifications, not reports. They think through:
- "What should the report contain?"
- "How should data be processed?"
- "What analyses are needed?"

NOT:
- "Here is the calculated return" (that's execution)
- "This portfolio has X risk" (that's execution)

### 2. Comprehensive Specifications
Every aspect is specified:
- Data formats and validation
- Calculation formulas
- Analysis methodologies
- Report structure
- Quality criteria

### 3. Actionable Instructions
The specification is detailed enough that an execution team can follow it step-by-step.

### 4. Industry-Standard Approaches
Agents recommend established methodologies and best practices from asset management.

### 5. Real-Time Progress Messages
Frontend sees each agent handoff:
```json
{"type": "thinking", "content": "Handoff to Requirements Analysis Agent..."}
{"type": "success", "content": "Requirements Analysis Agent completed analysis"}
{"type": "thinking", "content": "Handoff to Report Structure Designer Agent..."}
```

## Example Use Cases

### Use Case 1: Quarterly Performance Report
**Brief Requirement**: "Generate quarterly performance report for hedge fund investors"

**Generated Specification Includes**:
- Input files needed: holdings, transactions, market data
- Calculations: returns, Sharpe ratio, max drawdown
- Analyses: attribution analysis, sector allocation
- Risk assessment: VaR, concentration, leverage
- Report structure: executive summary, performance tables, risk disclosure

### Use Case 2: ESG Assessment Report
**Brief Requirement**: "Create ESG sustainability report for portfolio holdings"

**Generated Specification Includes**:
- Input files: holdings data, ESG ratings database
- Data processing: map holdings to ESG scores
- Analyses: carbon footprint calculation, ESG score distribution
- Report structure: ESG summary, detailed holdings analysis, improvement recommendations

### Use Case 3: Risk Analysis Report
**Brief Requirement**: "Monthly portfolio risk report for risk committee"

**Generated Specification Includes**:
- Input files: positions, market data, correlations
- Risk metrics: VaR, CVaR, stress test scenarios
- Analyses: concentration analysis, factor exposures
- Report structure: risk dashboard, detailed risk breakdown, scenario analysis

## Benefits

### For Users
- **Consistency**: Every specification follows the same comprehensive template
- **Completeness**: Nothing is overlooked - all aspects are planned
- **Clarity**: Specifications are clear and actionable
- **Speed**: Generate detailed specs in minutes instead of hours

### For Future Execution Teams
- **Clear Instructions**: Know exactly what to do
- **All Requirements Up Front**: No ambiguity about what's needed
- **Quality Criteria**: Know when the job is done correctly
- **Best Practices**: Benefit from industry-standard approaches

### For Organizations
- **Reusability**: Specifications can be reused for similar reports
- **Documentation**: Clear record of what each workflow does
- **Quality Control**: Specifications can be reviewed before execution
- **Efficiency**: Separate planning from execution

## Technical Implementation

### Agent Specialization
Each agent has a specific domain:
- Requirements analyst understands stakeholder needs
- Structure designer knows report formats
- Data specialist understands data engineering
- Analysis expert knows financial methodologies
- Risk specialist knows risk frameworks
- Compiler knows how to write clear specifications

### Structured Outputs
Each agent returns strongly-typed Pydantic models:

```python
class AnalysisMethodologyOutput(BaseModel):
    financial_analyses: List[Dict[str, str]]
    performance_metrics: List[str]
    benchmarking_approach: str
    comparison_requirements: List[str]
    market_context_needed: List[str]
```

### Context Passing
Each agent receives context from previous agents:

```python
analysis_context = f"""
Report Requirement: {workflow_request}
Objectives: {requirements_output.key_objectives}
Report Sections: {structure_output.report_sections}

Define all analyses needed...
"""
```

## API Usage

The API remains unchanged:

```bash
# Start specification generation
curl -X POST http://localhost:8080/api/workflow-spec/generate \
  -H "Content-Type: application/json" \
  -d '{"workflow_request": "Generate quarterly investor performance report"}'

# Returns: {"task_id": "abc-123", "status": "processing"}

# Poll for status (every second)
curl http://localhost:8080/api/workflow-spec/status/abc-123

# Download specification document
curl http://localhost:8080/api/workflow-spec/download/abc-123 -o spec.docx
```

## Future: Execution Team

In a future phase, a separate execution team will:
1. Read the specification document
2. Load the actual input files
3. Perform the specified calculations and analyses
4. Generate the actual report

This separation allows:
- **Planning once, executing many times** (reuse specs)
- **Review before execution** (validate specs)
- **Different models** (fast models for planning, specialized models for execution)
- **Clear separation of concerns**

## Installation

```bash
# Already installed:
pip install openai-agents>=0.1.0
```

## Files Modified

1. **`src/backend/services/workflow_spec_generator.py`** - Complete rewrite using Agents SDK
2. **`requirements.txt`** - Added `openai-agents>=0.1.0`

## References

- [OpenAI Agents SDK Documentation](https://openai.github.io/openai-agents-python/)
- [OpenAI Agents Quickstart](https://openai.github.io/openai-agents-python/quickstart/)

## Conclusion

This multi-agent specification system transforms brief requirements into comprehensive, actionable workflow specifications for asset management report generation. The agents work together to ensure every aspect is planned thoroughly, creating a complete blueprint that a future execution team can follow.

**Key Distinction**: This team plans the work; a future team will do the work.
