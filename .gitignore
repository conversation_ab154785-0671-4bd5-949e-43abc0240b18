# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
ENV/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environment
.env
.env.local

# Runtime files
*.log
*.pid
.backend.pid
.frontend.pid
.eval_monitor.pid

# IDE
.vscode/
.idea/
.cursor/
.specstory/
*.swp
*.swo
*~
.DS_Store

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/

# Database
*.db
*.sqlite3
database_dump*

# Temporary files
tmp/
temp/
*.tmp

# Node modules (if any)
node_modules/

# Documentation build
docs/_build/
temp/
