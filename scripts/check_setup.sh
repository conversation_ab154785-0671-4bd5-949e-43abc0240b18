#!/bin/bash
echo "🔍 Checking Workflow JSON Generator Setup..."
echo ""

cd /Users/<USER>/projects/axon-pfc
source venv/bin/activate

# 1. Python version
echo "1. Python version:"
python --version
echo ""

# 2. Dependencies
echo "2. Checking dependencies:"
python -c "from docx import Document; print('  ✅ python-docx')" 2>&1 || echo "  ❌ python-docx missing"
python -c "from agents import Agent; print('  ✅ openai-agents')" 2>&1 || echo "  ❌ openai-agents missing"
python -c "import openai; print('  ✅ openai')" 2>&1 || echo "  ❌ openai missing"
echo ""

# 3. Service imports
echo "3. Service imports:"
python -c "import sys; sys.path.insert(0, 'src/backend'); from services.workflow_json_generator import get_workflow_json_generator; print('  ✅ Service OK')" 2>&1 || echo "  ❌ Service import failed"
echo ""

# 4. Route imports
echo "4. Route imports:"
python -c "import sys; sys.path.insert(0, 'src/backend'); from routes.workflow_json_generation import router; print('  ✅ Routes OK')" 2>&1 || echo "  ❌ Route import failed"
echo ""

# 5. OpenAI API key
echo "5. OpenAI API key:"
if grep -q "OPENAI_API_KEY=" .env 2>/dev/null; then
    echo "  ✅ API key configured in .env"
else
    echo "  ❌ API key NOT found in .env"
    echo "     Add with: echo 'OPENAI_API_KEY=your-key' >> .env"
fi
echo ""

# 6. Backend status
echo "6. Backend server:"
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "  ✅ Backend is running"
    echo ""
    echo "  Main health:"
    curl -s http://localhost:8000/health | python -m json.tool
    echo ""
    echo "  Workflow generation health:"
    curl -s http://localhost:8000/workflow-generation/health | python -m json.tool
else
    echo "  ❌ Backend is NOT running"
    echo "     Start with: cd src/backend && python main.py"
fi
echo ""

echo "✅ Setup check complete!"
echo ""
echo "To test the workflow generator, run:"
echo "  python scripts/test_workflow_generator.py"
echo ""
echo "To start backend if not running:"
echo "  cd src/backend && python main.py"

