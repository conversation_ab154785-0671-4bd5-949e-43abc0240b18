#!/usr/bin/env python3
"""
Process workflow runs in parallel using agentic-core messaging.

This script demonstrates how to:
1. Create workflow runs (flow executions) from a workflow in the database
2. Publish run messages to RabbitMQ
3. Process runs in parallel with semaphore-controlled concurrency
4. For simple team workflows (no tool use or delegates)

Usage:
    python process_runs.py
"""

import os
import sys
import asyncio
import logging
import subprocess
from pathlib import Path
from typing import List, Dict, Any, Optional
from uuid import UUID, uuid4
from datetime import datetime
from dotenv import load_dotenv

# Add agentic-core to Python path
project_root = Path(__file__).parent.resolve()
load_dotenv(project_root / ".env")

agentic_core_path_str = os.getenv("AGENTIC_CORE_PATH", "../agentic-core")
agentic_core_path = Path(agentic_core_path_str)
if not agentic_core_path.is_absolute():
    agentic_core_path = (project_root / agentic_core_path).resolve()

backend_path = agentic_core_path / "backend"
backend_lib_path = backend_path / "lib"

if backend_path.exists():
    sys.path.insert(0, str(backend_lib_path))
    sys.path.insert(0, str(backend_path))
else:
    print(f"Error: agentic-core backend not found at {backend_path}")
    sys.exit(1)

# Import agentic-core modules
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from database.users.models import User, Tenant
from database.agentic_objects.models import Flow, FlowVersion
from database.flow_execution.models import FlowExecution
from data_types.database.flow_executions import FlowResult

# Import messaging modules
import aio_pika
from async_engine.core.models.messages import MessageHeader, StartFlowExecutionMessage
from async_engine.messaging.factory import MessagingBackendFactory
from async_engine.messaging.message_factory import MessageFactory
from async_engine.core.models.messaging_config import MessagingConfig
from async_engine.pools.pool_registry import PoolRegistry

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def discover_docker_port(service: str, internal_port: int, agentic_core_path: Path) -> int:
    """
    Discover the host port for a Docker service, similar to start.sh logic.
    
    Args:
        service: Docker service name (e.g., 'postgres', 'rabbitmq')
        internal_port: Internal container port (e.g., 5432, 5672)
        agentic_core_path: Path to agentic-core directory
        
    Returns:
        Host port number
    """
    try:
        # Method 1: Use docker compose port command
        result = subprocess.run(
            ['docker', 'compose', 'port', service, str(internal_port)],
            cwd=str(agentic_core_path),
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0 and result.stdout.strip():
            # Output format: "0.0.0.0:PORT" or ":::PORT"
            port_str = result.stdout.strip().split(':')[-1]
            return int(port_str)
        
        # Method 2: Fallback to docker inspect
        result = subprocess.run(
            ['docker', 'compose', 'ps', '-q', service],
            cwd=str(agentic_core_path),
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0 and result.stdout.strip():
            container_id = result.stdout.strip()
            result = subprocess.run(
                ['docker', 'inspect', container_id,
                 '--format', f'{{{{(index (index .NetworkSettings.Ports "{internal_port}/tcp") 0).HostPort}}}}'],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0 and result.stdout.strip():
                return int(result.stdout.strip())
        
        # Fallback to default port
        logger.warning(f"Could not discover port for {service}, using default {internal_port}")
        return internal_port
        
    except Exception as e:
        logger.warning(f"Error discovering port for {service}: {e}, using default {internal_port}")
        return internal_port


class WorkflowRunProcessor:
    """Process workflow runs in parallel with controlled concurrency."""
    
    def __init__(
        self,
        workflow_name: str = "test",
        max_concurrent_runs: int = 3,
        database_url: Optional[str] = None,
        rabbitmq_url: Optional[str] = None
    ):
        self.workflow_name = workflow_name
        self.max_concurrent_runs = max_concurrent_runs
        
        # Discover ports from Docker if not provided
        if database_url is None and rabbitmq_url is None:
            database_url, rabbitmq_url = self._discover_service_urls()
        
        self.database_url = database_url or os.getenv(
            "DATABASE_URL",
            "postgresql://postgres:postgres@localhost:5432/invisible"
        )
        self.rabbitmq_url = rabbitmq_url or os.getenv(
            "RABBITMQ_URL",
            "amqp://guest:guest@localhost:5672/"
        )
        
        # Database setup
        assert self.database_url, "DATABASE_URL must be set"
        self.engine = create_engine(self.database_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        
        # State
        self.flow_version_id: Optional[UUID] = None
        self.system_user_id: Optional[UUID] = None
        self.system_tenant_id: Optional[UUID] = None
        
        # RabbitMQ connection (for compatibility, but we'll use messaging backend)
        self.rabbitmq_connection: Optional[aio_pika.Connection] = None
        self.rabbitmq_channel: Optional[aio_pika.Channel] = None
        self.rabbitmq_exchange: Optional[aio_pika.Exchange] = None
        
        # Messaging backend (proper Celery integration)
        self.messaging_backend = None
    
    def _discover_service_urls(self) -> tuple[str, str]:
        """Discover database and RabbitMQ URLs from running Docker containers."""
        # Get agentic-core path
        agentic_core_path_str = os.getenv("AGENTIC_CORE_PATH", "../agentic-core")
        agentic_core_path = Path(agentic_core_path_str)
        if not agentic_core_path.is_absolute():
            agentic_core_path = (project_root / agentic_core_path).resolve()
        
        # Discover ports
        postgres_port = discover_docker_port('postgres', 5432, agentic_core_path)
        rabbitmq_port = discover_docker_port('rabbitmq', 5672, agentic_core_path)
        
        database_url = f"postgresql://postgres:postgres@localhost:{postgres_port}/invisible"
        rabbitmq_url = f"amqp://guest:guest@localhost:{rabbitmq_port}/"
        
        logger.info(f"Discovered PostgreSQL port: {postgres_port}")
        logger.info(f"Discovered RabbitMQ port: {rabbitmq_port}")
        
        return database_url, rabbitmq_url
        
    async def setup(self):
        """Initialize database and RabbitMQ connections."""
        logger.info("Setting up WorkflowRunProcessor...")
        
        # Get workflow from database
        with self.SessionLocal() as db:
            flow_version = self._get_workflow(db)
            self.flow_version_id = flow_version.id
            
            # Get system user and tenant
            system_user = self._get_or_create_system_user(db)
            self.system_user_id = system_user.id
            
            tenant = db.query(Tenant).first()
            if not tenant:
                raise Exception("No tenant found in database")
            self.system_tenant_id = tenant.id
        
        logger.info(f"Found workflow '{self.workflow_name}' with version ID: {self.flow_version_id}")
        
        # Setup RabbitMQ connection
        await self._setup_rabbitmq()
        
    async def _setup_rabbitmq(self):
        """Setup messaging backend (uses aio_pika for lightweight publishing)."""
        logger.info(f"Setting up messaging backend to {self.rabbitmq_url}...")
        
        # Create messaging config
        config = MessagingConfig(
            backend_type="aio_pika",
            broker_url=self.rabbitmq_url,
            result_backend=None
        )
        
        # Create aio_pika backend (lighter weight, still Celery-compatible)
        self.messaging_backend = MessagingBackendFactory.create_backend("aio_pika", config)
        await self.messaging_backend.connect()
        
        logger.info("Messaging backend initialized (aio_pika - Celery compatible)")
        
    def _get_workflow(self, db: Session) -> FlowVersion:
        """Get the workflow by name from database."""
        flow = db.query(Flow).filter(
            Flow.name == self.workflow_name,
            Flow.is_archived.is_(False)
        ).first()
        
        if not flow:
            raise Exception(f"Workflow '{self.workflow_name}' not found")
        
        # Get latest flow version
        flow_version = db.query(FlowVersion).filter(
            FlowVersion.flow_id == flow.id
        ).order_by(FlowVersion.modified_date.desc()).first()
        
        if not flow_version:
            raise Exception(f"No version found for workflow '{self.workflow_name}'")
        
        return flow_version
    
    def _get_or_create_system_user(self, db: Session) -> User:
        """Get or create system user."""
        system_user = db.query(User).filter(User.email == "system@localhost").first()
        if not system_user:
            # Create system tenant first
            system_tenant = db.query(Tenant).filter(Tenant.name == "system").first()
            if not system_tenant:
                system_tenant = Tenant(id=uuid4(), name="system")
                db.add(system_tenant)
                db.flush()
            
            system_user = User(
                id=uuid4(),
                email="system@localhost",
                display_name="System User",
                system_admin=True
            )
            db.add(system_user)
            db.commit()
        
        return system_user
    
    def create_flow_execution(self, variables: Dict[str, Any], debugging_enabled: bool = True) -> UUID:
        """
        Create a FlowExecution record in the database.
        
        Args:
            variables: Dictionary of input variables
            debugging_enabled: Whether to enable debugging (conversation logs). Default True.
            
        Returns:
            UUID of the created flow execution
        """
        # Format variables for agentic-core (array of objects with variable_name, variable_value, variable_type)
        formatted_variables = [
            {
                'variable_name': name,
                'variable_value': value,
                'variable_type': 'String',  # Must match AnyPrimitiveType enum (capitalize first letter)
                'step_id': None
            }
            for name, value in variables.items()
        ]
        
        with self.SessionLocal() as db:
            flow_execution = FlowExecution(
                id=uuid4(),
                correlation_id=uuid4(),
                flow_version_id=self.flow_version_id,
                tenant_id=self.system_tenant_id,
                user_id=self.system_user_id,
                result=FlowResult.PENDING,
                variables=formatted_variables,
                debugging_enabled=debugging_enabled  # Enable debugging to capture conversation logs
            )
            db.add(flow_execution)
            db.commit()
            
            execution_id = flow_execution.id
            logger.info(f"Created flow execution: {execution_id} with variables: {variables} (debugging: {debugging_enabled})")
            return execution_id
    
    async def publish_run_message(self, flow_execution_id: UUID):
        """Publish a StartFlowExecutionMessage using proper Celery protocol."""
        import json
        import base64
        
        # Create message header
        header = MessageHeader(
            correlation_id=str(uuid4()),
            flow_execution_id=str(flow_execution_id),
            tenant_id=str(self.system_tenant_id),
            user_id=str(self.system_user_id),
            debug=None
        )
        
        # Create start flow execution message using MessageFactory
        message = MessageFactory.create_start_flow_execution_message(
            header=header,
            flow_version_id=str(self.flow_version_id),
            enable_debugging=False
        )
        
        # Get the correct queue and task name for this message type
        queue = PoolRegistry.get_queue_for_processor(message.message_type)
        task_name = f"async_engine.{message.message_type}"
        
        message_dict = message.to_dict()
        task_id = str(uuid4())
        
        # Format as Celery task message (Celery protocol v2)
        # The body contains the task args as a JSON array
        celery_body = json.dumps([
            [message_dict],  # args (message is first argument)
            {},              # kwargs
            {                # embed
                'callbacks': None,
                'errbacks': None,
                'chain': None,
                'chord': None
            }
        ])
        
        # Create Celery protocol headers
        celery_headers = {
            'lang': 'py',
            'task': task_name,
            'id': task_id,
            'shadow': None,
            'eta': None,
            'expires': None,
            'group': None,
            'group_index': None,
            'retries': 0,
            'timelimit': [None, None],
            'root_id': task_id,
            'parent_id': None,
            'argsrepr': repr([message_dict])[:200],
            'kwargsrepr': '{}',
            'origin': 'axon-pfc@workflow-processor',
        }
        
        # Create aio_pika message with Celery headers
        aio_pika_message = aio_pika.Message(
            body=celery_body.encode('utf-8'),
            delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
            content_type='application/json',
            content_encoding='utf-8',
            headers=celery_headers
        )
        
        # Publish to existing queue (don't declare to avoid config conflicts)
        await self.messaging_backend.channel.default_exchange.publish(
            aio_pika_message,
            routing_key=queue
        )
        
        logger.info(f"Published Celery task '{task_name}' for execution {flow_execution_id} to queue {queue}")
    
    async def process_run(self, semaphore: asyncio.Semaphore, variables: Dict[str, Any], run_number: int):
        """Process a single run with semaphore control."""
        async with semaphore:
            logger.info(f"[Run {run_number}] Starting processing with variables: {variables}")
            
            try:
                # Create flow execution in database
                flow_execution_id = self.create_flow_execution(variables)
                
                # Publish message to RabbitMQ
                await self.publish_run_message(flow_execution_id)
                
                # Wait a bit to simulate processing and avoid overwhelming the system
                await asyncio.sleep(1)
                
                logger.info(f"[Run {run_number}] Successfully published run {flow_execution_id}")
                
                return flow_execution_id
                
            except Exception as e:
                logger.error(f"[Run {run_number}] Failed to process run: {e}", exc_info=True)
                return None
    
    async def process_runs(self, input_sets: List[Dict[str, Any]]):
        """Process multiple runs in parallel with semaphore control."""
        logger.info(f"Processing {len(input_sets)} runs with max concurrency: {self.max_concurrent_runs!s}")
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(self.max_concurrent_runs)
        
        # Create tasks for all runs
        tasks = [
            self.process_run(semaphore, variables, i+1)
            for i, variables in enumerate(input_sets)
        ]
        
        # Execute all tasks in parallel (but controlled by semaphore)
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Log results
        successful = [r for r in results if r is not None and not isinstance(r, Exception)]
        failed = [r for r in results if isinstance(r, Exception)]
        
        logger.info(f"Completed processing: {len(successful)} successful, {len(failed)} failed")
        
        return successful
    
    async def cleanup(self):
        """Cleanup connections."""
        if self.messaging_backend and hasattr(self.messaging_backend, 'connection'):
            if self.messaging_backend.connection and not self.messaging_backend.connection.is_closed:
                await self.messaging_backend.connection.close()
        
        logger.info("Cleanup completed")


async def main():
    """Main entry point."""
    logger.info("=" * 80)
    logger.info("Workflow Run Processor - Parallel Execution Demo")
    logger.info("=" * 80)
    
    # Hardcoded example input sets
    # These are example inputs for a simple team workflow
    input_sets = [
        {"topic": "space exploration"},
        {"topic": "ocean adventure"},
        {"topic": "mountain hiking"},
        {"topic": "desert journey"},
        {"topic": "forest expedition"},
    ]
    
    # Create processor
    processor = WorkflowRunProcessor(
        workflow_name="test",
        max_concurrent_runs=3  # Process 3 runs at a time
    )
    
    try:
        # Setup
        await processor.setup()
        
        # Process runs
        logger.info("")
        logger.info("Starting parallel run processing...")
        logger.info("")
        
        successful_runs = await processor.process_runs(input_sets)
        
        logger.info("")
        logger.info("=" * 80)
        logger.info(f"All runs published successfully!")
        logger.info(f"Total runs created: {len(successful_runs)}")
        logger.info(f"Flow executions: {[str(run_id) for run_id in successful_runs]}")
        logger.info("")
        logger.info("Note: The runs are now in RabbitMQ and will be processed by")
        logger.info("agentic-core workers (async-engine-worker). Make sure the workers")
        logger.info("are running to actually execute the workflows.")
        logger.info("=" * 80)
        
    except Exception as e:
        logger.error(f"Error in main: {e}", exc_info=True)
        return 1
    finally:
        await processor.cleanup()
    
    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))

