#!/usr/bin/env python3
"""Delete PPT Ingestion Tool from Agentic-Core"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Setup paths
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
load_dotenv(project_root / ".env")

# Get agentic-core path
agentic_core_path_str = os.getenv("AGENTIC_CORE_PATH", "../agentic-core")
agentic_core_path = Path(agentic_core_path_str)
if not agentic_core_path.is_absolute():
    agentic_core_path = (project_root / agentic_core_path).resolve()

backend_path = agentic_core_path / "backend"
backend_lib_path = backend_path / "lib"

if backend_path.exists():
    sys.path.insert(0, str(backend_lib_path))
    sys.path.insert(0, str(backend_path))
else:
    print(f"Error: agentic-core backend not found at {backend_path}")
    sys.exit(1)

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "postgresql://postgres:postgres@localhost:5432/invisible"
)

def delete_tool():
    """Delete the PPT ingestion tool."""
    print("Deleting PPT Ingestion Tool...")
    
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(bind=engine)
    db = SessionLocal()
    
    try:
        # Find the tool
        result = db.execute(text("""
            SELECT id, name FROM agentic_objects.tool 
            WHERE name = 'ppt_ingestion'
        """))
        
        tool = result.fetchone()
        
        if not tool:
            print("✓ Tool 'ppt_ingestion' not found (already deleted)")
            return
        
        tool_id = tool[0]
        print(f"Found tool ID: {tool_id}")
        
        # Delete tool_version entries first (foreign key)
        print("  → Deleting tool versions...")
        db.execute(text("""
            DELETE FROM agentic_objects.tool_version 
            WHERE tool_id = :tool_id
        """), {"tool_id": tool_id})
        
        # Delete tool
        print("  → Deleting tool...")
        db.execute(text("""
            DELETE FROM agentic_objects.tool 
            WHERE id = :tool_id
        """), {"tool_id": tool_id})
        
        db.commit()
        print("✓ Successfully deleted PPT Ingestion Tool")
        
    except Exception as e:
        db.rollback()
        print(f"✗ Error deleting tool: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        db.close()

if __name__ == "__main__":
    delete_tool()

