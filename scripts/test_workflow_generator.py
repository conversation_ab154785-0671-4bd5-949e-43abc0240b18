#!/usr/bin/env python3
"""
Test script for Workflow JSON Generator

This script tests the workflow JSON generation service to help diagnose issues.
"""

import sys
import time
from pathlib import Path

# Add backend to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src" / "backend"))

print("=" * 80)
print("Workflow JSON Generator Test Script")
print("=" * 80)
print()

# Test 1: Import the service
print("Test 1: Import service...")
try:
    from services.workflow_json_generator import get_workflow_json_generator
    print("✅ Service imported successfully")
except Exception as e:
    print(f"❌ Failed to import service: {e}")
    sys.exit(1)

# Test 2: Initialize the generator
print("\nTest 2: Initialize generator...")
try:
    generator = get_workflow_json_generator()
    print(f"✅ Generator initialized with model: {generator.model}")
except Exception as e:
    print(f"❌ Failed to initialize generator: {e}")
    sys.exit(1)

# Test 3: Create a test document
print("\nTest 3: Create test document...")
try:
    from docx import Document
    
    test_doc_path = "/tmp/test_workflow_spec.docx"
    doc = Document()
    doc.add_heading('Workflow Specification: Data Analysis', 0)
    
    doc.add_heading('Objective', level=1)
    doc.add_paragraph('Analyze data files and generate summary reports.')
    
    doc.add_heading('Inputs', level=1)
    doc.add_paragraph('• data_file: Path to CSV or Excel data file')
    doc.add_paragraph('• report_type: Type of report to generate (summary/detailed)')
    
    doc.add_heading('Processing Steps', level=1)
    doc.add_paragraph('1. Load and validate the data file')
    doc.add_paragraph('2. Perform statistical analysis')
    doc.add_paragraph('3. Identify trends and patterns')
    doc.add_paragraph('4. Generate visualizations')
    doc.add_paragraph('5. Create final report')
    
    doc.add_heading('Outputs', level=1)
    doc.add_paragraph('• summary: Text summary of findings')
    doc.add_paragraph('• statistics: Key statistical metrics')
    doc.add_paragraph('• report_path: Path to generated report file')
    
    doc.save(test_doc_path)
    print(f"✅ Test document created: {test_doc_path}")
except Exception as e:
    print(f"❌ Failed to create test document: {e}")
    sys.exit(1)

# Test 4: Test text extraction
print("\nTest 4: Extract text from document...")
try:
    extracted_text = generator.extract_text_from_docx(test_doc_path)
    print(f"✅ Extracted {len(extracted_text)} characters")
    print(f"   Preview: {extracted_text[:100]}...")
except Exception as e:
    print(f"❌ Failed to extract text: {e}")
    sys.exit(1)

# Test 5: Generate workflow JSON (this will take 30-60 seconds)
print("\nTest 5: Generate workflow JSON...")
print("   ⏳ This may take 30-60 seconds...")
try:
    task_id = generator.generate_workflow_json(
        spec_file_path=test_doc_path,
        workflow_name="Test Data Analysis Workflow"
    )
    print(f"✅ Generation started, task ID: {task_id}")
    
    # Poll for completion
    max_attempts = 60  # 2 minutes max
    for i in range(max_attempts):
        status = generator.get_task_status(task_id)
        
        print(f"   [{i+1}/{max_attempts}] Status: {status['status']} - {status['message']}")
        
        if status['status'] == 'completed':
            print("\n✅ Workflow JSON generated successfully!")
            
            import json
            workflow_json = status['workflow_json']
            
            print("\nGenerated Workflow Summary:")
            print(f"  Description: {workflow_json.get('description', 'N/A')}")
            print(f"  Input variables: {len(workflow_json.get('variables', {}).get('input', {}))}")
            print(f"  Output variables: {len(workflow_json.get('variables', {}).get('output', {}))}")
            print(f"  Steps: {len(workflow_json.get('steps', []))}")
            
            for idx, step in enumerate(workflow_json.get('steps', []), 1):
                team = step.get('team', {})
                agents = team.get('agents', [])
                print(f"    Step {idx}: {team.get('name', 'N/A')} ({len(agents)} agents)")
            
            print("\nFull JSON (first 500 chars):")
            json_str = json.dumps(workflow_json, indent=2)
            print(json_str[:500])
            if len(json_str) > 500:
                print(f"... ({len(json_str) - 500} more characters)")
            
            break
        
        elif status['status'] == 'failed':
            print(f"\n❌ Generation failed: {status.get('error', 'Unknown error')}")
            sys.exit(1)
        
        time.sleep(2)
    else:
        print(f"\n⚠️  Generation timed out after {max_attempts * 2} seconds")
        print(f"   Last status: {status['message']}")
        sys.exit(1)

except Exception as e:
    print(f"❌ Generation failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n" + "=" * 80)
print("✅ All tests passed! Workflow JSON Generator is working correctly.")
print("=" * 80)
print("\nYou can now use the frontend UI to generate workflows from specification documents.")
print("Navigate to: http://localhost:8000/src/frontend/index.html")
print("Click: 🚀 Generate from Doc")

