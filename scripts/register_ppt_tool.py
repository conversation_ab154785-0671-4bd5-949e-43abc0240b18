#!/usr/bin/env python3
"""
Register PPT Ingestion Tool in Agentic-Core

This script creates a Tool entry in the agentic-core database
so it appears in the UI's tool list tab.

Usage:
    python scripts/register_ppt_tool.py
"""

import os
import sys
import json
from pathlib import Path
from uuid import uuid4
from datetime import datetime
from dotenv import load_dotenv

# Setup paths
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment
load_dotenv(project_root / ".env")

# Get agentic-core path
agentic_core_path_str = os.getenv("AGENTIC_CORE_PATH", "../agentic-core")
agentic_core_path = Path(agentic_core_path_str)
if not agentic_core_path.is_absolute():
    agentic_core_path = (project_root / agentic_core_path).resolve()

backend_path = agentic_core_path / "backend"
backend_lib_path = backend_path / "lib"

if backend_path.exists():
    sys.path.insert(0, str(backend_lib_path))
    sys.path.insert(0, str(backend_path))
else:
    print(f"Error: agentic-core backend not found at {backend_path}")
    sys.exit(1)

# Import agentic-core modules
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from database.users.models import User
from database.agentic_objects.models import Namespace

# Database configuration
DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "postgresql://postgres:postgres@localhost:5432/invisible"
)


def get_system_user_id(db):
    """Get system user ID."""
    system_user = db.query(User).filter(User.email == "system@localhost").first()
    if not system_user:
        print("Error: System user not found. Please run setup_system_user_entitlements.sh first")
        sys.exit(1)
    return system_user.id


def get_default_namespace_id(db, user_id):
    """Get or create default namespace."""
    namespace = db.query(Namespace).filter(Namespace.name == "default").first()
    if not namespace:
        namespace = Namespace(
            id=uuid4(),
            name="default",
            created_by=user_id
        )
        db.add(namespace)
        db.flush()
    return namespace.id


def register_tool():
    """Register the PPT ingestion tool in the database."""
    print("=" * 80)
    print("Registering PPT Ingestion Tool")
    print("=" * 80)
    
    # Connect to database
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(bind=engine)
    db = SessionLocal()
    
    try:
        # Get system user and namespace
        user_id = get_system_user_id(db)
        namespace_id = get_default_namespace_id(db, user_id)
        
        tool_name = "ppt_ingestion"
        
        print(f"\nTool: {tool_name}")
        print(f"Description: Process PowerPoint (PPTX) or PDF files")
        
        # Check if tool already exists
        result = db.execute(text("""
            SELECT id, name FROM agentic_objects.tool 
            WHERE name = :name AND namespace_id = :namespace_id
        """), {"name": tool_name, "namespace_id": namespace_id})
        
        existing_tool = result.fetchone()
        
        if existing_tool:
            print(f"\n✗ Tool '{tool_name}' already exists (ID: {existing_tool[0]})")
            print("  Delete it first if you want to recreate it.")
            return
        
        # Generate UUIDs
        tool_id = uuid4()
        tool_version_id = uuid4()
        
        print(f"\n→ Creating tool...")
        
        # Insert into tool table
        db.execute(text("""
            INSERT INTO agentic_objects.tool (
                id, name, namespace_id, created_by, created_date, is_archived
            ) VALUES (
                :id, :name, :namespace_id, :created_by, :created_date, :is_archived
            )
        """), {
            "id": tool_id,
            "name": tool_name,
            "namespace_id": namespace_id,
            "created_by": user_id,
            "created_date": datetime.utcnow(),
            "is_archived": False
        })
        
        print(f"  ✓ Created tool (ID: {tool_id})")
        
        # Insert into tool_version table
        # Self-contained implementation - no external imports needed
        code = """
# PPT Ingestion Tool - Self-Contained Version
# This tool processes PowerPoint and PDF presentations
# Uses PyMuPDF for OCR and GPT-4 Vision for enhancement

import os
import base64
import tempfile
from pathlib import Path
from typing import List, Dict, Any, Optional
import fitz  # PyMuPDF
from pptx import Presentation
from openai import OpenAI

def process_presentation(file_path: str, use_vision: bool = True, output_format: str = 'text') -> dict:
    \"\"\"
    Process a PowerPoint or PDF presentation.
    
    Args:
        file_path: Path to the PPTX or PDF file
        use_vision: Whether to use GPT-4 Vision for enhancement (default: True)
        output_format: 'text' or 'json' (default: 'text')
    
    Returns:
        Dictionary with extracted content:
        - success: bool
        - total_slides: int
        - text_content: str (if output_format='text')
        - slides: list (if output_format='json')
    \"\"\"
    try:
        file_path = Path(file_path)
        if not file_path.exists():
            return {
                'success': False,
                'error': f'File not found: {file_path}',
                'file_path': str(file_path)
            }
        
        # Initialize OpenAI client
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            return {
                'success': False,
                'error': 'OPENAI_API_KEY environment variable not set',
                'file_path': str(file_path)
            }
        
        client = OpenAI(api_key=api_key)
        vision_model = os.getenv('VISION_MODEL', 'gpt-4o')
        
        # Process based on file type
        file_extension = file_path.suffix.lower()
        
        if file_extension == '.pdf':
            slides = _process_pdf(file_path)
        elif file_extension == '.pptx':
            slides = _process_pptx(file_path)
        else:
            return {
                'success': False,
                'error': f'Unsupported file format: {file_extension}. Only .pdf and .pptx supported.',
                'file_path': str(file_path)
            }
        
        # Enhance with vision if requested
        if use_vision:
            _enhance_with_vision(slides, client, vision_model)
        
        # Format results
        result = {
            'success': True,
            'total_slides': len(slides),
            'file_path': str(file_path)
        }
        
        if output_format == 'json':
            result['slides'] = slides
        else:
            # Text format
            text_parts = []
            for slide in slides:
                text_parts.append('=' * 80)
                text_parts.append(f\"Slide/Page {slide['page_number']}\")
                text_parts.append('=' * 80)
                text_parts.append('')
                text = slide.get('enhanced_text') or slide.get('ocr_text', '')
                text_parts.append(text)
                text_parts.append('')
            
            result['text_content'] = '\\n'.join(text_parts)
        
        return result
        
    except Exception as e:
        import traceback
        return {
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc(),
            'file_path': str(file_path)
        }

def _process_pdf(file_path: Path) -> List[Dict[str, Any]]:
    \"\"\"Process a PDF file.\"\"\"
    slides = []
    doc = fitz.open(file_path)
    
    try:
        for page_num in range(len(doc)):
            page = doc[page_num]
            ocr_text = page.get_text()
            
            # Convert page to image
            zoom = 2.0
            mat = fitz.Matrix(zoom, zoom)
            pix = page.get_pixmap(matrix=mat)
            image_data = pix.tobytes('png')
            
            slides.append({
                'page_number': page_num + 1,
                'ocr_text': ocr_text,
                'image_data': image_data,
                'enhanced_text': None
            })
    finally:
        doc.close()
    
    return slides

def _process_pptx(file_path: Path) -> List[Dict[str, Any]]:
    \"\"\"Process a PPTX file.\"\"\"
    slides = []
    prs = Presentation(file_path)
    
    # Convert PPTX to PDF for images
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_pdf:
        tmp_pdf_path = tmp_pdf.name
    
    try:
        # Convert using PyMuPDF
        doc = fitz.open(file_path)
        pdf_bytes = doc.convert_to_pdf()
        doc.close()
        
        with open(tmp_pdf_path, 'wb') as f:
            f.write(pdf_bytes)
        
        pdf_doc = fitz.open(tmp_pdf_path)
        
        for slide_num, slide in enumerate(prs.slides):
            # Extract text from PPTX
            text_parts = []
            for shape in slide.shapes:
                if hasattr(shape, 'text'):
                    text_parts.append(shape.text)
            
            ocr_text = '\\n'.join(text_parts)
            
            # Get image from PDF
            image_data = b''
            if slide_num < len(pdf_doc):
                page = pdf_doc[slide_num]
                zoom = 2.0
                mat = fitz.Matrix(zoom, zoom)
                pix = page.get_pixmap(matrix=mat)
                image_data = pix.tobytes('png')
            
            slides.append({
                'page_number': slide_num + 1,
                'ocr_text': ocr_text,
                'image_data': image_data,
                'enhanced_text': None
            })
        
        pdf_doc.close()
    finally:
        if os.path.exists(tmp_pdf_path):
            os.unlink(tmp_pdf_path)
    
    return slides

def _enhance_with_vision(slides: List[Dict[str, Any]], client: OpenAI, model: str) -> None:
    \"\"\"Enhance slides with GPT-4 Vision.\"\"\"
    prompt_template = \"\"\"
You are analyzing a presentation slide/page. I have provided you with:
1. OCR-extracted text from the slide
2. An image of the slide

Please provide a comprehensive text representation of this slide that includes:
- All text content (correcting any OCR errors)
- Descriptions of any tables, charts, graphs, or diagrams
- The structure and layout of the content
- Any important visual elements that convey information

OCR Text:
{ocr_text}

Please provide a pure text representation that captures all the information from this slide.
\"\"\"
    
    for slide in slides:
        if not slide.get('image_data'):
            slide['enhanced_text'] = slide['ocr_text']
            continue
        
        try:
            image_base64 = base64.b64encode(slide['image_data']).decode('utf-8')
            prompt = prompt_template.format(ocr_text=slide['ocr_text'])
            
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {
                        'role': 'user',
                        'content': [
                            {'type': 'text', 'text': prompt},
                            {
                                'type': 'image_url',
                                'image_url': {
                                    'url': f'data:image/png;base64,{image_base64}',
                                    'detail': 'high'
                                }
                            }
                        ]
                    }
                ],
                max_tokens=2000
            )
            
            slide['enhanced_text'] = response.choices[0].message.content
        except Exception as e:
            print(f'Warning: Failed to enhance slide {slide[\"page_number\"]}: {e}')
            slide['enhanced_text'] = slide['ocr_text']
"""
        
        # Arguments should be an empty array like other tools
        arguments = []
        
        db.execute(text("""
            INSERT INTO agentic_objects.tool_version (
                id, tool_id, code, arguments, description, 
                modified_by, modified_date, is_draft, category
            ) VALUES (
                :id, :tool_id, :code, CAST(:arguments AS jsonb), :description,
                :modified_by, :modified_date, :is_draft, :category
            )
        """), {
            "id": tool_version_id,
            "tool_id": tool_id,
            "code": code,
            "arguments": json.dumps(arguments),
            "description": "Process PowerPoint (PPTX) or PDF presentations. Extracts text via OCR using PyMuPDF and enhances with GPT-4 Vision to handle complex content like tables, charts, and graphs.",
            "modified_by": user_id,
            "modified_date": datetime.now(),
            "is_draft": False,
            "category": "document_processing"
        })
        
        print(f"  ✓ Created version (ID: {tool_version_id})")
        
        # Commit changes
        db.commit()
        
        print("\n" + "=" * 80)
        print("✓ Successfully registered PPT Ingestion Tool")
        print("=" * 80)
        print(f"\nTool ID: {tool_id}")
        print(f"Version ID: {tool_version_id}")
        print(f"\nThe tool should now appear in:")
        print(f"  • Agentic-Core UI → Tools tab")
        print(f"  • Available to workflows as: {tool_name}")
        print(f"\nUse in workflows:")
        print(f'''
{{
  "agents": [
    {{
      "name": "agent_name",
      "tools": ["{tool_name}"]
    }}
  ]
}}
''')
        
        # Verify it shows up
        print("\nVerifying registration...")
        result = db.execute(text("""
            SELECT t.id, t.name, tv.id as version_id, tv.description
            FROM agentic_objects.tool t
            JOIN agentic_objects.tool_version tv ON t.id = tv.tool_id
            WHERE t.name = :name
        """), {"name": tool_name})
        
        row = result.fetchone()
        if row:
            print(f"✓ Confirmed: Tool '{row[1]}' is registered")
            print(f"  Tool ID: {row[0]}")
            print(f"  Version ID: {row[2]}")
        else:
            print("✗ Warning: Could not verify tool registration")
        
    except Exception as e:
        db.rollback()
        print(f"\n✗ Error registering tool: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    finally:
        db.close()


def main():
    register_tool()


if __name__ == "__main__":
    main()

