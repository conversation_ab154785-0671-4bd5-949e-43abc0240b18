#!/bin/bash
# Setup system user entitlements for all tenants
#
# This script grants the system user (used by GraphQL log writer and other services)
# access to all tenants in the database. Without these entitlements, the log writer
# will fail with "Forbidden" errors when trying to subscribe to execution logs.

set -e

echo "🔧 Setting up system user entitlements..."
echo ""

# Check if agentic-core is running
if ! docker ps | grep -q "agentic-core-postgres"; then
    echo "❌ Error: agentic-core PostgreSQL container is not running"
    echo "   Please start agentic-core first:"
    echo "   cd ../agentic-core && docker compose up -d"
    exit 1
fi

# Get system user ID
echo "📋 Looking up system user..."
SYSTEM_USER_ID=$(docker exec agentic-core-postgres-1 psql -U postgres -d invisible -tAc \
  "SELECT id FROM users.user WHERE email = 'system@internal';" 2>/dev/null || echo "")

if [ -z "$SYSTEM_USER_ID" ]; then
  echo "❌ Error: System user not found in database"
  echo "   Expected email: system@internal"
  echo ""
  echo "   Make sure agentic-core is properly initialized:"
  echo "   1. cd ../agentic-core"
  echo "   2. docker compose up -d"
  echo "   3. Wait for services to initialize"
  exit 1
fi

echo "✓ Found system user: $SYSTEM_USER_ID"
echo ""

# Count tenants
TENANT_COUNT=$(docker exec agentic-core-postgres-1 psql -U postgres -d invisible -tAc \
  "SELECT COUNT(*) FROM users.tenant;" 2>/dev/null || echo "0")

if [ "$TENANT_COUNT" -eq "0" ]; then
  echo "⚠️  Warning: No tenants found in database"
  echo "   System user entitlements will be empty until tenants are created"
  echo "   Run this script again after creating tenants"
  exit 0
fi

echo "📋 Found $TENANT_COUNT tenant(s)"
echo ""

# Grant entitlements for all tenants
echo "🔐 Granting entitlements to system user for all tenants..."
RESULT=$(docker exec agentic-core-postgres-1 psql -U postgres -d invisible -tAc "
  INSERT INTO users.tenant_entitlements (id, user_id, tenant_id, claims)
  SELECT 
    gen_random_uuid(),
    '$SYSTEM_USER_ID'::uuid,
    id,
    ARRAY['admin', 'audit']::users.tenant_entitlement[]
  FROM users.tenant
  ON CONFLICT (tenant_id, user_id) DO UPDATE 
  SET claims = ARRAY['admin', 'audit']::users.tenant_entitlement[]
  RETURNING user_id;
" 2>&1)

if [ $? -ne 0 ]; then
  echo "❌ Error granting entitlements:"
  echo "$RESULT"
  exit 1
fi

# Verify entitlements were granted
ENTITLEMENT_COUNT=$(docker exec agentic-core-postgres-1 psql -U postgres -d invisible -tAc \
  "SELECT COUNT(*) FROM users.tenant_entitlements WHERE user_id = '$SYSTEM_USER_ID';" 2>/dev/null || echo "0")

echo "✓ System user entitlements configured for $ENTITLEMENT_COUNT tenant(s)"
echo ""
echo "📊 Verification:"
docker exec agentic-core-postgres-1 psql -U postgres -d invisible -c "
  SELECT 
    te.tenant_id,
    t.name as tenant_name,
    te.claims
  FROM users.tenant_entitlements te
  JOIN users.tenant t ON t.id = te.tenant_id
  WHERE te.user_id = '$SYSTEM_USER_ID'::uuid
  ORDER BY t.name;
"

echo ""
echo "✅ System user entitlement setup complete!"
echo ""
echo "The GraphQL log writer and other system services can now:"
echo "  - Subscribe to flow execution logs (audit permission)"
echo "  - Manage tenant resources (admin permission)"
echo ""
echo "💡 If you create new tenants in the future, run this script again"
echo "   to grant the system user access to them."

