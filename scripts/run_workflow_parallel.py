#!/usr/bin/env python3
"""
All-in-one script to create and process workflow runs in parallel.

This script demonstrates the complete workflow:
1. Get an existing workflow from the database (uses "test" workflow)
2. Create multiple run executions with different input sets
3. Publish run messages to RabbitMQ
4. Process runs from RabbitMQ in parallel with semaphore-controlled concurrency

This is a simplified demonstration. For production use, run the actual
async-engine-worker services from agentic-core.

Usage:
    python run_workflow_parallel.py
"""

import os
import sys
import asyncio
import logging
import json
import subprocess
from pathlib import Path
from typing import List, Dict, Any, Optional
from uuid import UUID, uuid4
from datetime import datetime
from dotenv import load_dotenv

# Add agentic-core to Python path
project_root = Path(__file__).parent.resolve()
load_dotenv(project_root / ".env")

agentic_core_path_str = os.getenv("AGENTIC_CORE_PATH", "../agentic-core")
agentic_core_path = Path(agentic_core_path_str)
if not agentic_core_path.is_absolute():
    agentic_core_path = (project_root / agentic_core_path).resolve()

backend_path = agentic_core_path / "backend"
backend_lib_path = backend_path / "lib"

if backend_path.exists():
    sys.path.insert(0, str(backend_lib_path))
    sys.path.insert(0, str(backend_path))
else:
    print(f"Error: agentic-core backend not found at {backend_path}")
    sys.exit(1)

# Import agentic-core modules
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from database.users.models import User, Tenant
from database.agentic_objects.models import Flow, FlowVersion
from database.flow_execution.models import FlowExecution
from data_types.database.flow_executions import FlowResult

# Import messaging and async engine modules
import aio_pika
from async_engine.core.models.messages import MessageHeader, StartFlowExecutionMessage
from async_engine.core.models.messaging_config import MessagingConfig
from async_engine.messaging.factory import MessagingBackendFactory

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def discover_docker_port(service: str, internal_port: int, agentic_core_path: Path) -> int:
    """
    Discover the host port for a Docker service, similar to start.sh logic.
    
    Args:
        service: Docker service name (e.g., 'postgres', 'rabbitmq')
        internal_port: Internal container port (e.g., 5432, 5672)
        agentic_core_path: Path to agentic-core directory
        
    Returns:
        Host port number
    """
    try:
        # Method 1: Use docker compose port command
        result = subprocess.run(
            ['docker', 'compose', 'port', service, str(internal_port)],
            cwd=str(agentic_core_path),
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0 and result.stdout.strip():
            # Output format: "0.0.0.0:PORT" or ":::PORT"
            port_str = result.stdout.strip().split(':')[-1]
            return int(port_str)
        
        # Method 2: Fallback to docker inspect
        result = subprocess.run(
            ['docker', 'compose', 'ps', '-q', service],
            cwd=str(agentic_core_path),
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0 and result.stdout.strip():
            container_id = result.stdout.strip()
            result = subprocess.run(
                ['docker', 'inspect', container_id,
                 '--format', f'{{{{(index (index .NetworkSettings.Ports "{internal_port}/tcp") 0).HostPort}}}}'],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0 and result.stdout.strip():
                return int(result.stdout.strip())
        
        # Fallback to default port
        logger.warning(f"Could not discover port for {service}, using default {internal_port}")
        return internal_port
        
    except Exception as e:
        logger.warning(f"Error discovering port for {service}: {e}, using default {internal_port}")
        return internal_port


class WorkflowParallelRunner:
    """Orchestrates parallel workflow execution."""
    
    def __init__(
        self,
        workflow_name: str = "test",
        max_concurrent_runs: int = 3,
        database_url: Optional[str] = None,
        rabbitmq_url: Optional[str] = None
    ):
        self.workflow_name = workflow_name
        self.max_concurrent_runs = max_concurrent_runs
        
        # Discover ports from Docker if not provided
        if database_url is None and rabbitmq_url is None:
            database_url, rabbitmq_url = self._discover_service_urls()
        
        self.database_url = database_url or os.getenv(
            "DATABASE_URL",
            "postgresql://postgres:postgres@localhost:5432/invisible"
        )
        self.rabbitmq_url = rabbitmq_url or os.getenv(
            "RABBITMQ_URL",
            "amqp://guest:guest@localhost:5672/"
        )
        
        # Database setup
        assert self.database_url, "DATABASE_URL must be set"
        self.engine = create_engine(self.database_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        
        # State
        self.flow_version_id: Optional[UUID] = None
        self.system_user_id: Optional[UUID] = None
        self.system_tenant_id: Optional[UUID] = None
        
        # RabbitMQ
        self.rabbitmq_connection: Optional[aio_pika.Connection] = None
        self.rabbitmq_channel: Optional[aio_pika.Channel] = None
        self.rabbitmq_exchange: Optional[aio_pika.Exchange] = None
        
        # Messaging backend for processing
        self.messaging_backend = None
    
    def _discover_service_urls(self) -> tuple[str, str]:
        """Discover database and RabbitMQ URLs from running Docker containers."""
        # Get agentic-core path
        agentic_core_path_str = os.getenv("AGENTIC_CORE_PATH", "../agentic-core")
        agentic_core_path = Path(agentic_core_path_str)
        if not agentic_core_path.is_absolute():
            agentic_core_path = (project_root / agentic_core_path).resolve()
        
        # Discover ports
        postgres_port = discover_docker_port('postgres', 5432, agentic_core_path)
        rabbitmq_port = discover_docker_port('rabbitmq', 5672, agentic_core_path)
        
        database_url = f"postgresql://postgres:postgres@localhost:{postgres_port}/invisible"
        rabbitmq_url = f"amqp://guest:guest@localhost:{rabbitmq_port}/"
        
        logger.info(f"Discovered PostgreSQL port: {postgres_port}")
        logger.info(f"Discovered RabbitMQ port: {rabbitmq_port}")
        
        return database_url, rabbitmq_url
        
    async def setup(self):
        """Initialize connections."""
        logger.info("=" * 80)
        logger.info("Setting up Workflow Parallel Runner...")
        logger.info("=" * 80)
        
        # Get workflow from database
        with self.SessionLocal() as db:
            flow_version = self._get_workflow(db)
            self.flow_version_id = flow_version.id
            
            # Get system user and tenant
            system_user = self._get_or_create_system_user(db)
            self.system_user_id = system_user.id
            
            tenant = db.query(Tenant).first()
            if not tenant:
                raise Exception("No tenant found in database")
            self.system_tenant_id = tenant.id
        
        logger.info(f"✓ Found workflow '{self.workflow_name}'")
        logger.info(f"  Version ID: {self.flow_version_id}")
        
        # Setup RabbitMQ
        await self._setup_rabbitmq()
        
        # Setup messaging backend
        await self._setup_messaging_backend()
        
        logger.info("✓ Setup complete")
        logger.info("")
        
    def _get_workflow(self, db: Session) -> FlowVersion:
        """Get the workflow by name from database."""
        flow = db.query(Flow).filter(
            Flow.name == self.workflow_name,
            Flow.is_archived.is_(False)
        ).first()
        
        if not flow:
            raise Exception(f"Workflow '{self.workflow_name}' not found")
        
        flow_version = db.query(FlowVersion).filter(
            FlowVersion.flow_id == flow.id
        ).order_by(FlowVersion.modified_date.desc()).first()
        
        if not flow_version:
            raise Exception(f"No version found for workflow '{self.workflow_name}'")
        
        return flow_version
    
    def _get_or_create_system_user(self, db: Session) -> User:
        """Get or create system user."""
        system_user = db.query(User).filter(User.email == "system@localhost").first()
        if not system_user:
            system_tenant = db.query(Tenant).filter(Tenant.name == "system").first()
            if not system_tenant:
                system_tenant = Tenant(id=uuid4(), name="system")
                db.add(system_tenant)
                db.flush()
            
            system_user = User(
                id=uuid4(),
                email="system@localhost",
                display_name="System User",
                system_admin=True
            )
            db.add(system_user)
            db.commit()
        
        return system_user
    
    async def _setup_rabbitmq(self):
        """Setup RabbitMQ connection."""
        logger.info(f"Connecting to RabbitMQ: {self.rabbitmq_url}")
        
        self.rabbitmq_connection = await aio_pika.connect_robust(self.rabbitmq_url)
        self.rabbitmq_channel = await self.rabbitmq_connection.channel()
        
        self.rabbitmq_exchange = await self.rabbitmq_channel.declare_exchange(
            'agentic',
            aio_pika.ExchangeType.TOPIC,
            durable=True
        )
        
        logger.info("✓ RabbitMQ connection established")
    
    async def _setup_messaging_backend(self):
        """Setup agentic-core messaging backend."""
        config = MessagingConfig(
            backend_type="aio_pika",
            broker_url=self.rabbitmq_url,
            result_backend=None
        )
        
        self.messaging_backend = MessagingBackendFactory.create_backend("aio_pika", config)
        await self.messaging_backend.connect()
        
        logger.info("✓ Messaging backend initialized")
    
    def create_flow_execution(self, variables: Dict[str, Any]) -> UUID:
        """Create a FlowExecution record."""
        with self.SessionLocal() as db:
            flow_execution = FlowExecution(
                id=uuid4(),
                correlation_id=uuid4(),
                flow_version_id=self.flow_version_id,
                tenant_id=self.system_tenant_id,
                user_id=self.system_user_id,
                result=FlowResult.PENDING,
                variables=variables,
                debugging_enabled=False
            )
            db.add(flow_execution)
            db.commit()
            
            return flow_execution.id
    
    async def publish_run(self, flow_execution_id: UUID, variables: Dict[str, Any]):
        """Publish a run to RabbitMQ."""
        header = MessageHeader(
            correlation_id=str(uuid4()),
            flow_execution_id=str(flow_execution_id),
            tenant_id=str(self.system_tenant_id),
            user_id=str(self.system_user_id),
            debug=None
        )
        
        message = StartFlowExecutionMessage(
            message_type="start_flow_execution",
            header=header,
            timestamp=datetime.utcnow().isoformat(),
            flow_version_id=str(self.flow_version_id),
            enable_debugging=False
        )
        
        # Publish to RabbitMQ using messaging backend
        assert self.messaging_backend is not None, "Messaging backend not initialized"
        await self.messaging_backend.publish_message(
            message,
            routing_key="agentic.flyweight"
        )
        
        logger.info(f"📤 Published run {flow_execution_id} with variables: {variables}")
        
        return flow_execution_id
    
    async def create_and_publish_run(
        self,
        semaphore: asyncio.Semaphore,
        variables: Dict[str, Any],
        run_number: int
    ) -> Optional[UUID]:
        """Create and publish a single run with semaphore control."""
        async with semaphore:
            try:
                logger.info(f"[Run {run_number}] Creating execution...")
                
                # Create flow execution in database
                flow_execution_id = self.create_flow_execution(variables)
                
                # Publish to RabbitMQ
                await self.publish_run(flow_execution_id, variables)
                
                # Small delay between publishes
                await asyncio.sleep(0.5)
                
                return flow_execution_id
                
            except Exception as e:
                logger.error(f"[Run {run_number}] Failed: {e}", exc_info=True)
                return None
    
    async def create_and_publish_runs(self, input_sets: List[Dict[str, Any]]) -> List[UUID]:
        """Create and publish all runs in parallel."""
        logger.info("=" * 80)
        logger.info("Creating and Publishing Runs")
        logger.info("=" * 80)
        logger.info(f"Input sets: {len(input_sets)}")
        logger.info(f"Max concurrency: {self.max_concurrent_runs}")
        logger.info("")
        
        semaphore = asyncio.Semaphore(self.max_concurrent_runs)
        
        tasks = [
            self.create_and_publish_run(semaphore, variables, i+1)
            for i, variables in enumerate(input_sets)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter for successful UUID results only
        successful: List[UUID] = []
        for r in results:
            if r is not None and not isinstance(r, Exception) and isinstance(r, UUID):
                successful.append(r)
        
        logger.info("")
        logger.info(f"✓ Published {len(successful)} runs successfully")
        logger.info("")
        
        return successful
    
    async def monitor_runs(self, run_ids: List[UUID], timeout: int = 300):
        """Monitor run execution status."""
        logger.info("=" * 80)
        logger.info("Monitoring Run Execution")
        logger.info("=" * 80)
        logger.info(f"Monitoring {len(run_ids)} runs (timeout: {timeout}s)")
        logger.info("Press Ctrl+C to stop monitoring")
        logger.info("")
        
        start_time = asyncio.get_event_loop().time()
        check_interval = 5  # Check every 5 seconds
        
        completed = set()
        failed = set()
        
        try:
            while True:
                elapsed = asyncio.get_event_loop().time() - start_time
                
                if elapsed > timeout:
                    logger.info(f"⏱️  Timeout reached ({timeout}s)")
                    break
                
                # Check status of all runs
                with self.SessionLocal() as db:
                    for run_id in run_ids:
                        if run_id in completed or run_id in failed:
                            continue
                        
                        execution = db.query(FlowExecution).filter(
                            FlowExecution.id == run_id
                        ).first()
                        
                        if execution:
                            if execution.result == FlowResult.COMPLETE:
                                completed.add(run_id)
                                logger.info(f"✅ Run {run_id} completed")
                            elif execution.result == FlowResult.FAILED:
                                failed.add(run_id)
                                logger.info(f"❌ Run {run_id} failed: {execution.failure_reason}")
                
                # Check if all done
                if len(completed) + len(failed) == len(run_ids):
                    logger.info("")
                    logger.info("All runs completed!")
                    break
                
                # Status update
                pending = len(run_ids) - len(completed) - len(failed)
                logger.info(f"Status: {len(completed)} completed, {len(failed)} failed, {pending} pending ({elapsed:.0f}s elapsed)")
                
                await asyncio.sleep(check_interval)
        
        except KeyboardInterrupt:
            logger.info("\nMonitoring interrupted")
        
        logger.info("")
        logger.info("=" * 80)
        logger.info("Final Results")
        logger.info("=" * 80)
        logger.info(f"Completed: {len(completed)}")
        logger.info(f"Failed: {len(failed)}")
        logger.info(f"Pending: {len(run_ids) - len(completed) - len(failed)}")
        logger.info("")
        
        return completed, failed
    
    async def cleanup(self):
        """Cleanup connections."""
        if self.messaging_backend:
            await self.messaging_backend.disconnect()
        if self.rabbitmq_channel:
            await self.rabbitmq_channel.close()
        if self.rabbitmq_connection:
            await self.rabbitmq_connection.close()
        
        logger.info("Cleanup completed")


async def main():
    """Main entry point."""
    logger.info("")
    logger.info("╔" + "=" * 78 + "╗")
    logger.info("║" + " " * 20 + "Workflow Parallel Runner" + " " * 34 + "║")
    logger.info("╚" + "=" * 78 + "╝")
    logger.info("")
    
    # Hardcoded example input sets for the "test" workflow
    # The test workflow is a simple team that creates greetings based on a topic
    input_sets = [
        {"topic": "space exploration"},
        {"topic": "ocean adventure"},
        {"topic": "mountain hiking"},
        {"topic": "desert journey"},
        {"topic": "forest expedition"},
    ]
    
    logger.info("This script will:")
    logger.info("  1. Create flow executions for the 'test' workflow")
    logger.info("  2. Publish run messages to RabbitMQ")
    logger.info("  3. Monitor execution progress")
    logger.info("")
    logger.info("⚠️  Important: Make sure agentic-core workers are running!")
    logger.info("   Run: cd ../agentic-core && docker compose up async-engine-flyweight async-engine-async")
    logger.info("")
    
    runner = WorkflowParallelRunner(
        workflow_name="test",
        max_concurrent_runs=3
    )
    
    try:
        # Setup
        await runner.setup()
        
        # Create and publish runs
        run_ids = await runner.create_and_publish_runs(input_sets)
        
        if not run_ids:
            logger.error("No runs were published successfully")
            return 1
        
        # Monitor runs
        await runner.monitor_runs(run_ids, timeout=600)  # 10 minute timeout
        
        logger.info("=" * 80)
        logger.info("Run IDs for reference:")
        for i, run_id in enumerate(run_ids, 1):
            logger.info(f"  {i}. {run_id}")
        logger.info("=" * 80)
        
    except Exception as e:
        logger.error(f"Error: {e}", exc_info=True)
        return 1
    finally:
        await runner.cleanup()
    
    return 0


if __name__ == "__main__":
    try:
        sys.exit(asyncio.run(main()))
    except KeyboardInterrupt:
        logger.info("\nExiting...")
        sys.exit(0)

