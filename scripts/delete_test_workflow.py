#!/usr/bin/env python3
"""
Delete the 'test' workflow and all its related data from the database.
"""

import os
import sys
from pathlib import Path

# Setup Python path
project_root = Path(__file__).parent.resolve()
sys.path.insert(0, str(project_root / 'src' / 'backend'))

agentic_core_path_str = os.getenv('AGENTIC_CORE_PATH', '../agentic-core')
agentic_core_path = Path(agentic_core_path_str)
if not agentic_core_path.is_absolute():
    agentic_core_path = (project_root / agentic_core_path).resolve()

sys.path.insert(0, str(agentic_core_path / 'backend' / 'lib'))

# Import database models
from database.agentic_objects.models import Flow, FlowVersion, Step
from database.flow_execution.models import FlowExecution, FlowExecutionStep
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

engine = create_engine('postgresql://postgres:postgres@localhost:59037/invisible')
SessionLocal = sessionmaker(bind=engine)

print("Deleting 'test' workflow...")

with SessionLocal() as db:
    flow = db.query(Flow).filter(Flow.name == 'test').first()
    if flow:
        print(f"Found workflow 'test' (ID: {flow.id})")
        
        # Use raw SQL with CASCADE to delete all related data
        from sqlalchemy import text
        print("  Deleting workflow and all related data with CASCADE...")
        db.execute(text("DELETE FROM agentic_objects.flow WHERE id = :flow_id"), {"flow_id": str(flow.id)})
        
        db.commit()
        print("✓ Successfully deleted workflow 'test' and all related data")
    else:
        print("Workflow 'test' not found")

