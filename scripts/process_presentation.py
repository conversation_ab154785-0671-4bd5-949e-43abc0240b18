#!/usr/bin/env python3
"""
CLI tool for processing PPT/PDF presentations

Usage:
    python scripts/process_presentation.py input.pptx --output output.txt
    python scripts/process_presentation.py input.pdf --output output.json --format json
"""

import argparse
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.backend.services.ppt_ingestion import get_ppt_ingestion_service


def main():
    parser = argparse.ArgumentParser(
        description="Process PowerPoint or PDF presentations"
    )
    parser.add_argument(
        "input_file",
        help="Path to input file (PPTX or PDF)"
    )
    parser.add_argument(
        "-o", "--output",
        help="Output file path (default: <input>_processed.txt)",
        default=None
    )
    parser.add_argument(
        "-f", "--format",
        choices=["text", "json"],
        default="text",
        help="Output format (default: text)"
    )
    parser.add_argument(
        "--no-vision",
        action="store_true",
        help="Disable GPT-4 Vision enhancement"
    )
    parser.add_argument(
        "--vision-prompt",
        help="Custom prompt for vision model",
        default=None
    )
    parser.add_argument(
        "--api-key",
        help="OpenAI API key (if not set in environment)",
        default=None
    )
    
    args = parser.parse_args()
    
    # Determine output path
    input_path = Path(args.input_file)
    if args.output:
        output_path = Path(args.output)
    else:
        suffix = ".json" if args.format == "json" else ".txt"
        output_path = input_path.with_name(f"{input_path.stem}_processed{suffix}")
    
    try:
        # Initialize service
        print(f"Initializing PPT ingestion service...")
        service = get_ppt_ingestion_service(openai_api_key=args.api_key)
        
        # Process file
        print(f"Processing {input_path}...")
        slides = service.process_file(
            file_path=input_path,
            use_vision=not args.no_vision,
            vision_prompt=args.vision_prompt
        )
        
        print(f"Extracted {len(slides)} slides/pages")
        
        # Export results
        print(f"Exporting to {output_path}...")
        if args.format == "json":
            service.export_to_json(slides, output_path)
        else:
            service.export_to_text(slides, output_path)
        
        print(f"✓ Successfully processed and saved to {output_path}")
        
        # Print summary
        for slide in slides:
            ocr_len = len(slide.ocr_text)
            enhanced_len = len(slide.enhanced_text) if slide.enhanced_text else 0
            status = "✓" if slide.enhanced_text else "○"
            print(f"  {status} Slide {slide.page_number}: {ocr_len} chars OCR, {enhanced_len} chars enhanced")
    
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()

