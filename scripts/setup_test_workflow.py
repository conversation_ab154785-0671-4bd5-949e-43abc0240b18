#!/usr/bin/env python3
"""
Setup script to create the "test" workflow in the database.

This creates a simple team-based workflow that can be used
with the run_workflow_parallel.py script.

Usage:
    python setup_test_workflow.py
"""

import requests
import json
import sys
from pathlib import Path

# Load the example team JSON
example_json_path = Path(__file__).parent / "src" / "backend" / "example_team.json"

if not example_json_path.exists():
    print(f"Error: Could not find {example_json_path}")
    sys.exit(1)

with open(example_json_path, 'r') as f:
    workflow_data = json.load(f)

# API endpoint
API_URL = "http://localhost:8000/api/workflows"

print("=" * 80)
print("Creating 'test' workflow in database")
print("=" * 80)
print()

# Check if workflow exists and delete it if it does
print("Checking if 'test' workflow already exists...")
try:
    list_response = requests.get(API_URL)
    list_response.raise_for_status()
    workflows = list_response.json()
    
    for wf in workflows:
        if wf.get('name') == 'test':
            print(f"✓ Found existing workflow 'test' (ID: {wf['id']}), deleting it...")
            # Delete via database directly since API might not support delete
            import sys
            import os
            from pathlib import Path
            
            # Setup Python path for database imports
            project_root = Path(__file__).parent.resolve()
            sys.path.insert(0, str(project_root / 'src' / 'backend'))
            
            agentic_core_path_str = os.getenv('AGENTIC_CORE_PATH', '../agentic-core')
            agentic_core_path = Path(agentic_core_path_str)
            if not agentic_core_path.is_absolute():
                agentic_core_path = (project_root / agentic_core_path).resolve()
            
            sys.path.insert(0, str(agentic_core_path / 'backend' / 'lib'))
            
            # Now import database models
            from database.flow.models import Flow, FlowVersion, Step
            from database.flow_execution.models import FlowExecution
            from sqlalchemy import create_engine
            from sqlalchemy.orm import sessionmaker
            
            engine = create_engine('postgresql://postgres:postgres@localhost:59037/invisible')
            SessionLocal = sessionmaker(bind=engine)
            
            with SessionLocal() as db:
                flow = db.query(Flow).filter(Flow.id == wf['flow_id']).first()
                if flow:
                    # Delete related records
                    db.query(FlowExecution).filter(FlowExecution.flow_version_id.in_(
                        db.query(FlowVersion.id).filter(FlowVersion.flow_id == flow.id)
                    )).delete(synchronize_session=False)
                    
                    db.query(Step).filter(Step.flow_version_id.in_(
                        db.query(FlowVersion.id).filter(FlowVersion.flow_id == flow.id)
                    )).delete(synchronize_session=False)
                    
                    db.query(FlowVersion).filter(FlowVersion.flow_id == flow.id).delete(synchronize_session=False)
                    db.query(Flow).filter(Flow.id == flow.id).delete(synchronize_session=False)
                    db.commit()
                    print("✓ Deleted old workflow and all related data")
            break
except requests.exceptions.RequestException as e:
    print(f"Error checking existing workflows: {e}")
    print("Make sure the backend is running at http://localhost:8000")
    sys.exit(1)

# Create the workflow
print("Creating 'test' workflow...")

payload = {
    "name": "test",
    "workflow": workflow_data,
    "namespace": "default",
    "is_draft": False
}

try:
    response = requests.post(
        API_URL,
        json=payload,
        headers={"Content-Type": "application/json"}
    )
    response.raise_for_status()
    result = response.json()
    
    print("✓ Successfully created workflow 'test'")
    print()
    print("Workflow Details:")
    print(f"  ID: {result['id']}")
    print(f"  Flow ID: {result['flow_id']}")
    print(f"  Name: {result['name']}")
    print(f"  Namespace: {result['namespace']}")
    print()
    print("You can now run: python process_runs.py")
    print("=" * 80)
    
except requests.exceptions.RequestException as e:
    print(f"Error creating workflow: {e}")
    if hasattr(e.response, 'text'):
        print(f"Response: {e.response.text}")
    sys.exit(1)

