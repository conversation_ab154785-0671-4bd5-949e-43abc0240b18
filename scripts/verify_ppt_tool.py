#!/usr/bin/env python3
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

agentic_core_path = Path(os.getenv('AGENTIC_CORE_PATH', '../agentic-core')).resolve()
sys.path.insert(0, str(agentic_core_path / 'backend/lib'))
sys.path.insert(0, str(agentic_core_path / 'backend'))

from sqlalchemy import create_engine, text

DATABASE_URL = os.getenv('DATABASE_URL', 'postgresql://postgres:postgres@localhost:5432/invisible')
engine = create_engine(DATABASE_URL)

with engine.connect() as conn:
    result = conn.execute(text("""
        SELECT t.name, tv.arguments, tv.description, tv.category
        FROM agentic_objects.tool t
        JOIN agentic_objects.tool_version tv ON t.id = tv.tool_id
        WHERE t.name = 'ppt_ingestion'
    """))
    
    print('PPT Ingestion Tool:')
    for row in result:
        print(f'  Name: {row[0]}')
        print(f'  Arguments: {row[1]}')
        print(f'  Description: {row[2][:80]}...')
        print(f'  Category: {row[3]}')
        print()
        print('✓ Tool is correctly registered!')

