#!/usr/bin/env python3
"""
Worker that processes workflow runs from RabbitMQ in parallel.

This script demonstrates how to:
1. Consume run messages from RabbitMQ
2. Process runs using agentic-core processors
3. Control concurrency with a semaphore
4. Handle simple team workflows (no tool use or delegates for now)

Usage:
    python run_processor_worker.py

Note: This is a simplified worker that processes messages directly.
      In production, use the full async-engine-worker service from agentic-core.
"""

import os
import sys
import asyncio
import logging
import json
from pathlib import Path
from typing import Optional, Dict, Any
from uuid import UUID
from dotenv import load_dotenv

# Add agentic-core to Python path
project_root = Path(__file__).parent.resolve()
load_dotenv(project_root / ".env")

agentic_core_path_str = os.getenv("AGENTIC_CORE_PATH", "../agentic-core")
agentic_core_path = Path(agentic_core_path_str)
if not agentic_core_path.is_absolute():
    agentic_core_path = (project_root / agentic_core_path).resolve()

backend_path = agentic_core_path / "backend"
backend_lib_path = backend_path / "lib"

if backend_path.exists():
    sys.path.insert(0, str(backend_lib_path))
    sys.path.insert(0, str(backend_path))
else:
    print(f"Error: agentic-core backend not found at {backend_path}")
    sys.exit(1)

# Import agentic-core modules
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import aio_pika

from async_engine.core.models.messages import StartFlowExecutionMessage, MessageHeader
from async_engine.core.models.messaging_config import MessagingConfig
from async_engine.database.factory import DatabaseHandlerFactory
from async_engine.core.interfaces.execution_context import ExecutionContext
from async_engine.processors.processor_factory import ProcessorFactory

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class RunProcessorWorker:
    """Worker that processes runs from RabbitMQ with controlled concurrency."""
    
    def __init__(
        self,
        max_concurrent_runs: int = 3,
        queue_name: str = "agentic.flyweight",
        database_url: Optional[str] = None,
        rabbitmq_url: Optional[str] = None
    ):
        self.max_concurrent_runs = max_concurrent_runs
        self.queue_name = queue_name
        self.database_url = database_url or os.getenv(
            "DATABASE_URL",
            "postgresql://postgres:postgres@localhost:5432/invisible"
        )
        self.rabbitmq_url = rabbitmq_url or os.getenv(
            "RABBITMQ_URL",
            "amqp://guest:guest@localhost:5672/"
        )
        
        # Database setup
        assert self.database_url, "DATABASE_URL must be set"
        self.engine = create_engine(self.database_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        
        # Create database handler for async engine
        self.db_handler = DatabaseHandlerFactory.create_handler(
            handler_type="direct",
            database_url=self.database_url
        )
        
        # RabbitMQ connection
        self.rabbitmq_connection: Optional[aio_pika.Connection] = None
        self.rabbitmq_channel: Optional[aio_pika.Channel] = None
        self.rabbitmq_queue: Optional[aio_pika.Queue] = None
        
        # Concurrency control
        self.semaphore = asyncio.Semaphore(max_concurrent_runs)
        self.active_tasks: set[asyncio.Task[Any]] = set()
        self.shutdown_event = asyncio.Event()
        
    async def setup(self):
        """Initialize RabbitMQ connection and queue."""
        logger.info(f"Setting up RunProcessorWorker (max concurrency: {self.max_concurrent_runs})...")
        
        # Setup RabbitMQ connection
        logger.info(f"Connecting to RabbitMQ at {self.rabbitmq_url}...")
        self.rabbitmq_connection = await aio_pika.connect_robust(self.rabbitmq_url)
        self.rabbitmq_channel = await self.rabbitmq_connection.channel()
        
        # Set QoS (prefetch count) to control how many messages we get at once
        await self.rabbitmq_channel.set_qos(prefetch_count=self.max_concurrent_runs)
        
        # Declare the queue
        self.rabbitmq_queue = await self.rabbitmq_channel.declare_queue(
            self.queue_name,
            durable=True
        )
        
        logger.info(f"Connected to queue: {self.queue_name}")
        logger.info(f"Queue has ~{self.rabbitmq_queue.declaration_result.message_count} messages")
        
    async def process_message(
        self,
        message: aio_pika.IncomingMessage,
        message_data: Dict[str, Any]
    ):
        """Process a single message using agentic-core processors."""
        async with message.process():
            try:
                # Extract message type and create appropriate message object
                message_type = message_data.get('message_type')
                
                if message_type == 'start_flow_execution':
                    await self._process_start_flow_execution(message_data)
                else:
                    logger.warning(f"Unknown message type: {message_type}")
                    
            except Exception as e:
                logger.error(f"Error processing message: {e}", exc_info=True)
                # In a real worker, you might want to reject and requeue or send to dead letter
                raise
    
    async def _process_start_flow_execution(self, message_data: Dict[str, Any]):
        """Process a StartFlowExecutionMessage."""
        # Parse message
        header_data = message_data['header']
        header = MessageHeader(
            correlation_id=header_data['correlation_id'],
            flow_execution_id=header_data['flow_execution_id'],
            tenant_id=header_data['tenant_id'],
            user_id=header_data['user_id'],
            debug=header_data.get('debug')
        )
        
        message = StartFlowExecutionMessage(
            message_type=message_data['message_type'],
            header=header,
            timestamp=message_data['timestamp'],
            flow_version_id=message_data['flow_version_id'],
            enable_debugging=message_data.get('enable_debugging', False)
        )
        
        logger.info(f"Processing StartFlowExecution for flow_execution_id: {header.flow_execution_id}")
        
        # Create execution context
        context = ExecutionContext(
            db=self.db_handler,
            header=header,
            messaging_backend=None  # Simplified: not sending further messages for now
        )
        
        # Get processor and execute
        processor = ProcessorFactory.get_processor('start_flow_execution')
        
        try:
            result_messages = await processor.execute(message, context)
            logger.info(f"StartFlowExecution completed for {header.flow_execution_id}")
            logger.info(f"Generated {len(result_messages) if result_messages else 0} follow-up messages")
            
            # In a real implementation, we would publish result_messages back to RabbitMQ
            # For now, we're just logging them
            if result_messages:
                logger.info("Note: Follow-up messages not published (simplified worker)")
                
        except Exception as e:
            logger.error(f"Processor execution failed: {e}", exc_info=True)
            raise
    
    async def handle_message(self, message: aio_pika.IncomingMessage):
        """Handle a message from the queue with semaphore control."""
        async with self.semaphore:
            try:
                # Parse message body
                body = message.body.decode('utf-8')
                
                # Handle both JSON and string-encoded dict formats
                try:
                    message_data = json.loads(body)
                except json.JSONDecodeError:
                    # Try to parse as Python dict string (if it was str(dict))
                    import ast
                    message_data = ast.literal_eval(body)
                
                logger.info(f"Received message type: {message_data.get('message_type')}")
                
                # Process the message
                await self.process_message(message, message_data)
                
            except Exception as e:
                logger.error(f"Error handling message: {e}", exc_info=True)
                # Reject and don't requeue - in production you'd want better error handling
                await message.reject(requeue=False)
    
    async def start_consuming(self):
        """Start consuming messages from the queue."""
        logger.info("Starting to consume messages...")
        logger.info(f"Processing up to {self.max_concurrent_runs} messages concurrently")
        logger.info("Press Ctrl+C to stop")
        logger.info("")
        
        # Start consuming
        async with self.rabbitmq_queue.iterator() as queue_iter:
            async for message in queue_iter:
                # Check if we should shutdown
                if self.shutdown_event.is_set():
                    break
                
                # Create task for message processing
                task = asyncio.create_task(self.handle_message(message))
                self.active_tasks.add(task)
                task.add_done_callback(self.active_tasks.discard)
    
    async def shutdown(self):
        """Graceful shutdown."""
        logger.info("Shutting down worker...")
        self.shutdown_event.set()
        
        # Wait for active tasks to complete
        if self.active_tasks:
            logger.info(f"Waiting for {len(self.active_tasks)} active tasks to complete...")
            await asyncio.gather(*self.active_tasks, return_exceptions=True)
        
        # Close RabbitMQ connections
        if self.rabbitmq_channel:
            await self.rabbitmq_channel.close()
        if self.rabbitmq_connection:
            await self.rabbitmq_connection.close()
        
        logger.info("Worker shutdown complete")


async def main():
    """Main entry point."""
    logger.info("=" * 80)
    logger.info("Workflow Run Processor Worker")
    logger.info("=" * 80)
    logger.info("")
    logger.info("This is a SIMPLIFIED worker for demonstration purposes.")
    logger.info("It processes StartFlowExecution messages but doesn't handle")
    logger.info("the full async engine message flow.")
    logger.info("")
    logger.info("For production use, run the full async-engine-worker service")
    logger.info("from agentic-core instead.")
    logger.info("=" * 80)
    logger.info("")
    
    # Create worker
    worker = RunProcessorWorker(
        max_concurrent_runs=3,  # Process 3 messages at a time
        queue_name="agentic.flyweight"
    )
    
    try:
        # Setup
        await worker.setup()
        
        # Start consuming (runs until interrupted)
        await worker.start_consuming()
        
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received")
    except Exception as e:
        logger.error(f"Error in main: {e}", exc_info=True)
        return 1
    finally:
        await worker.shutdown()
    
    return 0


if __name__ == "__main__":
    try:
        sys.exit(asyncio.run(main()))
    except KeyboardInterrupt:
        logger.info("Exiting...")
        sys.exit(0)

