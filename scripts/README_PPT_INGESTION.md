# PPT/PDF Ingestion CLI Tool

Process PowerPoint (PPTX) and PDF presentations with OCR and GPT-4 Vision.

## Quick Start

```bash
# Process a presentation
python scripts/process_presentation.py input.pptx

# Process a PDF
python scripts/process_presentation.py document.pdf

# Specify output location
python scripts/process_presentation.py input.pptx -o output.txt
```

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set your OpenAI API key:
```bash
export OPENAI_API_KEY="your-api-key-here"
```

Or add it to `.env`:
```
OPENAI_API_KEY=your-api-key-here
```

## Usage

### Basic Usage

```bash
python scripts/process_presentation.py <input_file>
```

Supported formats: `.pptx`, `.pdf`

### Options

- `-o, --output <path>` - Output file path (default: `<input>_processed.txt`)
- `-f, --format <text|json>` - Output format (default: `text`)
- `--no-vision` - Disable GPT-4 Vision (faster, OCR only)
- `--vision-prompt <prompt>` - Custom prompt for vision model
- `--api-key <key>` - OpenAI API key (if not in environment)

### Examples

**Export as JSON:**
```bash
python scripts/process_presentation.py data.pptx -o output.json -f json
```

**Fast OCR-only processing:**
```bash
python scripts/process_presentation.py large.pdf --no-vision
```

**Custom vision prompt:**
```bash
python scripts/process_presentation.py tech.pptx --vision-prompt "Focus on code and technical diagrams"
```

## Output Formats

### Text Format (default)

```
================================================================================
Slide/Page 1
================================================================================

[Slide content here]

================================================================================
Slide/Page 2
================================================================================

[Slide content here]
```

### JSON Format

```json
{
  "total_slides": 10,
  "slides": [
    {
      "page_number": 1,
      "ocr_text": "Original OCR text",
      "enhanced_text": "Enhanced text with vision",
      "has_image": true,
      "image_format": "png"
    }
  ]
}
```

## How It Works

1. **Extract Text**: Uses PyMuPDF to extract text via OCR
2. **Generate Images**: Converts each slide/page to high-res PNG
3. **Enhance with Vision** (optional): Sends text + image to GPT-4 Vision for:
   - Correcting OCR errors
   - Describing tables, charts, graphs
   - Understanding layout and structure

## Performance

- **OCR only** (`--no-vision`): ~0.5-1 sec per slide
- **With Vision**: ~3-5 sec per slide

For large files, use `--no-vision` for initial processing.

## Troubleshooting

### "OpenAI API key is required"
Set the API key: `export OPENAI_API_KEY="your-key"`

### "Unsupported file format"
Only `.pptx` and `.pdf` are supported. Convert other formats first.

### Poor OCR quality
Use vision enhancement: remove `--no-vision` flag

## See Also

- Full documentation: `docs/ppt-ingestion.md`
- Python examples: `examples/ppt_ingestion_example.py`
- API usage: See FastAPI docs at `http://localhost:8000/docs`

