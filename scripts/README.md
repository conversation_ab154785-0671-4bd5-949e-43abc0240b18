# Utility Scripts

This directory contains setup and utility scripts for Axon-PFC.

---

## Setup Scripts (Required)

### setup_system_user_entitlements.sh
**Purpose:** Configure system user permissions for all tenants  
**Status:** ✅ **REQUIRED** - Must run after database migration  
**When to use:** 
- During initial setup (after running database migrations)
- After creating new tenants
- When GraphQL log writer shows "Forbidden" errors

**Usage:**
```bash
./scripts/setup_system_user_entitlements.sh
```

**What it does:**
- Grants the system user (`system@internal`) access to all tenants
- Adds `admin` and `audit` entitlements for flow execution monitoring
- Enables GraphQL log writer to subscribe to execution streams

**Documentation:** See [SETUP.md](../SETUP.md#3-setup-system-user-entitlements)

---

## Utility Scripts (Legacy)

These scripts were used during development and testing. They are **no longer needed** for normal operation but are kept for reference.

### Legacy Scripts

### setup_test_workflow.py
**Purpose:** Creates test workflows in the database via API  
**Status:** Legacy - Use the UI instead (http://localhost:3000)

**Usage:**
```bash
python scripts/setup_test_workflow.py
```

---

### delete_test_workflow.py
**Purpose:** Deletes test workflows from the database  
**Status:** Legacy - Use the UI instead

**Usage:**
```bash
python scripts/delete_test_workflow.py <workflow-id>
```

---

### run_workflow_parallel.py
**Purpose:** Tests parallel workflow execution by creating multiple flow_execution records  
**Status:** Legacy - Use the Evaluation System instead (Test Cases)

**Usage:**
```bash
python scripts/run_workflow_parallel.py --workflow-id <id> --count 5
```

---

### run_processor_worker.py
**Purpose:** Custom worker that processes workflow runs from RabbitMQ  
**Status:** Legacy - Replaced by agentic-core's Docker workers

**Note:** This was a simplified worker for testing. Production uses:
- `async-engine-async` (Docker container)
- `async-engine-flyweight` (Docker container)

---

### process_runs.py
**Purpose:** Process and analyze workflow runs  
**Status:** Legacy - Dashboard provides this functionality

---

## Why These Are Legacy

### Before
During development, these scripts were needed to:
- Manually create test workflows
- Simulate parallel execution
- Run custom workers
- Process results

### Now
The system has:
- **UI** for workflow management (create, edit, delete)
- **Evaluation System** for testing (test cases, batch runs)
- **Docker Workers** for execution (agentic-core)
- **Dashboard** for monitoring and results

---

## If You Need Them

These scripts can still be useful for:
- **Debugging** - Direct database/API manipulation
- **Bulk Operations** - Creating many workflows at once
- **Testing** - Stress testing the system
- **Development** - Prototyping new features

---

## Dependencies

These scripts may require additional setup:
```bash
# Ensure agentic-core path is set
export AGENTIC_CORE_PATH=/path/to/agentic-core

# Ensure .env is configured
cd ..
source venv/bin/activate
```

---

**Recommendation:** Use the standard Axon-PFC UI and evaluation system for normal operations. Only use these scripts if you need direct programmatic access to the system.

