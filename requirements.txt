# Core dependencies for Axon-PFC
pydantic>=2.0.0
pyyaml>=6.0
requests>=2.31.0
python-dotenv>=1.0.0
rich>=13.0.0

# FastAPI and web server
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# Database support (for future phases)
sqlalchemy>=2.0
psycopg2-binary>=2.9.0
alembic>=1.13.0
alembic-postgresql-enum>=1.0.0
asyncpg>=0.29.0
greenlet>=3.0.0

# RabbitMQ and Celery support
aio-pika>=9.0.0
celery>=5.5.0
kombu>=5.4.0

# AI/LLM support
openai>=1.52.0  # GPT-5 support requires latest version
openai-agents>=0.1.0  # OpenAI Agents SDK for multi-agent orchestration

# Document processing
pymupdf>=1.23.0
python-pptx>=0.6.21
python-docx>=1.1.0
Pillow>=10.0.0
pytesseract>=0.3.10

# Development dependencies
pytest>=7.4.0
pytest-asyncio>=0.21.0

